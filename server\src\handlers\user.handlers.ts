import { Request, Response } from 'express';
import { getUserById, getUserByEmail, updateUser, createUser } from '../services/user.service';

/**
 * Functional User Handlers
 * Pure functions for handling user-related HTTP requests
 */

/**
 * Get user by ID
 * GET /api/users/:userId
 */
export const handleGetUser = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;
        
        console.log('🔍 GET /api/users/:userId - Request for user:', userId);

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        const user = await getUserById(userId);

        if (!user) {
            res.status(404).json({
                success: false,
                error: 'User not found'
            });
            return;
        }

        console.log('✅ User retrieved successfully:', user.id);
        res.json({
            success: true,
            data: user
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetUser:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch user',
            details: error.message
        });
    }
};

/**
 * Get user by email
 * GET /api/users/email/:email
 */
export const handleGetUserByEmail = async (req: Request, res: Response): Promise<void> => {
    try {
        const { email } = req.params;
        
        console.log('🔍 GET /api/users/email/:email - Request for email:', email);

        if (!email) {
            res.status(400).json({
                success: false,
                error: 'Email is required'
            });
            return;
        }

        const user = await getUserByEmail(email);

        if (!user) {
            res.status(404).json({
                success: false,
                error: 'User not found'
            });
            return;
        }

        console.log('✅ User retrieved by email successfully:', user.id);
        res.json({
            success: true,
            data: user
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetUserByEmail:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch user by email',
            details: error.message
        });
    }
};

/**
 * Update user
 * PUT /api/users/:userId
 */
export const handleUpdateUser = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;
        const updates = req.body;
        
        console.log('🔄 PUT /api/users/:userId - Update request for user:', userId);

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        const updatedUser = await updateUser(userId, updates);

        console.log('✅ User updated successfully:', updatedUser.id);
        res.json({
            success: true,
            data: updatedUser
        });
    } catch (error: any) {
        console.error('❌ Error in handleUpdateUser:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update user',
            details: error.message
        });
    }
};

/**
 * Create user
 * POST /api/users
 */
export const handleCreateUser = async (req: Request, res: Response): Promise<void> => {
    try {
        const userData = req.body;
        
        console.log('🔄 POST /api/users - Create user request for email:', userData.email);

        if (!userData.email) {
            res.status(400).json({
                success: false,
                error: 'Email is required'
            });
            return;
        }

        const newUser = await createUser(userData);

        console.log('✅ User created successfully:', newUser.id);
        res.status(201).json({
            success: true,
            data: newUser
        });
    } catch (error: any) {
        console.error('❌ Error in handleCreateUser:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create user',
            details: error.message
        });
    }
};

/**
 * Get user's LinkedIn account ID
 * GET /api/users/:userId/linkedin-account-id
 */
export const handleGetUserLinkedInAccountId = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;
        
        console.log('🔍 GET /api/users/:userId/linkedin-account-id - Request for user:', userId);

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        const user = await getUserById(userId);

        if (!user) {
            res.status(404).json({
                success: false,
                error: 'User not found'
            });
            return;
        }

        console.log('✅ LinkedIn account ID retrieved:', user.account_id);
        res.json({
            success: true,
            data: {
                userId: user.id,
                linkedinAccountId: user.account_id,
                connectionStatus: user.linkedin_connection_status
            }
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetUserLinkedInAccountId:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get LinkedIn account ID',
            details: error.message
        });
    }
};
