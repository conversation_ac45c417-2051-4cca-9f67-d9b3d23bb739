import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { X, Refresh<PERSON><PERSON>, Linkedin, Shield, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/context/AuthContext';
import { analyticsService } from '@/services/analytics';
import { getApiUrl } from '@/config/env';

interface LinkedInReconnectPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (accountId: string) => void;
  existingAccountId?: string;
  existingAccountName?: string;
}

const LinkedInReconnectPopup: React.FC<LinkedInReconnectPopupProps> = ({
  isOpen,
  onClose,
  onSuccess,
  existingAccountId,
  existingAccountName
}) => {
  const { user } = useAuth();
  const [isReconnecting, setIsReconnecting] = useState(false);

  useEffect(() => {
    if (isOpen && user?.id) {
      analyticsService.setUserId(user.id);
      analyticsService.trackUserAction('linkedin_reconnect_popup_shown', {
        context: 'dashboard_reconnect'
      });
    }
  }, [isOpen, user]);

  const handleReconnect = async () => {
    if (!user?.email) {
      toast.error('User session not found. Please try logging in again.');
      return;
    }

    setIsReconnecting(true);

    try {
      console.log('🔄 Starting LinkedIn reconnection process...');

      // Track reconnection attempt
      await analyticsService.trackUserAction('linkedin_reconnect_attempt', {
        existingAccountId,
        userEmail: user.email
      });

      let accountIdToReconnect = existingAccountId;

      // If no account ID provided, check for existing account
      if (!accountIdToReconnect) {
        console.log('🔍 No account ID provided, checking for existing account...');
        
        const checkResponse = await fetch(getApiUrl('/auth/get-account-by-email'), {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: user.email }),
        });

        const checkData = await checkResponse.json();

        if (checkResponse.ok && checkData.success && checkData.account_id) {
          accountIdToReconnect = checkData.account_id;
          console.log('✅ Found existing account:', accountIdToReconnect);
        } else {
          throw new Error('No existing LinkedIn account found for reconnection');
        }
      }

      // Call reconnect endpoint
      console.log('🚀 Calling reconnect endpoint with account ID:', accountIdToReconnect);
      
      const reconnectResponse = await fetch(getApiUrl('/auth/hosted-auth/reconnect'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userEmail: user.email, // Backend expects 'userEmail', not 'userId'
          accountId: accountIdToReconnect,
          type: 'reconnect'
        }),
      });

      if (!reconnectResponse.ok) {
        const errorData = await reconnectResponse.json();
        throw new Error(errorData.details || `HTTP error! status: ${reconnectResponse.status}`);
      }

      const reconnectData = await reconnectResponse.json();
      console.log('✅ Backend reconnect response:', reconnectData);

      // Extract the URL from the backend response structure
      const reconnectUrl = reconnectData.data?.url || reconnectData.url;

      if (!reconnectUrl) {
        throw new Error('No reconnect URL received from backend');
      }

      console.log('✅ Reconnect URL generated:', reconnectUrl);
      
      // Track successful reconnect URL generation
      await analyticsService.trackUserAction('linkedin_reconnect_url_generated', {
        accountId: accountIdToReconnect
      });

      toast.success('Redirecting to LinkedIn reconnection...');

      // Redirect to Unipile reconnect page
      window.location.href = reconnectUrl;

    } catch (error: any) {
      console.error('❌ Error during reconnection:', error);
      toast.error(`Failed to reconnect: ${error.message}`);
      setIsReconnecting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md bg-white rounded-xl shadow-2xl">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <RefreshCw className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">Reconnect Your LinkedIn</h2>
                <p className="text-sm text-gray-600">Refresh your LinkedIn connection</p>
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              disabled={isReconnecting}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Account Info */}
          {existingAccountName && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-3">
                <Linkedin className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-blue-900">Current Account</p>
                  <p className="text-sm text-blue-700">{existingAccountName}</p>
                  <p className="text-xs text-blue-600">{user?.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Reconnection Info */}
          <div className="mb-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-900">Why Reconnect?</p>
                <p className="text-sm text-yellow-700 mt-1">
                  Your LinkedIn connection may have expired or needs refreshing. 
                  Reconnecting will restore access to your LinkedIn data and analytics.
                </p>
              </div>
            </div>
          </div>

          {/* Reconnect Button */}
          <Button
            onClick={handleReconnect}
            disabled={isReconnecting}
            className="w-full bg-green-600 hover:bg-green-700 text-white py-3"
          >
            {isReconnecting ? (
              <div className="flex items-center">
                <RefreshCw className="animate-spin h-4 w-4 mr-2" />
                Reconnecting...
              </div>
            ) : (
              <div className="flex items-center">
                <Linkedin className="h-4 w-4 mr-2" />
                Reconnect Your Account
              </div>
            )}
          </Button>

          {/* Cancel Button */}
          <Button
            onClick={onClose}
            variant="outline"
            disabled={isReconnecting}
            className="w-full mt-3"
          >
            Cancel
          </Button>

          {/* Security Notice */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-gray-600" />
              <p className="text-xs text-gray-600">
                🔒 Secure reconnection through Unipile's official authentication system
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinkedInReconnectPopup;
