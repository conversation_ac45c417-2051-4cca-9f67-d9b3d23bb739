import { useState } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { useNavigate } from 'react-router-dom';
import { toast } from "sonner";
import { supabase } from '@/integrations/supabase/client';
import axios from 'axios';
import { getApiUrl } from '@/config/env';

export const useAuthState = () => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [linkedinVerificationRequired, setLinkedinVerificationRequired] = useState(false);
  const [checkpointData, setCheckpointData] = useState<any>(null); // holds checkpoint info if 2FA or captcha needed

  const navigate = useNavigate();

  const signUp = async (signupData: {
    email: string;
    password: string;
    options?: { data?: Record<string, any> }
  }) => {
    try {
      setIsLoading(true);

      // Clear stale session 
      const isLinkedInOAuth = window.location.pathname.includes('/auth/') ||
                             window.location.pathname.includes('/callback') ||
                             window.location.search.includes('accountId') ||
                             window.location.href.includes('unipile');

      if (!isLinkedInOAuth) {
        await supabase.auth.signOut();
        localStorage.removeItem('supabase.auth.token');
      }

      // Sign up to Supabase Auth
      // Note: LinkedIn password is never stored in Supabase for security
      const { data, error } = await supabase.auth.signUp({
        email: signupData.email,
        password: signupData.password,
        options: {
          ...signupData.options,
          // Redirect to custom confirmation page
           emailRedirectTo: `${import.meta.env.VITE_CLIENT_URL}/login`,
          data: {
            ...signupData.options?.data,
            // Ensure LinkedIn password is never included in metadata
            linkedin_password: undefined
          }
        }
      });

      if (error) {
        if (error.message.includes('already registered')) {
          toast.error("This email is already registered. Try logging in.");
          navigate('/login');
          return { error };
        }
        toast.error(error.message);
        return { error };
      }

      // If Supabase auth user was created successfully, create the profile in Supabase
      if (data.user) {
        console.log('✅ Supabase auth user created, now creating profile in Supabase...');

        try {
          // Create profile directly in Supabase (frontend handles all database operations)
          const { error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: data.user.id, // Use the Supabase auth user ID
              email: signupData.email,
              first_name: signupData.options?.data?.firstName,
              last_name: signupData.options?.data?.lastName,
              linkedin_connection_status: 'not_connected',
              role: 'user',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (profileError) {
            console.error('❌ Failed to create user profile:', profileError);
          } else {
            console.log('✅ User profile created successfully in Supabase');
          }
        } catch (profileError: any) {
          console.error('❌ Error creating user profile:', profileError);
          // Don't fail the signup if profile creation fails - user can still use the app
        }
      }

      toast.success("Sign-up successful! Please verify your email.");
      navigate('/login');
      return { data };
    } catch (error: any) {
      console.error('Sign-up error:', error);
      toast.error("There was an error during sign-up. Please try again.");
      return { error };
    } finally {
      setIsLoading(false);
    }
  };

  const verifyCheckpoint = async (checkpointPayload: any) => {
    try {
      setIsLoading(true);
      const response = await axios.post(getApiUrl('/auth/solve-checkpoint'), {
        ...checkpointPayload,
        checkpointData
      });

      if (response.data.success) {
        toast.success("LinkedIn verification successful!");

        // After checkpoint passes, resume flow (you might want to re-trigger signUp with stored state)
        setLinkedinVerificationRequired(false);
        setCheckpointData(null);
      } else {
        toast.error("Verification failed. Please try again.");
      }
    } catch (err) {
      toast.error("Checkpoint verification failed.");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        // Return error instead of showing toast here
        return { error };
      }

      if (data.session) {
        // Return success instead of showing toast here
        navigate('/user-dashboard');
        return { data, error: null };
      }

      return { data, error: null };
    } catch (error: any) {
      // Return caught error instead of showing toast here
      return { error: { message: error.message || "Login failed." } };
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      await supabase.auth.signOut();
      localStorage.removeItem('supabase.auth.token');
      setSession(null);
      setUser(null);
      navigate('/login');
      toast.success("Logged out successfully.");
    } catch (error) {
      toast.error("Logout failed.");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    session,
    setSession,
    user,
    setUser,
    isLoading,
    setIsLoading,
    signUp,
    signIn,
    signOut,
    verifyCheckpoint,
    linkedinVerificationRequired,
    checkpointData
  };
};



