import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2, CheckCircle, AlertCircle, Wifi, WifiOff, Clock } from 'lucide-react';

export type SyncStatus = 'idle' | 'syncing' | 'success' | 'error' | 'offline' | 'scheduled';

interface SyncStatusIndicatorProps {
  status: SyncStatus;
  message?: string;
  lastSyncTime?: Date;
  nextSyncTime?: Date;
  className?: string;
  compact?: boolean;
}

export const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({
  status,
  message,
  lastSyncTime,
  nextSyncTime,
  className = '',
  compact = false
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'syncing':
        return {
          icon: Loader2,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          text: message || 'Syncing...',
          animate: 'animate-spin'
        };
      
      case 'success':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          text: message || 'Synced',
          animate: ''
        };
      
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          text: message || 'Sync failed',
          animate: 'animate-pulse'
        };
      
      case 'offline':
        return {
          icon: WifiOff,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          text: message || 'Offline',
          animate: ''
        };
      
      case 'scheduled':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          text: message || 'Scheduled',
          animate: 'animate-pulse'
        };
      
      default: // idle
        return {
          icon: Wifi,
          color: 'text-gray-400',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          text: message || 'Ready',
          animate: ''
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  };

  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  if (compact) {
    return (
      <div className={cn(
        'inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border',
        config.bgColor,
        config.borderColor,
        config.color,
        className
      )}>
        <Icon className={cn('w-3 h-3', config.animate)} />
        <span>{config.text}</span>
      </div>
    );
  }

  return (
    <div className={cn(
      'flex items-center justify-between p-3 rounded-lg border',
      config.bgColor,
      config.borderColor,
      className
    )}>
      <div className="flex items-center space-x-3">
        <Icon className={cn('w-5 h-5', config.color, config.animate)} />
        <div>
          <div className={cn('font-medium text-sm', config.color)}>
            {config.text}
          </div>
          {lastSyncTime && (
            <div className="text-xs text-gray-500">
              Last sync: {formatRelativeTime(lastSyncTime)}
            </div>
          )}
        </div>
      </div>
      
      {nextSyncTime && status !== 'syncing' && (
        <div className="text-xs text-gray-500">
          Next: {formatTime(nextSyncTime)}
        </div>
      )}
    </div>
  );
};

// Real-time sync status hook
export const useSyncStatus = () => {
  const [status, setStatus] = React.useState<SyncStatus>('idle');
  const [message, setMessage] = React.useState<string>('');
  const [lastSyncTime, setLastSyncTime] = React.useState<Date | undefined>();
  const [nextSyncTime, setNextSyncTime] = React.useState<Date | undefined>();

  const updateStatus = (newStatus: SyncStatus, newMessage?: string) => {
    setStatus(newStatus);
    if (newMessage) setMessage(newMessage);
    
    if (newStatus === 'success') {
      setLastSyncTime(new Date());
      // Schedule next sync in 15 minutes (background scheduler interval)
      setNextSyncTime(new Date(Date.now() + 15 * 60 * 1000));
    }
  };

  const startSync = (message?: string) => updateStatus('syncing', message);
  const syncSuccess = (message?: string) => updateStatus('success', message);
  const syncError = (message?: string) => updateStatus('error', message);
  const goOffline = () => updateStatus('offline', 'Connection lost');
  const goOnline = () => updateStatus('idle', 'Connected');

  return {
    status,
    message,
    lastSyncTime,
    nextSyncTime,
    updateStatus,
    startSync,
    syncSuccess,
    syncError,
    goOffline,
    goOnline
  };
};
