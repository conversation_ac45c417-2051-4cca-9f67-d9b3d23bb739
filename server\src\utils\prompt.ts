import readline from 'readline';

export const promptUserForCode = (checkpointType: string): Promise<string> => {
    return new Promise((resolve) => {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
        });

        rl.question(`Enter the code for checkpoint type "${checkpointType}": `, (code) => {
            rl.close();
            resolve(code.trim());
        });
    });
};
