
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const NotificationsTab = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Notification Preferences</CardTitle>
          <CardDescription>
            Configure how and when you receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Email Notifications</h3>
            
            <div className="space-y-3">
              {[
                {
                  id: "email-content-approval",
                  label: "Content approval requests",
                  defaultChecked: true
                },
                {
                  id: "email-content-published",
                  label: "Content published",
                  defaultChecked: true
                },
                {
                  id: "email-user-activity",
                  label: "User activity reports",
                  defaultChecked: false
                },
                {
                  id: "email-performance-reports",
                  label: "Weekly performance reports",
                  defaultChecked: true
                }
              ].map((item) => (
                <div key={item.id} className="flex items-center space-x-2">
                  <Switch id={item.id} defaultChecked={item.defaultChecked} />
                  <Label htmlFor={item.id}>{item.label}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="text-sm font-medium">In-App Notifications</h3>
            
            <div className="space-y-3">
              {[
                {
                  id: "app-content-approval",
                  label: "Content approval requests",
                  defaultChecked: true
                },
                {
                  id: "app-content-comments",
                  label: "Comments on content",
                  defaultChecked: true
                },
                {
                  id: "app-mentions",
                  label: "Mentions and tags",
                  defaultChecked: true
                },
                {
                  id: "app-system-updates",
                  label: "System updates and announcements",
                  defaultChecked: false
                }
              ].map((item) => (
                <div key={item.id} className="flex items-center space-x-2">
                  <Switch id={item.id} defaultChecked={item.defaultChecked} />
                  <Label htmlFor={item.id}>{item.label}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="summary-frequency">Email Summary Frequency</Label>
            <Select defaultValue="daily">
              <SelectTrigger id="summary-frequency">
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="realtime">Real-time</SelectItem>
                <SelectItem value="daily">Daily digest</SelectItem>
                <SelectItem value="weekly">Weekly summary</SelectItem>
                <SelectItem value="never">Never</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button className="bg-enterprise-blue hover:bg-enterprise-blue/90">
            Save Notification Preferences
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default NotificationsTab;
