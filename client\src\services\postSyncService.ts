// Phase 2: Frontend Post Sync Service
// Client-side service for LinkedIn post synchronization

import { getApiUrl } from '@/config/env';

export interface SyncRequest {
    userId: string;
    accountId?: string;
    userIdentifier?: string;
    department?: string;
    limit?: number;
    fullSync?: boolean;
}

export interface SyncResult {
    success: boolean;
    operation: {
        type: 'full_sync' | 'incremental_sync';
        startedAt: string;
        completedAt: string;
        durationMs: number;
    };
    posts: {
        fetched: number;
        stored: number;
        updated: number;
        failed: number;
        skipped: number;
    };
    apiCalls: {
        made: number;
        rateLimitHit: boolean;
    };
    errors: string[];
    lastSyncedPostDate?: string;
}

export interface SyncStatus {
    totalPosts: number;
    lastSyncAt?: string;
    newPosts: number;
    updatedPosts: number;
}

export interface PostData {
    id: string;
    user_id: string;
    linkedin_post_id: string;
    linkedin_post_url?: string;
    title: string;
    content: string;
    post_type: string;
    department: string;
    impressions: number;
    likes: number;
    comments: number;
    shares: number;
    engagement_rate: number;
    post_date: string;
    last_synced_at: string;
    is_newly_added: boolean;
    is_updated: boolean;
    sync_status: string;
    status: string;
    attachments_links?: string[]; // Array of attachment URLs
}

export interface PostsResponse {
    posts: PostData[];
    pagination: {
        total: number;
        limit: number;
        offset: number;
        hasMore: boolean;
    };
    filters: {
        newOnly: boolean;
    };
    refresh?: {
        triggered: boolean;
        result?: {
            type: string;
            postsUpdated: number;
            success: boolean;
            nextRefreshAt: Date;
        };
        dataAge?: {
            minutes: number;
            status: 'fresh' | 'stale' | 'old';
        };
    };
}

export interface PostAnalytics {
    totalPosts: number;
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    totalImpressions: number;
    averageEngagementRate: number;
    topPerformingPost: PostData | null;
    postsByType: Record<string, number>;
}

/**
 * Post Sync Service Class
 */
export class PostSyncService {
    private baseUrl: string;

    constructor() {
        this.baseUrl = getApiUrl('/api/posts');
    }

    /**
     * Check if server is reachable
     */
    async checkServerHealth(): Promise<boolean> {
        try {
            const response = await fetch(`${getApiUrl('/health')}`, {
                method: 'GET',
                signal: AbortSignal.timeout(5000) // 5 second timeout
            });
            return response.ok;
        } catch (error) {
            console.error('❌ Server health check failed:', error);
            return false;
        }
    }

    /**
     * Trigger LinkedIn post sync for a user
     */
    async syncPosts(request: SyncRequest): Promise<{
        success: boolean;
        data?: { sync: SyncResult; responseTime: number };
        error?: string;
    }> {
        try {
            console.log('🚀 Triggering post sync:', request);

            // Add timeout to prevent hanging requests
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

            const response = await fetch(`${this.baseUrl}/sync`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(request),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `HTTP error! status: ${response.status}`);
            }

            console.log('✅ Post sync completed:', result.data?.sync?.posts);
            return result;

        } catch (error: any) {
            console.error('❌ Error syncing posts:', error);

            // Check if it's a network error that we should retry
            const isNetworkError = error.name === 'TypeError' && error.message.includes('fetch');
            const isTimeoutError = error.name === 'AbortError';

            if (isNetworkError) {
                console.log('🌐 Network error detected - this is usually temporary');
                console.log('💡 Suggestion: Check if server is running and try refreshing the page');
            }

            return {
                success: false,
                error: error.message || 'Network connection failed'
            };
        }
    }

    /**
     * Get sync status for a user
     */
    async getSyncStatus(userId: string): Promise<{
        success: boolean;
        data?: SyncStatus;
        error?: string;
    }> {
        try {
            console.log('📊 Getting sync status for user:', userId);

            const response = await fetch(`${this.baseUrl}/sync/status/${userId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `HTTP error! status: ${response.status}`);
            }

            console.log('✅ Sync status retrieved:', result.data);
            return result;

        } catch (error: any) {
            console.error('❌ Error getting sync status:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get stored posts for a user
     */
    async getUserPosts(
        userId: string,
        options: {
            limit?: number;
            offset?: number;
            newOnly?: boolean;
            skipRefresh?: boolean;
        } = {}
    ): Promise<{
        success: boolean;
        data?: PostsResponse;
        error?: string;
    }> {
        try {
            const { limit = 50, offset = 0, newOnly = false, skipRefresh = false } = options;

            console.log('📋 Getting user posts:', { userId, limit, offset, newOnly, skipRefresh });

            const params = new URLSearchParams({
                limit: limit.toString(),
                offset: offset.toString(),
                newOnly: newOnly.toString(),
                skipRefresh: skipRefresh.toString()
            });

            const response = await fetch(`${this.baseUrl}/list/${userId}?${params}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `HTTP error! status: ${response.status}`);
            }

            console.log(`✅ Retrieved ${result.data?.posts?.length || 0} posts`);
            return result;

        } catch (error: any) {
            console.error('❌ Error getting user posts:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Mark posts as viewed
     */
    async markPostsViewed(
        userId: string,
        postIds?: string[]
    ): Promise<{
        success: boolean;
        data?: { message: string; userId: string; postsUpdated: number };
        error?: string;
    }> {
        try {
            console.log('👁️ Marking posts as viewed:', { userId, postCount: postIds?.length || 'all' });

            const response = await fetch(`${this.baseUrl}/mark-viewed`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userId, postIds }),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `HTTP error! status: ${response.status}`);
            }

            console.log('✅ Posts marked as viewed');
            return result;

        } catch (error: any) {
            console.error('❌ Error marking posts as viewed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get post analytics for a user
     */
    async getPostAnalytics(
        userId: string,
        days: number = 30
    ): Promise<{
        success: boolean;
        data?: {
            analytics: PostAnalytics;
            period: { days: number; from: string; to: string };
        };
        error?: string;
    }> {
        try {
            console.log('📈 Getting post analytics:', { userId, days });

            const response = await fetch(`${this.baseUrl}/analytics/${userId}?days=${days}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `HTTP error! status: ${response.status}`);
            }

            console.log('✅ Analytics retrieved:', result.data?.analytics);
            return result;

        } catch (error: any) {
            console.error('❌ Error getting post analytics:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Perform incremental sync (fetch only recent posts)
     */
    async incrementalSync(userId: string, 
        accountId?: string,
        userIdentifier?: string,
        department?: string,
    ): Promise<{
        success: boolean;
        data?: { sync: SyncResult; responseTime: number };
        error?: string;
    }> {
        return this.syncPosts({
            userId,
            ...(accountId ? { accountId } : {}),
            ...(userIdentifier ? { userIdentifier } : {}),
            ...(department ? { department } : {}),
            limit: 20, // Smaller limit for incremental
            fullSync: false
        });
    }

    /**
     * Perform full sync (fetch all available posts)
     */
    async fullSync(userId: string, accountId: string, userIdentifier: string): Promise<{
        success: boolean;
        data?: { sync: SyncResult; responseTime: number };
        error?: string;
    }> {
        return this.syncPosts({
            userId,
            accountId,
            userIdentifier,
            limit: 100, // Larger limit for full sync
            fullSync: true
        });
    }
}

// Export singleton instance
export const postSyncService = new PostSyncService();
