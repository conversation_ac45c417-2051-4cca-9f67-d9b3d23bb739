import { Router } from 'express';
import { ValidationMiddleware } from '../middleware/validation';
import {
  handleUnipileWebhook,
  handleCreateHostedAuth,
  handleCreateReconnectAuth,
  handleGetAccountByEmail,
  handleVerifyHostedAuthAccount,
  handleCheckAccountStatus,
  handleGetLatestLinkedInAccount,
  handleSaveLinkedInDataManually,
  handleGetLinkedInProfileForFrontend,
  handleReconnectLinkedInAccount,
  handleCompleteSignup
} from '../handlers/auth.handlers';

const router = Router();

// Complete user signup after LinkedIn verification
router.post('/complete-signup',
  ValidationMiddleware.validateSignupComplete,
  handleCompleteSignup
);

// Official Unipile webhook endpoint
router.post('/webhook/unipile-auth', (req, _res, next) => {
  console.log('🎯 WEBHOOK ENDPOINT HIT - /auth/webhook/unipile-auth');
  console.log('🎯 WEBHOOK URL CONFIGURED:', process.env.WEBHOOK_BASE_URL || process.env.SERVER_URL || 'http://localhost:3000');
  console.log('🎯 WEBHOOK REQUEST METHOD:', req.method);
  console.log('🎯 WEBHOOK REQUEST HEADERS:', JSON.stringify(req.headers, null, 2));
  console.log('🎯 WEBHOOK REQUEST BODY:', JSON.stringify(req.body, null, 2));
  next();
}, handleUnipileWebhook);

// Test webhook endpoint (for debugging)
router.post('/webhook/test', (req, res) => {
  console.log('🧪 TEST WEBHOOK RECEIVED:', JSON.stringify(req.body, null, 2));
  console.log('🧪 TEST WEBHOOK HEADERS:', JSON.stringify(req.headers, null, 2));
  res.status(200).json({ success: true, message: 'Test webhook received' });
});



// Official Unipile Hosted Auth Wizard Routes
console.log('🔧 Registering route: POST /auth/hosted-auth/create');
router.post('/hosted-auth/create',
  handleCreateHostedAuth
);

console.log('🔧 Registering route: POST /auth/hosted-auth/reconnect');
router.post('/hosted-auth/reconnect',
  handleCreateReconnectAuth
);

// Get account by email (immediate check)
router.post('/get-account-by-email',
  handleGetAccountByEmail
);

// Verify hosted auth account creation
router.post('/verify-hosted-auth-account',
  handleVerifyHostedAuthAccount
);

// Check account status for IN_APP_VALIDATION polling
router.get('/account-status/:accountId',
  handleCheckAccountStatus
);

// Save LinkedIn data after successful verification
router.post('/save-linkedin-data',
  handleSaveLinkedInDataManually
);

// Get LinkedIn profile for frontend fallback
router.get('/get-linkedin-profile/:accountId',
  handleGetLinkedInProfileForFrontend
);

// Reconnect existing LinkedIn account
router.post('/hosted-auth/reconnect',
  handleReconnectLinkedInAccount
);

// Get latest LinkedIn account (for webhook completion check)
router.get('/get-latest-linkedin-account',
  handleGetLatestLinkedInAccount
);

// Alternative route for frontend compatibility
router.get('/linkedin/latest',
  handleGetLatestLinkedInAccount
);

export default router;
