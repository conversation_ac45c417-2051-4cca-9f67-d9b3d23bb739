import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface EnvironmentConfig {
  // Environment
  NODE_ENV: 'development' | 'production' | 'staging';
  PORT: number;

  // URLs
  SERVER_URL: string;
  CLIENT_URL: string;
  WEBHOOK_BASE_URL: string;

  // Unipile Configuration
  UNIPILE_API_URL: string;
  UNIPILE_API_ACCESS_TOKEN: string;

  // Environment Detection
  isLive: boolean;
  isLocal: boolean;
  isRender: boolean;
}

/**
 * Determine the current environment
 */
function getEnvironment(): 'development' | 'production' | 'staging' {
  const env = process.env.NODE_ENV?.toLowerCase();
  if (env === 'production') return 'production';
  if (env === 'staging') return 'staging';
  return 'development';
}

/**
 * Detect if we're running on Render (live environment)
 */
function isRunningOnRender(): boolean {
  return !!(process.env.RENDER ||
    process.env.RENDER_SERVICE_ID ||
    process.env.RENDER_EXTERNAL_URL ||
    (process.env.SERVER_URL && process.env.SERVER_URL.includes('onrender.com')));
}

/**
 * Detect if we're running locally
 */
function isRunningLocally(): boolean {
  return !!(process.env.SERVER_URL &&
    (process.env.SERVER_URL.includes('localhost') ||
      process.env.SERVER_URL.includes('127.0.0.1'))) ||
    !process.env.SERVER_URL;
}

/**
 * Get server URL based on environment
 */
function getServerUrl(): string {
  const env = getEnvironment();

  // Explicit server URL
  if (process.env.SERVER_URL) {
    return process.env.SERVER_URL;
  }

  // Production: Use Render URL
  if (env === 'production') {
    return 'https://whisper-voice-server.onrender.com';
  }

  // Development: Use localhost
  return 'http://localhost:3000';
}

/**
 * Get client URL based on environment
 */
function getClientUrl(): string {
  const env = getEnvironment();

  // Explicit client URL
  if (process.env.CLIENT_URL) {
    return process.env.CLIENT_URL;
  }
  // Development: Use localhost
  return process.env.CLIENT_URL || 'https://localhost:8080';
}

/**
 * Get webhook base URL with intelligent live/local detection
 */
function getWebhookBaseUrl(): string {
  const env = getEnvironment();

  // Explicit webhook URL (highest priority)
  if (process.env.WEBHOOK_BASE_URL) {
    console.log('🔗 Using explicit WEBHOOK_BASE_URL:', process.env.WEBHOOK_BASE_URL);
    return process.env.WEBHOOK_BASE_URL;
  }

  // Auto-detect environment
  if (isRunningOnRender()) {
    console.log('🌍 Detected Render environment - using live webhook URL');
    return process.env.SERVER_URL || 'https://whisper-voice-server.onrender.com';
  }

  // Check for ngrok URL (development with webhook testing)
  if (process.env.NGROK_URL) {
    console.log('🔗 Detected ngrok URL - using for webhook testing:', process.env.NGROK_URL);
    return process.env.NGROK_URL;
  }

  // Local development fallback
  if (isRunningLocally()) {
    console.log('🏠 Detected local environment - using localhost (webhooks may not work)');
    return process.env.SERVER_URL || 'http://localhost:3000';
  }

  // Production fallback
  if (env === 'production') {
    console.log('🚀 Production environment - using Render URL');
    return 'https://whisper-voice-server.onrender.com';
  }

  // Final fallback
  console.log('⚠️ Using fallback webhook URL');
  return process.env.SERVER_URL || 'http://localhost:3000';
}

/**
 * Validate required environment variables
 */
function validateEnvironment(config: EnvironmentConfig): void {
  const errors: string[] = [];

  // Required for all environments
  if (!config.UNIPILE_API_ACCESS_TOKEN) {
    errors.push('UNIPILE_API_ACCESS_TOKEN is required');
  }

  if (!config.UNIPILE_API_URL) {
    errors.push('UNIPILE_API_URL is required');
  }

  if (errors.length > 0) {
    console.error('❌ Environment validation errors:');
    errors.forEach(error => console.error(`  - ${error}`));
    throw new Error(`Environment validation failed: ${errors.join(', ')}`);
  }
}

/**
 * Create and validate environment configuration
 */
export function createEnvironmentConfig(): EnvironmentConfig {
  const config: EnvironmentConfig = {
    NODE_ENV: getEnvironment(),
    PORT: parseInt(process.env.PORT || '3000', 10),

    // URLs
    SERVER_URL: getServerUrl(),
    CLIENT_URL: getClientUrl(),
    WEBHOOK_BASE_URL: getWebhookBaseUrl(),

    // Unipile
    UNIPILE_API_URL: process.env.UNIPILE_API_URL || 'https://api2.unipile.com:13290',
    UNIPILE_API_ACCESS_TOKEN: process.env.UNIPILE_API_ACCESS_TOKEN || '',

    // Environment Detection
    isLive: isRunningOnRender(),
    isLocal: isRunningLocally(),
    isRender: isRunningOnRender(),
  };

  // Validate configuration
  validateEnvironment(config);

  // Log configuration (without sensitive data)
  console.log('\n🔧 Environment Configuration:');
  console.log(`  Environment: ${config.NODE_ENV}`);
  console.log(`  Port: ${config.PORT}`);
  console.log(`  Server URL: ${config.SERVER_URL}`);
  console.log(`  Client URL: ${config.CLIENT_URL}`);
  console.log(`  Webhook Base URL: ${config.WEBHOOK_BASE_URL}`);
  console.log(`  Is Live (Render): ${config.isLive}`);
  console.log(`  Is Local: ${config.isLocal}`);
  console.log(`  Unipile API URL: ${config.UNIPILE_API_URL}`);
  console.log(`  Has Unipile Token: ${!!config.UNIPILE_API_ACCESS_TOKEN}`);

  return config;
}

// Export singleton instance
export const environmentConfig = createEnvironmentConfig();

// Export helper functions
export const isDevelopment = () => environmentConfig.NODE_ENV === 'development';
export const isProduction = () => environmentConfig.NODE_ENV === 'production';
export const isLive = () => environmentConfig.isLive;
export const isLocal = () => environmentConfig.isLocal;

// Export URL builders
export const buildWebhookUrl = (path: string): string => {
  const baseUrl = environmentConfig.WEBHOOK_BASE_URL.replace(/\/$/, '');
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
};

export const buildClientUrl = (path: string): string => {
  const baseUrl = environmentConfig.CLIENT_URL.replace(/\/$/, '');
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
};

export const buildServerUrl = (path: string): string => {
  const baseUrl = environmentConfig.SERVER_URL.replace(/\/$/, '');
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
};
