
import React from 'react';
import { AdminDashboardLayout } from '@/components/admin/AdminDashboardLayout';
import { AdminOverviewCards } from '@/components/admin/AdminOverviewCards';
import { AdminFiltersSection } from '@/components/admin/AdminFiltersSection';
import { AdminEmployeeTable } from '@/components/admin/AdminEmployeeTable';
import AdminPostFeed from '@/components/admin/AdminPostFeed';
import ApprovalsQueue from '@/components/dashboard/ApprovalsQueue';
import PageBackground from '@/components/layout/PageBackground';
import ContentCreator from '@/components/dashboard/ContentCreator';

const AdminDashboard = () => {
  return (
    <>
      <PageBackground />
      <AdminDashboardLayout>
        {/* Overview Cards */}
        <AdminOverviewCards />
        
        {/* Filters Section */}
        <AdminFiltersSection />
        
        {/* Employee Performance Table */}
        <AdminEmployeeTable />
        
        {/* Content Creator */}
        <div className="mt-8 mb-8">
          <h2 className="text-2xl font-bold mb-4">Create LinkedIn Post</h2>
          <p className="text-gray-600 mb-4">
            Create content for your company LinkedIn page or write posts for team members to share.
          </p>
          <ContentCreator />
        </div>
        
        {/* Post Review and Approval */}
        <div id="approvals-section" className="mt-8 mb-8">
          <h2 className="text-2xl font-bold mb-4">Post Review and Approval</h2>
          <p className="text-gray-600 mb-4">
            Review and approve content created by team members before it's published on LinkedIn.
          </p>
          <ApprovalsQueue />
        </div>
        
        {/* Post Content Feed */}
        <AdminPostFeed />
      </AdminDashboardLayout>
    </>
  );
};

export default AdminDashboard;
