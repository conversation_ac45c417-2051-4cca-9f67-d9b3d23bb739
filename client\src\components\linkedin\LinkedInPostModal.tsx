import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Heart,
  MessageCircle,
  Share2,
  ThumbsUp,
  Send,
  MoreHorizontal,
  Clock,
  Building,
  User,
  Briefcase,
  Paperclip,
  ExternalLink,
  Eye,
  X,
  Download,
  Image,
  Video,
  FileText
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { analyticsApi } from '@/services/analyticsApi';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';

interface LinkedInPostModalProps {
  isOpen: boolean;
  onClose: () => void;
  post: any;
  userId: string;
}

interface Comment {
  id: string;
  author: {
    name: string;
    headline?: string;
    profileUrl?: string;
    avatar?: string;
  };
  text: string;
  createdAt: string;
  reactions: {
    count: number;
    types: string[];
  };
  replies?: Comment[];
}

interface Reaction {
  id: string;
  type: 'LIKE' | 'CELEBRATE' | 'SUPPORT' | 'LOVE' | 'INSIGHTFUL' | 'FUNNY';
  author: {
    name: string;
    headline?: string;
    avatar?: string;
  };
  createdAt: string;
}

const LinkedInPostModal: React.FC<LinkedInPostModalProps> = ({
  isOpen,
  onClose,
  post,
  userId
}) => {
  const { user } = useAuth();
  const [comments, setComments] = useState<Comment[]>([]);
  const [reactions, setReactions] = useState<Reaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');

  // Attachment preview state
  const [previewAttachment, setPreviewAttachment] = useState<string | null>(null);
  const [attachmentType, setAttachmentType] = useState<'image' | 'video' | 'document' | 'unknown'>('unknown');

  // Helper function to determine attachment type
  const getAttachmentType = (url: string): 'image' | 'video' | 'document' | 'unknown' => {
    const extension = url.split('.').pop()?.toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const videoExtensions = ['mp4', 'webm', 'ogg', 'avi', 'mov'];
    const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

    if (imageExtensions.includes(extension || '')) return 'image';
    if (videoExtensions.includes(extension || '')) return 'video';
    if (documentExtensions.includes(extension || '')) return 'document';

    // Check URL patterns for LinkedIn media
    if (url.includes('media.licdn.com') && url.includes('image')) return 'image';
    if (url.includes('media.licdn.com') && url.includes('video')) return 'video';

    return 'unknown';
  };

  // Helper function to get attachment icon
  const getAttachmentIcon = (type: string) => {
    switch (type) {
      case 'image': return <Image size={16} className="text-green-600" />;
      case 'video': return <Video size={16} className="text-blue-600" />;
      case 'document': return <FileText size={16} className="text-red-600" />;
      default: return <Paperclip size={16} className="text-gray-600" />;
    }
  };

  // Handle attachment preview
  const handlePreviewAttachment = (url: string) => {
    const type = getAttachmentType(url);
    setAttachmentType(type);
    setPreviewAttachment(url);
  };

  // Close attachment preview
  const closeAttachmentPreview = () => {
    setPreviewAttachment(null);
    setAttachmentType('unknown');
  };

  // Load comments and reactions when modal opens
  useEffect(() => {
    if (isOpen && (post?.social_id || post?.id)) {
      loadPostDetails();
    }
  }, [isOpen, post?.social_id, post?.id]);

  const loadPostDetails = async () => {
    setLoading(true);
    try {
      // Use social_id (URN format) for API calls, fallback to id if social_id not available
      const postIdentifier = post.social_id || post.id;

      console.log('🔍 Loading post details:', {
        post_id: post.id,
        social_id: post.social_id,
        using_identifier: postIdentifier
      });

      // Load comments and reactions in parallel
      const [commentsResponse, reactionsResponse] = await Promise.all([
        analyticsApi.getLinkedInPostComments(userId, postIdentifier),
        analyticsApi.getLinkedInPostReactions(userId, postIdentifier)
      ]);

      setComments(commentsResponse.items || []);
      setReactions(reactionsResponse.items || []);
    } catch (error) {
      console.error('Error loading post details:', error);
      toast.error('Failed to load post details');
    } finally {
      setLoading(false);
    }
  };

  const getReactionIcon = (type: string) => {
    switch (type) {
      case 'LIKE': return <ThumbsUp className="h-4 w-4 text-blue-600" />;
      case 'CELEBRATE': return <span className="text-green-600">🎉</span>;
      case 'SUPPORT': return <span className="text-purple-600">💪</span>;
      case 'LOVE': return <Heart className="h-4 w-4 text-red-600 fill-current" />;
      case 'INSIGHTFUL': return <span className="text-yellow-600">💡</span>;
      case 'FUNNY': return <span className="text-orange-600">😂</span>;
      default: return <ThumbsUp className="h-4 w-4 text-blue-600" />;
    }
  };

  const getUserTypeIcon = (headline?: string) => {
    if (!headline) return <User className="h-3 w-3 text-gray-500" />;
    
    const lower = headline.toLowerCase();
    if (lower.includes('recruiter') || lower.includes('talent')) {
      return <Briefcase className="h-3 w-3 text-blue-600" />;
    } else if (lower.includes('ceo') || lower.includes('founder') || lower.includes('director')) {
      return <Building className="h-3 w-3 text-purple-600" />;
    } else {
      return <User className="h-3 w-3 text-gray-500" />;
    }
  };

  const getUserTypeBadge = (headline?: string) => {
    if (!headline) return null;
    
    const lower = headline.toLowerCase();
    if (lower.includes('recruiter') || lower.includes('talent')) {
      return <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700">Recruiter</Badge>;
    } else if (lower.includes('ceo') || lower.includes('founder')) {
      return <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-700">Executive</Badge>;
    } else if (lower.includes('director') || lower.includes('manager')) {
      return <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">Leadership</Badge>;
    }
    return null;
  };

  const handleAddComment = async () => {
    if (!newComment.trim()) return;
    
    // TODO: Implement comment posting via Unipile API
    toast.info('Comment posting will be implemented with Unipile API');
    setNewComment('');
  };

  const handleAddReply = async (commentId: string) => {
    if (!replyText.trim()) return;
    
    // TODO: Implement reply posting via Unipile API
    toast.info('Reply posting will be implemented with Unipile API');
    setReplyText('');
    setReplyingTo(null);
  };

  const handleReaction = async (type: string) => {
    // TODO: Implement reaction posting via Unipile API
    toast.info('Reaction posting will be implemented with Unipile API');
  };

  if (!post) return null;

  // Debug: Check if attachments are present
  console.log('📊 LinkedInPostModal received post:', post);
  console.log('📎 Post attachments_links:', post?.attachments_links);

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="border-b pb-4">
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={post.author?.avatar} />
              <AvatarFallback>{post.author?.name?.charAt(0) || 'U'}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-gray-900">{post.author?.name || 'LinkedIn User'}</h3>
                {getUserTypeBadge(post.author?.headline)}
              </div>
              <p className="text-sm text-gray-600">{post.author?.headline || 'Professional'}</p>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Clock className="h-3 w-3" />
                {formatDistanceToNow(new Date(post.created_at || Date.now()), { addSuffix: true })}
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {/* Post Content */}
          <div className="p-6 border-b">
            <div className="prose max-w-none">
              <p className="text-gray-900 whitespace-pre-wrap leading-relaxed">
                {post.text || post.content || 'No content available'}
              </p>
            </div>

            {/* Media (if available) */}
            {post.media && post.media.length > 0 && (
              <div className="mt-4 grid gap-2">
                {post.media.map((media: any, index: number) => (
                  <div key={index} className="rounded-lg overflow-hidden border">
                    {media.type === 'image' ? (
                      <img 
                        src={media.url} 
                        alt="Post media" 
                        className="w-full h-auto max-h-96 object-cover"
                      />
                    ) : media.type === 'video' ? (
                      <video 
                        src={media.url} 
                        controls 
                        className="w-full h-auto max-h-96"
                      />
                    ) : (
                      <div className="p-4 bg-gray-50 text-center">
                        <p className="text-gray-600">Media: {media.type}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Attachments Section */}
            {post.attachments_links && post.attachments_links.length > 0 && (
              <div className="mt-6">
                <div className="flex items-center gap-2 mb-4">
                  <Paperclip size={18} className="text-gray-600" />
                  <h3 className="font-semibold text-gray-900">
                    Attachments ({post.attachments_links.length})
                  </h3>
                </div>

                <div className="grid gap-3">
                  {post.attachments_links.map((url: string, index: number) => {
                    const type = getAttachmentType(url);
                    const fileName = url.split('/').pop() || `attachment-${index + 1}`;

                    return (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                          {getAttachmentIcon(type)}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {fileName}
                            </p>
                            <p className="text-xs text-gray-500 capitalize">
                              {type} attachment
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handlePreviewAttachment(url)}
                            className="text-xs"
                          >
                            <Eye size={14} className="mr-1" />
                            Preview
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(url, '_blank')}
                            className="text-xs"
                          >
                            <ExternalLink size={14} className="mr-1" />
                            Open
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Reactions Summary */}
            <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center gap-2">
                {reactions.length > 0 && (
                  <div className="flex items-center gap-1">
                    <div className="flex -space-x-1">
                      {['LIKE', 'CELEBRATE', 'LOVE'].map(type => (
                        reactions.some(r => r.type === type) && (
                          <div key={type} className="w-5 h-5 rounded-full bg-white border flex items-center justify-center">
                            {getReactionIcon(type)}
                          </div>
                        )
                      ))}
                    </div>
                    <span>{reactions.length} reactions</span>
                  </div>
                )}
              </div>
              <div className="flex items-center gap-4">
                <span>{comments.length} comments</span>
                <span>{post.shares || 0} shares</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-4 pt-4 border-t flex items-center justify-around">
              <Button 
                variant="ghost" 
                className="flex items-center gap-2 text-gray-600 hover:text-blue-600"
                onClick={() => handleReaction('LIKE')}
              >
                <ThumbsUp className="h-4 w-4" />
                Like
              </Button>
              <Button 
                variant="ghost" 
                className="flex items-center gap-2 text-gray-600 hover:text-green-600"
                onClick={() => document.getElementById('comment-input')?.focus()}
              >
                <MessageCircle className="h-4 w-4" />
                Comment
              </Button>
              <Button 
                variant="ghost" 
                className="flex items-center gap-2 text-gray-600 hover:text-purple-600"
              >
                <Share2 className="h-4 w-4" />
                Share
              </Button>
            </div>
          </div>

          {/* Comments Section */}
          <div className="p-6">
            {/* Add Comment */}
            <div className="flex gap-3 mb-6">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.user_metadata?.avatar_url} />
                <AvatarFallback>{user?.email?.charAt(0).toUpperCase()}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <Textarea
                  id="comment-input"
                  placeholder="Add a comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="min-h-[80px] resize-none"
                />
                <div className="flex justify-end mt-2">
                  <Button 
                    onClick={handleAddComment}
                    disabled={!newComment.trim()}
                    size="sm"
                  >
                    <Send className="h-4 w-4 mr-1" />
                    Post
                  </Button>
                </div>
              </div>
            </div>

            {/* Comments List */}
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-600 mt-2">Loading comments...</p>
              </div>
            ) : comments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <MessageCircle className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No comments yet. Be the first to comment!</p>
              </div>
            ) : (
              <div className="space-y-6">
                {comments.map((comment) => (
                  <div key={comment.id} className="flex gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={comment.author.avatar} />
                      <AvatarFallback>{comment.author.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-semibold text-sm">{comment.author.name}</span>
                          {getUserTypeIcon(comment.author.headline)}
                          {getUserTypeBadge(comment.author.headline)}
                        </div>
                        {comment.author.headline && (
                          <p className="text-xs text-gray-600 mb-2">{comment.author.headline}</p>
                        )}
                        <p className="text-sm text-gray-900">{comment.text}</p>
                      </div>
                      
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span>{formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}</span>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-auto p-0 text-xs text-gray-600 hover:text-blue-600"
                          onClick={() => setReplyingTo(comment.id)}
                        >
                          Reply
                        </Button>
                        {comment.reactions.count > 0 && (
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {comment.reactions.count}
                          </span>
                        )}
                      </div>

                      {/* Reply Input */}
                      {replyingTo === comment.id && (
                        <div className="mt-3 flex gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={user?.user_metadata?.avatar_url} />
                            <AvatarFallback className="text-xs">{user?.email?.charAt(0).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <Textarea
                              placeholder="Write a reply..."
                              value={replyText}
                              onChange={(e) => setReplyText(e.target.value)}
                              className="min-h-[60px] text-sm"
                            />
                            <div className="flex justify-end gap-2 mt-2">
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => setReplyingTo(null)}
                              >
                                Cancel
                              </Button>
                              <Button 
                                size="sm"
                                onClick={() => handleAddReply(comment.id)}
                                disabled={!replyText.trim()}
                              >
                                Reply
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Replies */}
                      {comment.replies && comment.replies.length > 0 && (
                        <div className="mt-4 ml-4 space-y-3 border-l-2 border-gray-100 pl-4">
                          {comment.replies.map((reply) => (
                            <div key={reply.id} className="flex gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={reply.author.avatar} />
                                <AvatarFallback className="text-xs">{reply.author.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <div className="flex-1">
                                <div className="bg-white rounded-lg p-2 border">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="font-semibold text-xs">{reply.author.name}</span>
                                    {getUserTypeBadge(reply.author.headline)}
                                  </div>
                                  <p className="text-xs text-gray-900">{reply.text}</p>
                                </div>
                                <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                                  <span>{formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>

    {/* Attachment Preview Modal */}
    {previewAttachment && (
      <Dialog open={!!previewAttachment} onOpenChange={closeAttachmentPreview}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="p-4 border-b">
            <div className="flex items-center justify-between">
              <DialogTitle className="flex items-center gap-2">
                {getAttachmentIcon(attachmentType)}
                Attachment Preview
              </DialogTitle>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.open(previewAttachment, '_blank')}
                >
                  <Download size={14} className="mr-1" />
                  Download
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={closeAttachmentPreview}
                >
                  <X size={14} />
                </Button>
              </div>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            {attachmentType === 'image' ? (
              <div className="p-4 flex items-center justify-center bg-gray-50 min-h-[400px]">
                <img
                  src={previewAttachment}
                  alt="Attachment preview"
                  className="max-w-full max-h-[70vh] object-contain rounded-lg shadow-lg"
                  onError={(e) => {
                    console.error('Image failed to load:', previewAttachment);
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
            ) : attachmentType === 'video' ? (
              <div className="p-4 flex items-center justify-center bg-gray-50 min-h-[400px]">
                <video
                  src={previewAttachment}
                  controls
                  className="max-w-full max-h-[70vh] rounded-lg shadow-lg"
                  onError={(e) => {
                    console.error('Video failed to load:', previewAttachment);
                  }}
                >
                  Your browser does not support the video tag.
                </video>
              </div>
            ) : (
              <div className="p-4">
                <div className="bg-gray-50 rounded-lg p-6 text-center min-h-[400px] flex flex-col items-center justify-center">
                  <div className="mb-4">
                    {getAttachmentIcon(attachmentType)}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Document Preview
                  </h3>
                  <p className="text-gray-600 mb-6">
                    This file type cannot be previewed directly. You can open it in a new tab or download it.
                  </p>

                  {/* Try iframe for documents */}
                  {attachmentType === 'document' && (
                    <div className="w-full h-96 border rounded-lg overflow-hidden">
                      <iframe
                        src={`https://docs.google.com/viewer?url=${encodeURIComponent(previewAttachment)}&embedded=true`}
                        className="w-full h-full"
                        title="Document preview"
                        onError={(e) => {
                          console.error('Document preview failed:', previewAttachment);
                        }}
                      />
                    </div>
                  )}

                  <div className="flex gap-3 mt-6">
                    <Button
                      onClick={() => window.open(previewAttachment, '_blank')}
                      className="flex items-center gap-2"
                    >
                      <ExternalLink size={16} />
                      Open in New Tab
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        const link = document.createElement('a');
                        link.href = previewAttachment;
                        link.download = previewAttachment.split('/').pop() || 'attachment';
                        link.click();
                      }}
                      className="flex items-center gap-2"
                    >
                      <Download size={16} />
                      Download
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    )}
    </>
  );
};

export default LinkedInPostModal;
