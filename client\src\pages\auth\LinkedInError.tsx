import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { AlertTriangle, ArrowLeft, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

const LinkedInError: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const error = searchParams.get('error') || 'Unknown error occurred';
  const errorDescription = searchParams.get('error_description') || 'LinkedIn verification failed';

  useEffect(() => {
    // Show error message
    toast.error('LinkedIn verification failed');
  }, []);

  const handleReturnToSignup = () => {
    navigate('/signup');
  };

  const handleTryAgain = () => {
    navigate('/signup');
    toast.info('Please try the LinkedIn verification again');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-red-100">
      <div className="max-w-md w-full mx-4">
        <div className="content-card text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          
          <h1 className="text-2xl font-bold text-foreground mb-4">
            LinkedIn Verification Failed
          </h1>
          
          <p className="text-muted-foreground mb-4">
            {errorDescription}
          </p>
          
          {error !== 'Unknown error occurred' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-6">
              <p className="text-xs text-red-700">
                <strong>Error:</strong> {error}
              </p>
            </div>
          )}
          
          <div className="space-y-3">
            <Button
              onClick={handleTryAgain}
              className="w-full button-primary"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            
            <Button
              onClick={handleReturnToSignup}
              variant="outline"
              className="w-full"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Return to Signup
            </Button>
          </div>
          
          <div className="mt-6 text-xs text-muted-foreground">
            <p>If you continue to experience issues, please:</p>
            <ul className="mt-2 space-y-1">
              <li>• Ensure your LinkedIn credentials are correct</li>
              <li>• Check if your LinkedIn account is accessible</li>
              <li>• Try using a different browser or device</li>
              <li>• Contact support if the problem persists</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinkedInError;
