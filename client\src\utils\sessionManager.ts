/**
 * Session Manager for tracking user login state and soft call behavior
 */

const SESSION_KEYS = {
  INITIAL_SYNC_COMPLETED: 'cvapp_initial_sync_completed',
  LOGIN_TIMESTAMP: 'cvapp_login_timestamp',
  USER_SESSION: 'cvapp_user_session'
} as const;

export class SessionManager {
  /**
   * Mark that the user has just logged in
   * This should be called immediately after successful authentication
   */
  static markUserLoggedIn(userId: string): void {
    const timestamp = Date.now();
    
    console.log('🔐 User logged in, marking session:', userId);
    
    // Store login session info
    sessionStorage.setItem(SESSION_KEYS.USER_SESSION, userId);
    sessionStorage.setItem(SESSION_KEYS.LOGIN_TIMESTAMP, timestamp.toString());
    
    // Reset the initial sync flag for new login
    sessionStorage.removeItem(SESSION_KEYS.INITIAL_SYNC_COMPLETED);
  }

  /**
   * Check if this is a fresh login (initial sync not yet completed)
   */
  static isInitialLogin(userId: string): boolean {
    const sessionUserId = sessionStorage.getItem(SESSION_KEYS.USER_SESSION);
    const syncCompleted = sessionStorage.getItem(SESSION_KEYS.INITIAL_SYNC_COMPLETED);
    
    // Check if:
    // 1. Same user session
    // 2. Initial sync not yet completed
    const isInitial = sessionUserId === userId && !syncCompleted;
    
    console.log('🔍 Checking if initial login:', {
      userId,
      sessionUserId,
      syncCompleted: !!syncCompleted,
      isInitial
    });
    
    return isInitial;
  }

  /**
   * Mark that the initial sync has been completed
   * This prevents further automatic syncs during the session
   */
  static markInitialSyncCompleted(userId: string): void {
    const sessionUserId = sessionStorage.getItem(SESSION_KEYS.USER_SESSION);
    
    if (sessionUserId === userId) {
      console.log('✅ Marking initial sync completed for user:', userId);
      sessionStorage.setItem(SESSION_KEYS.INITIAL_SYNC_COMPLETED, 'true');
    } else {
      console.warn('⚠️ User ID mismatch when marking sync completed:', {
        userId,
        sessionUserId
      });
    }
  }

  /**
   * Check if automatic sync should be allowed
   * Returns true only for initial login, false for route navigation/refresh
   */
  static shouldAllowAutomaticSync(userId: string): boolean {
    const isInitial = this.isInitialLogin(userId);
    
    console.log('🤔 Should allow automatic sync?', {
      userId,
      isInitial,
      reason: isInitial ? 'Initial login' : 'Route navigation/refresh'
    });
    
    return isInitial;
  }

  /**
   * Get session info for debugging
   */
  static getSessionInfo(): {
    userId: string | null;
    loginTimestamp: number | null;
    syncCompleted: boolean;
    sessionAge: number | null;
  } {
    const userId = sessionStorage.getItem(SESSION_KEYS.USER_SESSION);
    const timestampStr = sessionStorage.getItem(SESSION_KEYS.LOGIN_TIMESTAMP);
    const syncCompleted = !!sessionStorage.getItem(SESSION_KEYS.INITIAL_SYNC_COMPLETED);
    
    const loginTimestamp = timestampStr ? parseInt(timestampStr) : null;
    const sessionAge = loginTimestamp ? Date.now() - loginTimestamp : null;
    
    return {
      userId,
      loginTimestamp,
      syncCompleted,
      sessionAge
    };
  }

  /**
   * Clear session data (on logout)
   */
  static clearSession(): void {
    console.log('🧹 Clearing session data');
    
    Object.values(SESSION_KEYS).forEach(key => {
      sessionStorage.removeItem(key);
    });
  }

  /**
   * Force allow next automatic sync (for testing or special cases)
   */
  static resetSyncFlag(userId: string): void {
    console.log('🔄 Resetting sync flag for user:', userId);
    sessionStorage.removeItem(SESSION_KEYS.INITIAL_SYNC_COMPLETED);
  }

  /**
   * Check if user session is valid
   */
  static isValidSession(userId: string): boolean {
    const sessionUserId = sessionStorage.getItem(SESSION_KEYS.USER_SESSION);
    return sessionUserId === userId;
  }

  /**
   * Debug helper to log current session state
   */
  static logSessionState(): void {
    const info = this.getSessionInfo();
    console.log('📊 Current session state:', {
      ...info,
      sessionAgeMinutes: info.sessionAge ? Math.round(info.sessionAge / 60000) : null
    });
  }
}

/**
 * Hook for using session manager in React components
 */
export const useSessionManager = (userId?: string) => {
  const markLoggedIn = (id: string) => SessionManager.markUserLoggedIn(id);
  const isInitialLogin = () => userId ? SessionManager.isInitialLogin(userId) : false;
  const markSyncCompleted = () => userId && SessionManager.markInitialSyncCompleted(userId);
  const shouldAllowSync = () => userId ? SessionManager.shouldAllowAutomaticSync(userId) : false;
  const clearSession = () => SessionManager.clearSession();
  const resetSyncFlag = () => userId && SessionManager.resetSyncFlag(userId);
  const getSessionInfo = () => SessionManager.getSessionInfo();
  const logSessionState = () => SessionManager.logSessionState();

  return {
    markLoggedIn,
    isInitialLogin,
    markSyncCompleted,
    shouldAllowSync,
    clearSession,
    resetSyncFlag,
    getSessionInfo,
    logSessionState
  };
};
