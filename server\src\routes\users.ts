import { Router } from 'express';
import {
    handleGetUser,
    handleGetUserByEmail,
    handleUpdateUser,
    handleCreateUser,
    handleGetUserLinkedInAccountId
} from '../handlers/user.handlers';

/**
 * User Routes
 */

const router = Router();

// User CRUD operations
router.get('/:userId', handleGetUser);                           // GET /api/users/:userId
router.get('/email/:email', handleGetUserByEmail);              // GET /api/users/email/:email  
router.put('/:userId', handleUpdateUser);                       // PUT /api/users/:userId
router.post('/', handleCreateUser);                             // POST /api/users

// User LinkedIn-specific operations
router.get('/:userId/linkedin-account-id', handleGetUserLinkedInAccountId); // GET /api/users/:userId/linkedin-account-id

export default router;
