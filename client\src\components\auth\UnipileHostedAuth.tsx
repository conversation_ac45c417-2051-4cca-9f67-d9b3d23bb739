import React, { useState } from 'react';
import { Linkedin, Loader2, Shield, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { getApiUrl } from '@/config/env';

// Types from official Unipile documentation
interface HostedAuthResponse {
  object: string;
  url: string;
}

interface ConnectedAccount {
  status: 'CREATION_SUCCESS' | 'RECONNECTED';
  account_id: string;
  name: string;
}

interface UnipileHostedAuthProps {
  userId: string;
  onSuccess?: (account: ConnectedAccount) => void;
  onError?: (error: string) => void;
  providers?: string[];
}

// Official Unipile Hosted Auth Wizard Component
export const UnipileHostedAuth: React.FC<UnipileHostedAuthProps> = ({
  userId,
  onSuccess,
  onError,
  providers = ['LINKEDIN']
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleConnectAccount = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🚀 Starting official Unipile hosted auth for user:', userId);

      // Generate hosted auth link using official API pattern
      const response = await fetch(getApiUrl('/auth/hosted-auth/create'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail: userId, // Backend expects userEmail parameter
          type: 'create',
          providers
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('✅ Backend response:', responseData);

      // Extract the URL from the backend response structure
      const hostedAuthUrl = responseData.data?.url || responseData.url;

      if (!hostedAuthUrl) {
        throw new Error('No hosted auth URL received from backend');
      }

      console.log('✅ Hosted auth URL generated:', hostedAuthUrl);

      // Official pattern: Full page redirect (not popup)
      console.log('🔄 Redirecting to Unipile hosted auth page...');
      window.location.href = hostedAuthUrl;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate auth link';
      console.error('❌ Hosted auth error:', errorMessage);
      setError(errorMessage);
      onError?.(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-blue-100 rounded-full">
            <Shield className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Connect Your LinkedIn Account
        </h3>
        <p className="text-gray-600">
          Using Unipile's official hosted authentication wizard
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="text-red-500">❌</div>
            <div>
              <h4 className="text-sm font-medium text-red-800">Authentication Error</h4>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-800 mb-2">Official Unipile Hosted Auth:</h4>
        <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
          <li>Click "Connect LinkedIn Account" below</li>
          <li>You'll be redirected to Unipile's official hosted auth page</li>
          <li>Complete LinkedIn verification (CAPTCHA, OTP, security checks)</li>
          <li>Unipile will redirect you back with success/failure status</li>
          <li>Your account will be automatically connected</li>
        </ol>
        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
          <strong>Note:</strong> This uses the official Unipile hosted auth wizard pattern with full page redirects.
        </div>
      </div>

      {/* Connect Button */}
      <Button
        onClick={handleConnectAccount}
        disabled={loading}
        className="w-full button-primary"
      >
        {loading ? (
          <span className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Generating Auth Link...
          </span>
        ) : (
          <span className="flex items-center gap-2">
            <Linkedin className="h-4 w-4" />
            Connect LinkedIn Account
          </span>
        )}
      </Button>

      {/* Debug Info */}
      <div className="text-xs text-gray-500 text-center">
        <p>User ID: {userId}</p>
        <p>Providers: {providers.join(', ')}</p>
        <p>Using official Unipile hosted auth wizard pattern</p>
      </div>
    </div>
  );
};

// Reconnect Component (for existing accounts)
export const UnipileReconnectAccount: React.FC<{
  userId: string;
  accountId: string;
  onSuccess?: (account: ConnectedAccount) => void;
  onError?: (error: string) => void;
}> = ({ userId, accountId, onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleReconnect = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Starting reconnect for account:', accountId);

      const response = await fetch(getApiUrl('/auth/hosted-auth/reconnect'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail: userId, // Backend expects userEmail parameter
          accountId,
          type: 'reconnect'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('✅ Backend response:', responseData);

      // Extract the URL from the backend response structure
      const reconnectUrl = responseData.data?.url || responseData.url;

      if (!reconnectUrl) {
        throw new Error('No reconnect URL received from backend');
      }

      console.log('✅ Reconnect URL generated:', reconnectUrl);

      // Official pattern: Full page redirect
      window.location.href = reconnectUrl;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate reconnect link';
      console.error('❌ Reconnect error:', errorMessage);
      setError(errorMessage);
      onError?.(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-3">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}
      
      <Button
        onClick={handleReconnect}
        disabled={loading}
        variant="outline"
        className="w-full"
      >
        {loading ? (
          <span className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Generating Reconnect Link...
          </span>
        ) : (
          <span className="flex items-center gap-2">
            <ExternalLink className="h-4 w-4" />
            Reconnect Account
          </span>
        )}
      </Button>
    </div>
  );
};

// Hook for managing connected accounts
export const useUnipileAuth = () => {
  const [connectedAccounts, setConnectedAccounts] = useState<ConnectedAccount[]>([]);
  const [loading, setLoading] = useState(false);

  const addConnectedAccount = (account: ConnectedAccount) => {
    setConnectedAccounts(prev => [...prev, account]);
  };

  const removeAccount = (accountId: string) => {
    setConnectedAccounts(prev => prev.filter(acc => acc.account_id !== accountId));
  };

  return {
    connectedAccounts,
    loading,
    addConnectedAccount,
    removeAccount,
    setLoading
  };
};

export default UnipileHostedAuth;
