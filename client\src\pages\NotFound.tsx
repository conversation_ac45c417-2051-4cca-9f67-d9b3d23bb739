
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Home } from 'lucide-react';
import PageBackground from '@/components/layout/PageBackground';

const NotFound = () => {
  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      <PageBackground />
      
      <div className="text-center p-8 relative z-10 max-w-md">
        <h1 className="text-9xl font-bold text-black">404</h1>
        <h2 className="text-3xl font-semibold mt-4 mb-6">Page Not Found</h2>
        <p className="text-gray-600 mb-8">
          The page you are looking for might be under construction or doesn't exist.
        </p>
        <Button asChild className="bg-black hover:bg-black/90 text-white">
          <Link to="/" className="inline-flex items-center">
            <Home className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
