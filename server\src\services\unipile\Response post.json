{"object": "PostList", "items": [{"object": "Post", "provider": "LINKEDIN", "social_id": "urn:li:ugcPost:7347196037805043712", "share_url": "https://www.linkedin.com/posts/theafaq-ul-islam_python-machinelearning-ai-ugcPost-7347196037805043712-W5fE?utm_source=combined_share_message&utm_medium=member_desktop_web&rcm=ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "date": "3d", "parsed_datetime": "2025-07-05T11:53:00.558Z", "comment_counter": 2, "impressions_counter": 0, "reaction_counter": 8, "repost_counter": 1, "permissions": {"can_post_comments": true, "can_react": true, "can_share": true}, "text": "💡 Why Python?\n Because it empowers me to build what others only imagine. From handling large datasets to real-time computer vision — it just works!\nI recently built a Face Recognition Attendance System using Python and OpenCV.\n 👁️ It identifies known faces\n 🕒 Marks attendance with date & time\n 📄 Saves it all in a CSV file\nThis is just a glimpse of what’s possible with machine learning. Super excited for what’s ahead!\n#Python #MachineLearning #AI #FaceRecognition #OpenCV #DeveloperJourney #TechInnovation", "attachments": [{"type": "video", "gif": false, "id": "D4D05AQFwEmWlAb-mMg", "size": {"height": 720, "width": 1276}, "unavailable": false, "url": "https://dms.licdn.com/playlist/vid/v2/D4D05AQFwEmWlAb-mMg/mp4-720p-30fp-crf28/B4DZfZ73aQHMBw-/0/1751708129161?e=**********&v=beta&t=jeJgr97rH3iKrBvCHBpsBij6sLvnL6VNR34-GPQS0Mo"}], "author": {"public_identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "ACoAAC2kA80BfPapePkmB8IPFzwCV3lMsMlzAZk", "name": "<PERSON><PERSON><PERSON>", "is_company": false, "headline": "Software Engineer | MERN Stack | React Js | Next Js | React Native Developer"}, "is_repost": true, "id": "7347196037805043712", "repost_id": "7348261710769598465", "reposted_by": {"public_identifier": "m-haroon-a<PERSON>an-387b29303", "id": "ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "name": "<PERSON>", "is_company": false}}, {"object": "Post", "provider": "LINKEDIN", "social_id": "urn:li:activity:7347659384392429568", "share_url": "https://www.linkedin.com/posts/rafi-ullah-38b522339_scamalert-linkedinsafety-cybercrime-activity-7347659384392429568-Kr9c?utm_source=combined_share_message&utm_medium=member_desktop_web&rcm=ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "date": "1d", "parsed_datetime": "2025-07-07T11:53:00.558Z", "comment_counter": 63, "impressions_counter": 0, "reaction_counter": 171, "repost_counter": 19, "permissions": {"can_post_comments": true, "can_react": true, "can_share": true}, "text": "🚨 SCAM ALERT: LinkedIn Account <PERSON><PERSON> 🚨\n\nJust got a shady WhatsApp message offering money to rent my LinkedIn account – claiming they'd pay weekly if I had 500+ connections.\n\n❗ It’s a trap.\nThese accounts are used in big financial scams, and you end up facing legal action — including FBR cases, arrests, and heavy fines.\n\nThis isn’t “easy money.” It’s digital self-destruction.\n\n✅ Never rent your LinkedIn account\n✅ Warn others\n✅ Report such messages\n\nStay safe. Share this and spread awareness.\n\n#ScamAlert #LinkedInSafety #CyberCrime #Awareness #FraudPrevention #RafiUllah", "attachments": [{"id": "D4D22AQGNHVRMIjzi-w", "sticker": false, "size": {"height": 629, "width": 480}, "unavailable": false, "type": "img", "url": "https://media.licdn.com/dms/image/v2/D4D22AQGNHVRMIjzi-w/feedshare-shrink_480/B4DZfghPgEGsAc-/0/*************?e=**********&v=beta&t=E3P3J15yWmVcEqcpbWkfxZVtNKhDr5fRNSigkyAp7Mc"}], "author": {"public_identifier": "rafi-ul<PERSON>-38b522339", "id": "ACoAAFT4_ZsBPwrRTEnwx96tk7K05ouP4cie4OU", "name": "<PERSON><PERSON>", "is_company": false, "headline": "Web Developer at IT"}, "is_repost": true, "id": "7347659384392429568", "repost_id": "7348208230679859200", "reposted_by": {"public_identifier": "m-haroon-a<PERSON>an-387b29303", "id": "ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "name": "<PERSON>", "is_company": false}}, {"object": "Post", "provider": "LINKEDIN", "social_id": "urn:li:activity:7347873533609369600", "share_url": "https://www.linkedin.com/posts/m-haroon-awaan-387b29303_4testing-activity-7347873533609369600-zWBe?utm_source=combined_share_message&utm_medium=member_desktop_web&rcm=ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "date": "1d", "parsed_datetime": "2025-07-07T11:53:00.558Z", "comment_counter": 1, "impressions_counter": 34, "reaction_counter": 1, "repost_counter": 0, "permissions": {"can_post_comments": true, "can_react": true, "can_share": true}, "text": "Hi to all of you...😂😂😂\n#4testing", "attachments": [], "author": {"public_identifier": "m-haroon-a<PERSON>an-387b29303", "id": "ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "name": "<PERSON>", "is_company": false, "headline": "Full Stack Developer || MERN || AI & Machine Learning || Prompt Engineer"}, "is_repost": false, "id": "7347873533609369600"}, {"object": "Post", "provider": "LINKEDIN", "social_id": "urn:li:activity:7346427869796995073", "share_url": "https://www.linkedin.com/posts/m-haroon-awaan-387b29303_techcreator-mernstack-webdevelopment-activity-7346427869796995073-lcrB?utm_source=combined_share_message&utm_medium=member_desktop_web&rcm=ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "date": "5d", "parsed_datetime": "2025-07-03T11:53:00.558Z", "comment_counter": 1, "impressions_counter": 112, "reaction_counter": 2, "repost_counter": 0, "permissions": {"can_post_comments": true, "can_react": true, "can_share": true}, "text": "🎉 6 Months as a MERN Stack Developer at Tech Creator! 🚀\nIt’s been an incredible 6 months journey since I started my role as a MERN Stack Developer at Tech Creator. From the very beginning, it’s been a path filled with learning, growth, and exciting challenges.\nI'm proud of the progress we've made and grateful for the opportunity to work alongside such a talented team. Looking ahead, I hope this journey continues to bring more valuable work, growth, and success! \nAppreciate the support from the amazing team at Tech Creator!\n\n#TechCreator #MERNStack #WebDevelopment #CareerJourney #6MonthsMilestone #Gratitude #KeepGrowing", "attachments": [], "author": {"public_identifier": "m-haroon-a<PERSON>an-387b29303", "id": "ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "name": "<PERSON>", "is_company": false, "headline": "Full Stack Developer || MERN || AI & Machine Learning || Prompt Engineer"}, "is_repost": false, "id": "7346427869796995073"}, {"object": "Post", "provider": "LINKEDIN", "social_id": "urn:li:ugcPost:7341395416912908288", "share_url": "https://www.linkedin.com/posts/m-haroon-awaan-387b29303_introductoin-to-react-js-activity-7341395417693089794-6rO-?utm_source=combined_share_message&utm_medium=member_desktop_web&rcm=ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "date": "2w", "parsed_datetime": "2025-06-24T11:53:00.558Z", "comment_counter": 0, "impressions_counter": 49, "reaction_counter": 1, "repost_counter": 0, "permissions": {"can_post_comments": true, "can_react": true, "can_share": true}, "text": "Let get Start,\n#React_Intro #React", "attachments": [{"type": "file", "id": "D4D1FAQFyfQ-8mhqdfA", "file_name": "Introductoin to React JS ", "file_size": 0, "mimetype": "application/pdf", "unavailable": false, "url": "https://media.licdn.com/dms/document/media/v2/D4D1FAQFyfQ-8mhqdfA/feedshare-document-sanitized-pdf/B4DZeHf65zGYBA-/0/1750324993782?e=**********&v=beta&t=_GTtxy9zHiLgDn7Yq0dhWpESftUwuVDO7BxgqxoGAHE", "url_expires_at": **********000}], "author": {"public_identifier": "m-haroon-a<PERSON>an-387b29303", "id": "ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "name": "<PERSON>", "is_company": false, "headline": "Full Stack Developer || MERN || AI & Machine Learning || Prompt Engineer"}, "is_repost": false, "id": "7341395417693089794"}, {"object": "Post", "provider": "LINKEDIN", "social_id": "urn:li:ugcPost:7340978828535652353", "share_url": "https://www.linkedin.com/posts/m-haroon-awaan-387b29303_im-happy-to-share-that-im-starting-a-new-activity-7340978829391192064-trgR?utm_source=combined_share_message&utm_medium=member_desktop_web&rcm=ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "date": "2w", "parsed_datetime": "2025-06-24T11:53:00.559Z", "comment_counter": 1, "impressions_counter": 94, "reaction_counter": 2, "repost_counter": 0, "permissions": {"can_post_comments": true, "can_react": true, "can_share": true}, "text": "I’m happy to share that I’m starting a new position as Web Developer at TechCreator!", "attachments": [], "author": {"public_identifier": "m-haroon-a<PERSON>an-387b29303", "id": "ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "name": "<PERSON>", "is_company": false, "headline": "Full Stack Developer || MERN || AI & Machine Learning || Prompt Engineer"}, "is_repost": false, "id": "7340978829391192064"}, {"object": "Post", "provider": "LINKEDIN", "social_id": "urn:li:activity:7334564929695277057", "share_url": "https://www.linkedin.com/posts/m-haroon-awaan-387b29303_i-am-new-to-linkedin-can-anyone-help-to-activity-7334564929695277057-Q6P9?utm_source=combined_share_message&utm_medium=member_desktop_web&rcm=ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "date": "1mo", "parsed_datetime": "2025-06-08T11:53:00.559Z", "comment_counter": 2, "impressions_counter": 83, "reaction_counter": 2, "repost_counter": 0, "permissions": {"can_post_comments": true, "can_react": true, "can_share": true}, "text": "I am new to linkedin , can anyone help to get better opportunity of the jobs", "attachments": [], "author": {"public_identifier": "m-haroon-a<PERSON>an-387b29303", "id": "ACoAAE2WVNcBIn14lwNvbE_-qExzeXJi_Qh2GPM", "name": "<PERSON>", "is_company": false, "headline": "Full Stack Developer || MERN || AI & Machine Learning || Prompt Engineer"}, "is_repost": false, "id": "7334564929695277057"}], "cursor": "eyJwYWdpbmF0aW9uX3Rva2VuIjoiZFhKdU9teHBPbUZqZEdsMmFYUjVPamN6TXpRMU5qUTVNamsyT1RVeU56Y3dOVGN0TVRjME9EWTVOalUwT0Rnek53PT0iLCJzdGFydCI6N30=", "paging": {"page_count": 7}}