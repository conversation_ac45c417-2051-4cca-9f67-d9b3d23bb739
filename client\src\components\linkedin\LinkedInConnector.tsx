import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Loader2, Unlink, Linkedin, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';
import { LinkedInConnectionState } from '@/types/linkedin';
import LinkedInConnectionDialog from './LinkedInConnectionDialog';
import { supabase } from '@/integrations/supabase/client';

const LinkedInConnector: React.FC = () => {
  const { user, profile } = useAuth();
  const [connectionState, setConnectionState] = useState<LinkedInConnectionState>({
    status: 'disconnected',
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  useEffect(() => {
    checkLinkedInConnection();
  }, [profile]);
  
  const checkLinkedInConnection = () => {
    setConnectionState(prev => ({ ...prev, status: 'checking' }));

    try {
      // Check if user has linkedinAccountId from signup process
      const userMetadata = user?.user_metadata;
      const hasLinkedInAccountId = userMetadata?.linkedinAccountId || userMetadata?.linkedin_account_id;

      // If we have a linkedin account ID or linkedin_url in the profile, we'll consider it connected
      if (hasLinkedInAccountId || profile?.linkedin_url) {
        setConnectionState({
          status: 'connected',
          accountId: hasLinkedInAccountId || profile?.phyllo_id
        });
      } else {
        setConnectionState({ status: 'disconnected' });
      }
    } catch (error) {
      console.error("Error checking LinkedIn connection:", error);
      toast.error("Failed to check LinkedIn connection status");
      setConnectionState({
        status: 'error',
        error: "Failed to check LinkedIn connection status"
      });
    }
  };
  
  const handleConnectClick = () => {
    setIsDialogOpen(true);
  };
  
  const handleConnect = async (credentials: { email: string; password: string }) => {
    setConnectionState(prev => ({
      ...prev,
      status: 'connecting',
      credentials
    }));
    
    // Simulate a connection delay and 2FA requirement
    setTimeout(() => {
      // Simulate a 2FA requirement 50% of the time
      if (Math.random() > 0.5) {
        setConnectionState(prev => ({
          ...prev,
          status: '2fa_required',
          twoFactorDetails: {
            type: 'sms_code',
            message: `Enter the verification code sent to the phone number ending in ****${Math.floor(1000 + Math.random() * 9000)}`,
            codeLength: 6
          }
        }));
      } else {
        // Complete the connection successfully
        completeConnection();
      }
    }, 2000);
  };
  
  const handleSubmit2FA = async (code: string) => {
    setConnectionState(prev => ({
      ...prev,
      status: 'connecting',
    }));
    
    // Validate the 2FA code here (simulated)
    setTimeout(() => {
      if (code.length === 6) {
        completeConnection();
      } else {
        setConnectionState(prev => ({
          ...prev,
          status: 'error',
          error: "Invalid verification code. Please try again."
        }));
      }
    }, 1500);
  };
  
  const completeConnection = async () => {
    try {
      if (user) {
        // Generate a mock Phyllo ID for the simulation
        const mockPhylloId = `phyllo-${Math.random().toString(36).substring(2, 10)}`;
        
        // Update the user's profile with complete LinkedIn data (mock)
        const now = new Date().toISOString();
        const mockFirstName = user.user_metadata?.firstName || 'User';
        const mockLastName = user.user_metadata?.lastName || 'Demo';

        const { error } = await supabase
          .from('profiles')
          .update({
            first_name: mockFirstName,  // Update name from user metadata
            last_name: mockLastName,    // Update name from user metadata
            account_id: mockPhylloId,
            linkedin_email: user.email,
            linkedin_user_id: `mock-user-${user.id.slice(0, 8)}`,
            linkedin_url: `https://linkedin.com/in/user-${user.id.slice(0, 8)}`,
            linkedin_connection_status: 'connected',
            linkedin_connected_at: now,
            linkedin_last_sync: now,
            linkedin_profile_data: {
              name: `${mockFirstName} ${mockLastName}`,
              id: `mock-user-${user.id.slice(0, 8)}`,
              publicIdentifier: `user-${user.id.slice(0, 8)}`
            },
            linkedin_reconnection_count: 0,
            phyllo_id: mockPhylloId
          })
          .eq('id', user.id);
        
        if (error) {
          throw error;
        }
        
        // Update local state to reflect the connection
        setConnectionState({
          status: 'connected',
          accountId: mockPhylloId
        });
        
        toast.success("LinkedIn account connected successfully");
        setIsDialogOpen(false);
        
        // Force refresh the profile in the auth context by reloading the page
        // In a real implementation, you would update the context directly
        window.location.reload();
      }
    } catch (error) {
      console.error("Error connecting to LinkedIn:", error);
      setConnectionState({
        status: 'error',
        error: "Failed to connect LinkedIn account"
      });
      toast.error("Failed to connect LinkedIn account");
    }
  };
  
  const disconnectLinkedIn = async () => {
    try {
      if (user) {
        const { error } = await supabase
          .from('profiles')
          .update({
            linkedin_url: null,
            linkedin_connection_status: 'disconnected',
            linkedin_last_sync: new Date().toISOString(),
            // Keep historical data but mark as disconnected
          })
          .eq('id', user.id);
        
        if (error) {
          throw error;
        }
        
        setConnectionState({ status: 'disconnected' });
        toast.success("LinkedIn account disconnected");
        
        // Refresh the profile in the auth context
        window.location.reload();
      }
    } catch (error) {
      console.error("Error disconnecting from LinkedIn:", error);
      toast.error("Failed to disconnect LinkedIn account");
    }
  };
  
  return (
    <>
      <Card className="mb-8 overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-xl font-semibold">
            <Linkedin className="mr-2 h-5 w-5" />
            LinkedIn Connection
          </CardTitle>
          <CardDescription>
            Connect your LinkedIn account to track your post performance
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {connectionState.status === 'checking' && (
            <div className="flex items-center gap-2 py-2">
              <Loader2 className="h-5 w-5 animate-spin text-gray-500" />
              <span>Checking connection status...</span>
            </div>
          )}
          
          {connectionState.status === 'connected' && (
            <div className="rounded-md bg-green-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">LinkedIn Connected</h3>
                  <div className="mt-2 text-sm text-green-700">
                    <p>Your LinkedIn account is connected and ready to use.</p>
                    {profile?.linkedin_url && (
                      <p className="mt-1">
                        Profile: <a href={profile.linkedin_url} target="_blank" rel="noopener noreferrer" className="font-medium underline">
                          {profile.linkedin_url.replace('https://linkedin.com/in/', '')}
                        </a>
                      </p>
                    )}
                    {connectionState.accountId && (
                      <p className="mt-1">
                        Account ID: <span className="font-mono text-xs bg-green-100 px-2 py-0.5 rounded">{connectionState.accountId}</span>
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {connectionState.status === 'disconnected' && (
            <div className="rounded-md bg-gray-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-5 w-5 text-gray-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-800">LinkedIn Not Connected</h3>
                  <div className="mt-2 text-sm text-gray-700">
                    <p>Connect your LinkedIn account to track your post performance and engagement.</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {connectionState.status === 'connecting' && (
            <div className="flex flex-col items-center justify-center gap-3 py-6">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-center text-sm text-gray-600">
                Connecting to LinkedIn...
                <br />
                <span className="text-xs">This may take a few moments</span>
              </p>
            </div>
          )}
          
          {connectionState.status === 'error' && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-5 w-5 text-red-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Connection Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{connectionState.error || "There was an error connecting to LinkedIn."}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="bg-gray-50 px-6 py-4">
          {connectionState.status === 'connected' && (
            <Button 
              variant="outline" 
              onClick={disconnectLinkedIn}
              className="w-full sm:w-auto"
            >
              <Unlink className="mr-2 h-4 w-4" />
              Disconnect LinkedIn
            </Button>
          )}
          
          {connectionState.status === 'disconnected' && (
            <Button 
              onClick={handleConnectClick} 
              className="w-full bg-[#0A66C2] hover:bg-[#084E96] sm:w-auto"
            >
              <Linkedin className="mr-2 h-4 w-4" />
              Connect with LinkedIn
            </Button>
          )}
          
          {(connectionState.status === 'checking' || connectionState.status === 'connecting') && (
            <Button 
              disabled
              className="w-full sm:w-auto"
            >
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {connectionState.status === 'checking' ? 'Checking...' : 'Connecting...'}
            </Button>
          )}
          
          {connectionState.status === 'error' && (
            <Button 
              onClick={handleConnectClick} 
              className="w-full bg-[#0A66C2] hover:bg-[#084E96] sm:w-auto"
            >
              <Linkedin className="mr-2 h-4 w-4" />
              Retry Connection
            </Button>
          )}
        </CardFooter>
      </Card>
      
      <LinkedInConnectionDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onConnect={handleConnect}
        onSubmit2FA={handleSubmit2FA}
        connectionState={connectionState}
      />
    </>
  );
};

export default LinkedInConnector;
