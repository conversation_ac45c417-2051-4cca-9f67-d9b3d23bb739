import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface MetricUpdateAnimationProps {
  currentValue: number;
  previousValue?: number;
  label?: string;
  className?: string;
  showAnimation?: boolean;
  animationDuration?: number;
}

export const MetricUpdateAnimation: React.FC<MetricUpdateAnimationProps> = ({
  currentValue,
  previousValue,
  label,
  className = '',
  showAnimation = false,
  animationDuration = 3000
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [showDiff, setShowDiff] = useState(false);

  useEffect(() => {
    if (showAnimation && previousValue !== undefined && previousValue !== currentValue) {
      setIsAnimating(true);
      setShowDiff(true);

      const timer = setTimeout(() => {
        setIsAnimating(false);
        setShowDiff(false);
      }, animationDuration);

      return () => clearTimeout(timer);
    }
  }, [showAnimation, currentValue, previousValue, animationDuration]);

  const difference = previousValue !== undefined ? currentValue - previousValue : 0;
  const isIncrease = difference > 0;
  const isDecrease = difference < 0;

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className={cn('relative inline-block', className)}>
      {/* Main Value */}
      <span
        className={cn(
          'font-medium transition-all duration-500',
          isAnimating && isIncrease && 'text-green-600',
          isAnimating && isDecrease && 'text-red-600',
          isAnimating && 'animate-pulse'
        )}
      >
        {formatNumber(currentValue)}
      </span>

      {/* Animation Overlay */}
      {showDiff && previousValue !== undefined && difference !== 0 && (
        <div className="absolute -top-6 left-0 right-0 flex items-center justify-center">
          <div
            className={cn(
              'flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium animate-bounce',
              isIncrease && 'bg-green-100 text-green-700',
              isDecrease && 'bg-red-100 text-red-700'
            )}
          >
            {isIncrease && <TrendingUp className="w-3 h-3" />}
            {isDecrease && <TrendingDown className="w-3 h-3" />}
            <span className="text-[10px] opacity-60">
              {previousValue} → {currentValue}
            </span>
            <span className="text-[10px] font-bold">
              {isIncrease ? '+' : ''}{difference}
            </span>
          </div>
        </div>
      )}

      {/* Subtle Background Animation */}
      {isAnimating && (
        <div
          className={cn(
            'absolute inset-0 -z-10 rounded animate-pulse',
            isIncrease && 'bg-green-50',
            isDecrease && 'bg-red-50'
          )}
        />
      )}
    </div>
  );
};

// Hook to manage metric updates
export const useMetricUpdates = () => {
  const [previousMetrics, setPreviousMetrics] = useState<Record<string, any>>({});
  const [updatedMetrics, setUpdatedMetrics] = useState<Set<string>>(new Set());

  const updateMetrics = (newMetrics: Record<string, any>, postId: string) => {
    const prevMetrics = previousMetrics[postId] || {};
    
    // Check which metrics have changed
    const changedMetrics = new Set<string>();
    Object.keys(newMetrics).forEach(key => {
      if (prevMetrics[key] !== newMetrics[key]) {
        changedMetrics.add(`${postId}-${key}`);
      }
    });

    // Store previous metrics
    setPreviousMetrics(prev => ({
      ...prev,
      [postId]: prevMetrics
    }));

    // Mark metrics as updated
    setUpdatedMetrics(changedMetrics);

    // Clear update indicators after animation
    setTimeout(() => {
      setUpdatedMetrics(new Set());
    }, 3000);

    return changedMetrics;
  };

  const isMetricUpdated = (postId: string, metricKey: string) => {
    return updatedMetrics.has(`${postId}-${metricKey}`);
  };

  const getPreviousValue = (postId: string, metricKey: string) => {
    return previousMetrics[postId]?.[metricKey];
  };

  return {
    updateMetrics,
    isMetricUpdated,
    getPreviousValue
  };
};
