
import React from 'react';
import { Link } from 'react-router-dom';
import { PricingDialog } from '../pricing/PricingDialog';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-white/80 backdrop-blur-sm border-t py-8 mt-auto relative z-10">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-md bg-black flex items-center justify-center">
                <span className="font-bold text-white">CV</span>
              </div>
              <span className="font-semibold text-lg text-black">CompanyVoice</span>
            </div>
            <p className="text-gray-600 max-w-xs">
              Empower your employees to share your company's story with confidence.
            </p>
          </div>
          
          <div>
            <h5 className="font-medium mb-4 text-black">Best Practices</h5>
            <ul className="space-y-2">
              <li>
                <Link 
                  to="/best-practices" 
                  className="text-gray-600 hover:text-black transition-colors"
                >
                  Content Strategy Guide
                </Link>
              </li>
              <li>
                <a 
                  href="https://www.linkedin.com/help/linkedin/topics/6122/6135/Content+marketing+on+LinkedIn" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="text-gray-600 hover:text-black transition-colors"
                >
                  LinkedIn's Official Guide
                </a>
              </li>
              <li>
                <a 
                  href="https://business.linkedin.com/marketing-solutions/ads/ad-specs" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="text-gray-600 hover:text-black transition-colors"
                >
                  LinkedIn Ads Best Practices
                </a>
              </li>
            </ul>
          </div>
          
          <div>
            <h5 className="font-medium mb-4 text-black">Navigation</h5>
            <ul className="space-y-2">
              <li>
                <PricingDialog 
                  trigger={
                    <button className="text-gray-600 hover:text-black text-left transition-colors">
                      Pricing
                    </button>
                  }
                />
              </li>
              <li>
                <Link to="/terms" className="text-gray-600 hover:text-black transition-colors">
                  Terms and Conditions
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-600 hover:text-black transition-colors">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
          
          <div className="hidden md:block"></div>
        </div>
        
        <div className="border-t mt-8 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-600 text-sm">
            &copy; {currentYear} CompanyVoice. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link to="/terms" className="text-gray-600 text-sm hover:text-black transition-colors">
              Terms
            </Link>
            <Link to="/privacy" className="text-gray-600 text-sm hover:text-black transition-colors">
              Privacy
            </Link>
            <Link to="/cookies" className="text-gray-600 text-sm hover:text-black transition-colors">
              Cookies
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
