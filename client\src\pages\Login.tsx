
import React, { useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Mail, Lock, LogIn } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";
import PageBackground from '@/components/layout/PageBackground';

import { ToastManager } from '@/utils/toastManager';

const formSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(1, { message: "Password is required" }),
});

type FormValues = z.infer<typeof formSchema>;

const Login = () => {
  const navigate = useNavigate();
  const { signIn, user, profile, isLoading } = useAuth();
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  useEffect(() => {
    // Verification reminders are now handled via URL parameters or user state
    // No localStorage needed for verification reminders

    // If user is already logged in, redirect to appropriate dashboard
    if (user && !isLoading) {
      // Clear registration data after successful login to avoid conflicts
      console.log('🧹 Clearing registration data after successful login');

      if (profile?.role === 'admin') {
        navigate('/admin-dashboard');
      } else {
        navigate('/user-dashboard');
      }
    }
  }, [user, profile, isLoading, navigate]);

  // Listen for verification completion messages from popup windows
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Verify origin for security
      if (event.origin !== window.location.origin) {
        return;
      }

      if (event.data.type === 'VERIFICATION_COMPLETE') {
        console.log('📨 Received verification completion message in Login:', event.data);

        if (event.data.status === 'success') {
          toast.success("LinkedIn verification completed successfully! You can now log in.");
        } else if (event.data.status === 'error') {
          toast.error("LinkedIn verification failed. Please try again.");
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const onSubmit = async (values: FormValues) => {
    try {
      const result = await signIn(values.email, values.password);

      // Check if login failed due to email not confirmed
      if (result?.error) {
        if (result.error.message.includes('email') && result.error.message.includes('confirm')) {
          // Email not verified - show specific message
          ToastManager.auth.emailNotVerified(values.email);
        } else {
          // Other login errors
          ToastManager.auth.loginError(result.error.message);
        }
        return;
      }

      // Login was successful
      if (result?.data) {

        ToastManager.auth.loginSuccess();
      }
    } catch (error: any) {
      // Handle different types of errors
      if (error?.message?.includes('email') && error?.message?.includes('confirm')) {
        ToastManager.auth.emailNotVerified(values.email);
      } else {
        ToastManager.auth.loginError();
      }
    }
  };

  if (isLoading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  return (
    <div className="flex flex-col min-h-screen relative overflow-hidden">
      <PageBackground />
      
      <div className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-xl bg-opacity-95 backdrop-blur-sm border border-opacity-20">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-2 text-black">Welcome Back</h1>
            <p className="text-gray-600">Sign in to your CompanyVoice account</p>
          </div>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input 
                          placeholder="<EMAIL>" 
                          className="pl-10" 
                          {...field} 
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input 
                          type="password" 
                          placeholder="Enter your password" 
                          className="pl-10" 
                          {...field} 
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex items-center justify-end">
                <Link to="/forgot-password" className="text-sm text-black hover:text-black/90">
                  Forgot password?
                </Link>
              </div>
              
              <Button type="submit" className="w-full bg-black hover:bg-black/90 text-white">
                Sign In
              </Button>
              
              <div className="text-center text-gray-600">
                <p>Don't have an account?</p>
                <Link to="/signup" className="flex items-center justify-center text-black hover:text-black/90 mt-2">
                  <LogIn className="mr-2 h-4 w-4" /> Create Account
                </Link>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Login;
