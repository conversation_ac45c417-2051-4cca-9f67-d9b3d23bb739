import React from 'react';
import { Link } from 'react-router-dom';
import { PricingDialog } from '../pricing/PricingDialog';
const JiveFooter = () => {
  return <footer className="pt-16 pb-10 px-4 text-center py-[34px]">
      <div className="mb-6">
        <span className="text-3xl font-bold">CompanyVoice</span>
      </div>
      <p className="text-gray-600 mb-6">Transform employees into powerful brand ambassadors</p>
      
      <div className="flex justify-center gap-6 mb-6">
        <Link to="/best-practices" className="text-sm text-gray-600 hover:underline">Best Practices</Link>
        <PricingDialog trigger={<button className="text-sm text-gray-600 hover:underline">
              Pricing
            </button>} />
        <Link to="/terms" className="text-sm text-gray-600 hover:underline">Terms</Link>
        <Link to="#" className="text-sm text-gray-600 hover:underline">Contact</Link>
      </div>
      
      <p className="text-sm text-gray-500">© 2025 CompanyVoice Inc.</p>
    </footer>;
};
export default JiveFooter;