import { Router } from 'express';
import {
    handleGetLinkedInProfile,
    handleGetLinkedInPosts,
    handleGetLinkedInPost,
    handleGetLinkedInPostComments,
    handleGetLinkedInPostReactions
} from '../handlers/linkedin.handlers';
import {
    handleGetLatestLinkedInAccount
} from '../handlers/auth.handlers';

/**
 *  LinkedIn Routes
 */

const router = Router();

// LinkedIn account operations
router.get('/latest', handleGetLatestLinkedInAccount);           // GET /api/linkedin/latest

// LinkedIn profile operations
router.get('/profile/:userId', handleGetLinkedInProfile);        // GET /api/linkedin/profile/:userId

// LinkedIn posts operations
router.get('/posts/:userId', handleGetLinkedInPosts);            // GET /api/linkedin/posts/:userId
router.get('/post/:userId/:postId', handleGetLinkedInPost);      // GET /api/linkedin/post/:userId/:postId

// LinkedIn post engagement operations
router.get('/comments/:userId/:postId', handleGetLinkedInPostComments);   // GET /api/linkedin/comments/:userId/:postId
router.get('/reactions/:userId/:postId', handleGetLinkedInPostReactions); // GET /api/linkedin/reactions/:userId/:postId

export default router;
