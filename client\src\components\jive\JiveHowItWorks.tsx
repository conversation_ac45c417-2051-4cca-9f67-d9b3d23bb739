import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Lightbulb, Check, BarChart3 } from 'lucide-react';
const JiveHowItWorks = () => {
  return <section className="px-4 max-w-5xl mx-auto py-0">
      <h2 className="text-3xl sm:text-4xl font-bold text-center mb-2 py-0">Here's how it works</h2>
      <p className="text-center text-gray-600 mb-16">Simple, powerful employee advocacy</p>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
        {/* Step 1 */}
        <div className="text-center">
          <div className="w-16 h-16 bg-yellow-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <Lightbulb size={32} className="text-yellow-700" />
          </div>
          <h3 className="text-xl font-bold mb-3">Create on-brand content</h3>
          <p className="text-gray-600">Use AI to promote your company by generating engaging LinkedIn posts that align with your company's voice and messaging.</p>
        </div>
        
        {/* Step 2 */}
        <div className="text-center">
          <div className="w-16 h-16 bg-teal-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <Check size={32} className="text-teal-700" />
          </div>
          <h3 className="text-xl font-bold mb-3">Get quick approvals</h3>
          <p className="text-gray-600">
            Marketing teams can easily review and approve content, ensuring quality and compliance with guidelines.
          </p>
        </div>
        
        {/* Step 3 */}
        <div className="text-center">
          <div className="w-16 h-16 bg-purple-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <BarChart3 size={32} className="text-purple-700" />
          </div>
          <h3 className="text-xl font-bold mb-3">Measure and optimize</h3>
          <p className="text-gray-600">
            Track engagement metrics and analyze which content performs best to continuously improve your strategy.
          </p>
        </div>
      </div>
      
      <div className="text-center mt-16">
        <Link to="/signup">
          <Button className="rounded-full bg-black text-white hover:bg-black/90 py-6 px-8 text-lg">
            Amplify your brand
          </Button>
        </Link>
      </div>
    </section>;
};
export default JiveHowItWorks;