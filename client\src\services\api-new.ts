import { env } from '../config/env';

/**
 * New Functional API Service
 * Clean, specific API calls with clear separation of concerns
 */

class ApiService {
    private baseUrl: string;

    constructor() {
        this.baseUrl = env.SERVER_URL;
    }

    private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
        const url = `${this.baseUrl}${endpoint}`;
        
        console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`);
        
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
            ...options,
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`❌ API Error: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log(`✅ API Response:`, data);
        return data;
    }

    // ===== USER API METHODS =====

    /**
     * Get user by ID
     * GET /api/users/:userId
     */
    async getUser(userId: string) {
        return this.makeRequest(`/api/users/${userId}`);
    }

    /**
     * Get user by email
     * GET /api/users/email/:email
     */
    async getUserByEmail(email: string) {
        return this.makeRequest(`/api/users/email/${email}`);
    }

    /**
     * Update user
     * PUT /api/users/:userId
     */
    async updateUser(userId: string, updates: any) {
        return this.makeRequest(`/api/users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(updates),
        });
    }

    /**
     * Create user
     * POST /api/users
     */
    async createUser(userData: any) {
        return this.makeRequest('/api/users', {
            method: 'POST',
            body: JSON.stringify(userData),
        });
    }

    /**
     * Get user's LinkedIn account ID
     * GET /api/users/:userId/linkedin-account-id
     */
    async getUserLinkedInAccountId(userId: string) {
        return this.makeRequest(`/api/users/${userId}/linkedin-account-id`);
    }

    // ===== LINKEDIN API METHODS =====

    /**
     * Get LinkedIn profile for a user
     * GET /api/linkedin/profile/:userId
     */
    async getLinkedInProfile(userId: string) {
        return this.makeRequest(`/api/linkedin/profile/${userId}`);
    }

    /**
     * Get LinkedIn posts for a user
     * GET /api/linkedin/posts/:userId
     */
    async getLinkedInPosts(userId: string, limit: number = 50) {
        return this.makeRequest(`/api/linkedin/posts/${userId}?limit=${limit}`);
    }

    /**
     * Get specific LinkedIn post details
     * GET /api/linkedin/post/:userId/:postId
     */
    async getLinkedInPost(userId: string, postId: string) {
        return this.makeRequest(`/api/linkedin/post/${userId}/${postId}`);
    }

    /**
     * Get LinkedIn post comments
     * GET /api/linkedin/comments/:userId/:postId
     */
    async getLinkedInPostComments(userId: string, postId: string, limit: number = 50) {
        return this.makeRequest(`/api/linkedin/comments/${userId}/${postId}?limit=${limit}`);
    }

    /**
     * Get LinkedIn post reactions
     * GET /api/linkedin/reactions/:userId/:postId
     */
    async getLinkedInPostReactions(userId: string, postId: string, limit: number = 50) {
        return this.makeRequest(`/api/linkedin/reactions/${userId}/${postId}?limit=${limit}`);
    }

    // ===== ANALYTICS API METHODS =====

    /**
     * Get calculated analytics metrics for a user
     * GET /api/analytics/metrics/:userId
     */
    async getAnalyticsMetrics(userId: string) {
        return this.makeRequest(`/api/analytics/metrics/${userId}`);
    }

    /**
     * Sync analytics data for a user
     * POST /api/analytics/sync/:userId
     */
    async syncAnalytics(userId: string, forceSync: boolean = false) {
        return this.makeRequest(`/api/analytics/sync/${userId}`, {
            method: 'POST',
            body: JSON.stringify({ forceSync }),
        });
    }

    /**
     * Get dashboard data (combined user + analytics)
     * GET /api/analytics/dashboard/:userId
     */
    async getDashboardData(userId: string, linkedinAccountId?: string, linkedinUserId?: string) {
        let url = `/api/analytics/dashboard/${userId}`;
        const params = new URLSearchParams();
        
        if (linkedinAccountId) params.append('linkedinAccountId', linkedinAccountId);
        if (linkedinUserId) params.append('linkedinUserId', linkedinUserId);
        
        if (params.toString()) {
            url += `?${params.toString()}`;
        }
        
        return this.makeRequest(url);
    }

    /**
     * Track user action (for analytics)
     * POST /api/analytics/track
     */
    async trackAction(actionData: {
        action: string;
        userId: string;
        sessionId?: string;
        timestamp?: string;
        [key: string]: any;
    }) {
        return this.makeRequest('/api/analytics/track', {
            method: 'POST',
            body: JSON.stringify({
                ...actionData,
                timestamp: actionData.timestamp || new Date().toISOString(),
            }),
        });
    }

    /**
     * Get onboarding metrics
     * GET /api/analytics/onboarding/metrics
     */
    async getOnboardingMetrics() {
        return this.makeRequest('/api/analytics/onboarding/metrics');
    }

    /**
     * Get user journey
     * GET /api/analytics/journey/:sessionId
     */
    async getUserJourney(sessionId: string) {
        return this.makeRequest(`/api/analytics/journey/${sessionId}`);
    }

    // ===== COMPANY API METHODS =====

    /**
     * Store company domain
     * POST /api/companies/domain
     */
    async storeCompanyDomain(domain: string, userId?: string) {
        return this.makeRequest('/api/companies/domain', {
            method: 'POST',
            body: JSON.stringify({
                domain: domain.toLowerCase(),
                userId,
                createdAt: new Date().toISOString()
            }),
        });
    }

    /**
     * Get company by domain
     * GET /api/companies/domain/:domain
     */
    async getCompanyByDomain(domain: string) {
        return this.makeRequest(`/api/companies/domain/${domain.toLowerCase()}`);
    }
}

// Export singleton instance
export const apiService = new ApiService();

// Export individual methods for easier importing
export const {
    // User methods
    getUser,
    getUserByEmail,
    updateUser,
    createUser,
    getUserLinkedInAccountId,
    
    // LinkedIn methods
    getLinkedInProfile,
    getLinkedInPosts,
    getLinkedInPost,
    getLinkedInPostComments,
    getLinkedInPostReactions,
    
    // Analytics methods
    getAnalyticsMetrics,
    syncAnalytics,
    getDashboardData,
    trackAction,
    getOnboardingMetrics,
    getUserJourney,

    // Company methods
    storeCompanyDomain,
    getCompanyByDomain,
} = apiService;
