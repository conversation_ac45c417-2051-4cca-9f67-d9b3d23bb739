
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp, Users, Bar<PERSON><PERSON>, FileText, Calendar, Award } from "lucide-react";

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  delta?: {
    value: string;
    positive: boolean;
  };
}

const StatCard = ({ title, value, icon, delta }: StatCardProps) => (
  <Card className="border-t-[1px] border-t-enterprise-blue bg-white/60 backdrop-blur-sm hover:shadow-md transition-all duration-200 animate-fade-in">
    <CardContent className="p-3 flex justify-between items-center">
      <div>
        <p className="text-xs font-medium text-enterprise-gray-600 mb-1">{title}</p>
        <h3 className="text-lg font-bold leading-tight">{value}</h3>
        {delta && (
          <p className={`text-xs leading-tight mt-1 ${delta.positive ? 'text-green-600' : 'text-red-600'}`}>
            {delta.value}
          </p>
        )}
      </div>
      <div className="p-2 rounded-full bg-gray-50/80">
        {React.cloneElement(icon as React.ReactElement, { 
          className: "h-6 w-6",
          strokeWidth: 1.5 
        })}
      </div>
    </CardContent>
  </Card>
);

export const AdminOverviewCards = () => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-6">
      <StatCard
        title="Total Impressions"
        value="1.2M"
        icon={<TrendingUp className="text-enterprise-blue" />}
        delta={{
          value: "+8% from last month",
          positive: true
        }}
      />
      <StatCard
        title="Avg Impressions/User"
        value="12K"
        icon={<Users className="text-enterprise-teal" />}
        delta={{
          value: "+5% MoM",
          positive: true
        }}
      />
      <StatCard
        title="Total Posts This Month"
        value="450"
        icon={<FileText className="text-purple-500" />}
        delta={{
          value: "+15% MoM",
          positive: true
        }}
      />
      <StatCard
        title="Avg Posts/User/Week"
        value="1.1"
        icon={<Calendar className="text-green-500" />}
        delta={{
          value: "+3% MoM",
          positive: true
        }}
      />
      <StatCard
        title="Avg Engagement Rate"
        value="3.4%"
        icon={<BarChart className="text-amber-500" />}
        delta={{
          value: "+1.2% MoM",
          positive: true
        }}
      />
      <StatCard
        title="Top Post Reach"
        value="68K"
        icon={<Award className="text-rose-500" />}
        delta={{
          value: "+22% MoM",
          positive: true
        }}
      />
    </div>
  );
};
