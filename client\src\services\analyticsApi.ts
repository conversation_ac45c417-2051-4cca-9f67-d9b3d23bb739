/**
 * Analytics API Service
 * Handles communication with the backend analytics endpoints
 */

import { getApiUrl } from '@/config/env';
import { CloudCog } from 'lucide-react';

export interface AnalyticsData {
  totalImpressions: number;
  impressionsPerPost: number;
  impressionsPerFollower: number;
  likesPerPost: number;
  postsPerWeek: number;
  engagementRate: number;
  profile: {
    follower_count: number;
    connection_count: number;
    profile_views: number;
  };
  posts: LinkedInPost[];
}

export interface LinkedInPost {
  id: string;
  social_id?: string; // URN format like "urn:li:activity:7324767572325810176"
  text: string;
  created_at: string;
  impressions: number;
  likes: number;
  comments: number;
  shares: number;
  engagement_rate: number;
  attachments_links?: string[]; // Array of attachment URLs
}

export interface DashboardData {
  user: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    linkedin_connection_status: string;
    linkedin_account_id?: string;
  };
  analytics: AnalyticsData;
  isRealTime: boolean;
  last_sync: string | null;
}

class AnalyticsApiService {
  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = getApiUrl(`/analytics${endpoint}`);
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  /**
   * Get dashboard data for a user
   */
  async getDashboardData(userId: string, linkedinAccountId?: string, linkedinUserId?: string): Promise<DashboardData> {
    try {
      console.log('=== FRONTEND DASHBOARD REQUEST ===');
      console.log('🔍 Fetching dashboard data for user:', userId);
      console.log('🔗 LinkedIn account ID:', linkedinAccountId);
      console.log('🔗 LinkedIn user ID:', linkedinUserId);

      // Build URL with optional LinkedIn parameters
      let url = `/dashboard/${userId}`;
      const params = new URLSearchParams();
      if (linkedinAccountId) params.append('linkedinAccountId', linkedinAccountId);
      if (linkedinUserId) params.append('linkedinUserId', linkedinUserId);
      if (params.toString()) url += `?${params.toString()}`;

      console.log('📡 API URL:', getApiUrl(`/analytics${url}`));

      const response = await this.makeRequest(url);

      console.log('📊 Dashboard API response:', response);

      if (!response.success) {
        console.error('❌ Dashboard API error:', response.error);
        throw new Error(response.error || 'Failed to fetch dashboard data');
      }

      console.log('✅ Dashboard data received successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('💥 Error fetching dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get LinkedIn analytics for a user
   */
  async getLinkedInAnalytics(userId: string): Promise<AnalyticsData> {
    try {
      console.log('Fetching LinkedIn analytics for user:', userId);
      const response = await this.makeRequest(`/${userId}`);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch analytics data');
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching LinkedIn analytics:', error);
      throw error;
    }
  }

  /**
   * Get LinkedIn posts for a user
   */
  async getLinkedInPosts(userId: string, limit: number = 50): Promise<LinkedInPost[]> {
    try {
      console.log('Fetching LinkedIn posts for user:', userId);
      const response = await this.makeRequest(`/linkedin/posts/${userId}?limit=${limit}`);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch LinkedIn posts');
      }

      return response.data.items || response.data;
    } catch (error) {
      console.error('Error fetching LinkedIn posts:', error);
      throw error;
    }
  }

  /**
   * Get specific LinkedIn post details
   */
  async getLinkedInPost(userId: string, postId: string): Promise<LinkedInPost> {
    try {
      console.log('Fetching LinkedIn post details:', { userId, postId });
      const response = await this.makeRequest(`/linkedin/post/${userId}/${postId}`);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch LinkedIn post details');
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching LinkedIn post details:', error);
      throw error;
    }
  }

  /**
   * Sync analytics data for a user (Functional approach - requires LinkedIn account ID)
   */
  async syncAnalytics(userId: string, linkedinAccountId?: string): Promise<any> {
    try {
      console.log('=== FRONTEND SYNC REQUEST ===');
      console.log('🔄 Syncing analytics for user:', userId);
      console.log('🔗 LinkedIn Account ID:', linkedinAccountId);
      console.log('📡 API URL:', getApiUrl(`/analytics/sync/${userId}`));

      if (!linkedinAccountId) {
        throw new Error('LinkedIn account ID is required for analytics sync');
      }

      const response = await this.makeRequest(`/sync/${userId}`, {
        method: 'POST',
        body: JSON.stringify({
          linkedinAccountId: linkedinAccountId
        }),
      });

      console.log('🔄 Sync API response:', response);

      if (!response.success) {
        console.error('❌ Sync API error:', response.error);
        throw new Error(response.error || 'Failed to sync analytics data');
      }

      console.log('✅ Analytics synced successfully');
      return response.data; // Return the fresh analytics data
    } catch (error) {
      console.error('💥 Error syncing analytics:', error);
      throw error;
    }
  }

  /**
   * Initialize analytics tracking for a user
   */
  async initializeAnalytics(userId: string, linkedinAccountId: string): Promise<void> {
    try {
      console.log('Initializing analytics for user:', userId);
      const response = await this.makeRequest('/initialize', {
        method: 'POST',
        body: JSON.stringify({
          userId,
          linkedinAccountId,
        }),
      });
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to initialize analytics');
      }

      console.log('Analytics initialized successfully');
    } catch (error) {
      console.error('Error initializing analytics:', error);
      throw error;
    }
  }

  /**
   * Get LinkedIn profile data for a user
   */
  async getLinkedInProfile(userId: string): Promise<any> {
    try {
      console.log('Fetching LinkedIn profile for user:', userId);
      const response = await this.makeRequest(`/profile/${userId}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch LinkedIn profile');
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching LinkedIn profile:', error);
      throw error;
    }
  }

  /**
   * Get LinkedIn post comments using official Unipile API
   * Note: postId should be social_id (URN format) for proper API calls
   */
  async getLinkedInPostComments(userId: string, postId: string, limit: number = 50): Promise<any> {
    try {
      // console.log('🔍 Fetching LinkedIn post comments:', { userId, postId, limit });
      // console.log('📝 Note: postId should be social_id (URN format) like "urn:li:activity:7324767572325810176"');

      const response = await this.makeRequest(`/linkedin/post/${userId}/${postId}/comments?limit=${limit}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch LinkedIn post comments');
      }

      return response.data;
    } catch (error) {
      console.error('❌ Error fetching LinkedIn post comments:', error);
      throw error;
    }
  }

  /**
   * Get LinkedIn post reactions using official Unipile API
   * Note: postId should be social_id (URN format) for proper API calls
   */
  async getLinkedInPostReactions(userId: string, postId: string, limit: number = 50): Promise<any> {
    try {
      console.log('🔍 Fetching LinkedIn post reactions:', { userId, postId, limit });
      console.log('📝 Note: postId should be social_id (URN format) like "urn:li:activity:7324767572325810176"');

      const response = await this.makeRequest(`/linkedin/post/${userId}/${postId}/reactions?limit=${limit}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch LinkedIn post reactions');
      }

      return response.data;
    } catch (error) {
      console.error('❌ Error fetching LinkedIn post reactions:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive post data (post details + comments + reactions) with detailed step-by-step logging
   */
  async getComprehensivePostData(postId: string, accountId: string): Promise<any> {
    const startTime = Date.now();
    console.log(`🚀 [${new Date().toISOString()}] ===== FRONTEND: COMPREHENSIVE POST DATA FETCH STARTED =====`);
    console.log(`📊 Post ID: ${postId}`);
    console.log(`🔗 Account ID: ${accountId}`);
    console.log(`⏰ Frontend start time: ${new Date().toISOString()}`);

    try {
      console.log(`🔄 [${new Date().toISOString()}] Making API request to backend...`);

      const response = await this.makeRequest(`/posts/${postId}/comprehensive?accountId=${accountId}`);

      if (!response.success) {
        console.error(`❌ API request failed: ${response.error}`);
        throw new Error(response.error || 'Failed to fetch comprehensive post data');
      }

      const duration = Date.now() - startTime;

      console.log(`✅ [${new Date().toISOString()}] Comprehensive post data received successfully (${duration}ms)`);
      console.log(`📊 Frontend summary:`);
      console.log(`  - Post: "${response.data?.post?.text?.substring(0, 50)}..."`);
      console.log(`  - Author: ${response.data?.post?.author?.name} (${response.data?.post?.author?.is_company ? 'Company' : 'Individual'})`);
      console.log(`  - Published: ${response.data?.post?.formatted_date?.readable || 'N/A'}`);
      console.log(`  - Impressions: ${response.data?.post?.impressions_counter || 0}`);
      console.log(`  - Comments: ${response.data?.metadata?.total_comments || 0}`);
      console.log(`  - Reactions: ${response.data?.metadata?.total_reactions || 0}`);
      console.log(`  - Backend fetch duration: ${response.data?.metadata?.fetch_duration_ms || 0}ms`);
      console.log(`  - Total frontend duration: ${duration}ms`);

      // Log reaction breakdown
      if (response.data?.reactions?.breakdown) {
        console.log(`👍 Reaction types found:`);
        Object.entries(response.data.reactions.breakdown).forEach(([type, info]: [string, any]) => {
          console.log(`  ${type}: ${info.count} reactions`);
        });
      }

      // Log comment authors
      if (response.data?.comments?.items && response.data.comments.items.length > 0) {
        console.log(`📝 Comment authors:`);
        response.data.comments.items.slice(0, 5).forEach((comment: any, index: number) => {
          console.log(`  ${index + 1}. ${comment.author?.name} (${comment.author?.is_company ? 'Company' : 'Individual'})`);
        });
        if (response.data.comments.items.length > 5) {
          console.log(`  ... and ${response.data.comments.items.length - 5} more comments`);
        }
      }

      console.log(`🎉 [${new Date().toISOString()}] ===== FRONTEND: COMPREHENSIVE POST DATA FETCH COMPLETED =====`);

      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ [${new Date().toISOString()}] ===== FRONTEND: COMPREHENSIVE POST DATA FETCH FAILED =====`);
      console.error(`⏱️ Failed after: ${duration}ms`);
      console.error(`❌ Error: ${error}`);
      throw error;
    }
  }
}

// Export singleton instance
export const analyticsApi = new AnalyticsApiService();

// Empty analytics data for initialization (no mock data)
export const emptyAnalyticsData: AnalyticsData = {
  totalImpressions: 0,
  impressionsPerPost: 0,
  impressionsPerFollower: 0,
  likesPerPost: 0,
  postsPerWeek: 0,
  engagementRate: 0,
  profile: {
    follower_count: 0,
    connection_count: 0,
    profile_views: 0,
  },
  posts: [],
};

export default analyticsApi;
