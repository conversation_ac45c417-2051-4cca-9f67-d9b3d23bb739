
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Trash2, Send, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Skeleton } from '@/components/ui/skeleton';
import { Post } from '@/types/post';
import { useLinkedInConnection } from '@/hooks/useLinkedInConnection';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const SavedPostsTable = () => {
  const [savedPosts, setSavedPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [postToDelete, setPostToDelete] = useState<string | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { isLinkedInConnected, isLoading: isLinkedInLoading, isInitialized } = useLinkedInConnection();

  useEffect(() => {
    fetchSavedPosts();
    
    // Set up realtime subscription for post updates
    const channel = supabase
      .channel('saved-posts-updates')
      .on(
        'postgres_changes',
        { 
          event: '*', 
          schema: 'public', 
          table: 'employee_posts',
          filter: `user_id=eq.${user?.id}` 
        },
        () => {
          fetchSavedPosts();
        }
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
    };
  }, [user?.id]);

  const fetchSavedPosts = async () => {
    if (!user) return;
    
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('employee_posts')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'draft')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      setSavedPosts(data as Post[]);
    } catch (error) {
      console.error('Error fetching saved posts:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load your saved posts."
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditPost = (postId: string) => {
    // In a real implementation, this would navigate to an edit page
    // For now, we'll just show a toast
    toast({
      title: "Edit post",
      description: "This feature is coming soon."
    });
  };

  const handlePublishPost = async (post: Post) => {
    try {
      const newStatus = post.post_type === 'company' ? 'pending_approval' : 'published';
      
      const { error } = await supabase
        .from('employee_posts')
        .update({ status: newStatus })
        .eq('id', post.id);
      
      if (error) throw error;
      
      toast({
        title: newStatus === 'pending_approval' ? "Submitted for approval" : "Post published",
        description: newStatus === 'pending_approval' 
          ? "Your post has been submitted for approval."
          : "Your post has been published successfully."
      });
      
      fetchSavedPosts();
    } catch (error) {
      console.error('Error publishing post:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to publish your post."
      });
    }
  };

  const confirmDeletePost = (postId: string) => {
    setPostToDelete(postId);
  };
  
  const handleDeletePost = async () => {
    if (!postToDelete) return;
    
    try {
      const { error } = await supabase
        .from('employee_posts')
        .delete()
        .eq('id', postToDelete);
      
      if (error) throw error;
      
      toast({
        title: "Post deleted",
        description: "Your saved post has been deleted."
      });
      
      fetchSavedPosts();
    } catch (error) {
      console.error('Error deleting post:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete your post."
      });
    } finally {
      setPostToDelete(null);
    }
  };

  return (
    <Card className="mt-8">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl flex items-center gap-2">
            <FileText className="h-5 w-5 text-enterprise-teal" />
            Your Generated and Saved Posts
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        ) : savedPosts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>You don't have any saved posts yet.</p>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    className="mt-4 disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => navigate('/content-creator')}
                    disabled={!isLinkedInConnected || isLinkedInLoading}
                  >
                    Create a post
                  </Button>
                </TooltipTrigger>
                {(!isLinkedInConnected && !isLinkedInLoading) && (
                  <TooltipContent>
                    <p>Connect your LinkedIn account to create posts</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium">Post</th>
                  <th className="text-center py-3 px-4 font-medium">Created</th>
                  <th className="text-center py-3 px-4 font-medium">Type</th>
                  <th className="text-center py-3 px-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {savedPosts.map((post) => (
                  <tr key={post.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="font-medium">{post.title}</div>
                      <div className="text-enterprise-gray-600 text-xs line-clamp-1">
                        {post.content.substring(0, 100)}...
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-enterprise-gray-600">
                      {new Date(post.created_at).toLocaleDateString()}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span 
                        className={`px-2 py-1 text-xs rounded-full ${
                          post.post_type === 'personal' 
                            ? 'bg-purple-100 text-purple-800' 
                            : 'bg-blue-100 text-blue-800'
                        }`}
                      >
                        {post.post_type}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex justify-center space-x-2">
                        <Button variant="ghost" size="sm" onClick={() => handleEditPost(post.id)}>
                          <Edit size={16} className="text-enterprise-gray-600" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handlePublishPost(post)}
                          className="text-enterprise-teal"
                        >
                          <Send size={16} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => confirmDeletePost(post.id)}
                          className="text-red-500"
                        >
                          <Trash2 size={16} />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={!!postToDelete} onOpenChange={() => postToDelete && setPostToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this saved post. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeletePost} className="bg-red-500 hover:bg-red-600">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

export default SavedPostsTable;
