
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface LinkedInPostExample {
  id: string;
  subject: string;
  content: string;
}

interface LinkedInExampleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentExample: LinkedInPostExample | null;
  isEditing: boolean;
  onSave: () => void;
  onSubjectChange: (value: string) => void;
  onContentChange: (value: string) => void;
}

const LinkedInExampleDialog = ({
  open,
  onOpenChange,
  currentExample,
  isEditing,
  onSave,
  onSubjectChange,
  onContentChange
}: LinkedInExampleDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit LinkedIn Post Example" : "Add LinkedIn Post Example"}</DialogTitle>
          <DialogDescription>
            Create examples of ideal LinkedIn posts to reference when generating new content.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="post-subject">Subject Line</Label>
            <Input 
              id="post-subject" 
              placeholder="e.g., Product Launch, Industry Insight"
              value={currentExample?.subject || ''}
              onChange={(e) => onSubjectChange(e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              A brief description of what the post is about
            </p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="post-content">Post Content</Label>
            <Textarea 
              id="post-content" 
              placeholder="Enter the LinkedIn post content here..."
              rows={6}
              value={currentExample?.content || ''}
              onChange={(e) => onContentChange(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={onSave}
            className="bg-enterprise-blue hover:bg-enterprise-blue/90"
          >
            {isEditing ? "Update Example" : "Add Example"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LinkedInExampleDialog;
