// Utility functions for drawing background elements

// Interface for a squiggle element
export interface Squiggle {
  x: number;
  y: number;
  amplitude: number;
  frequency: number;
  speed: number;
  width: number;
  length: number;
  offset: number;
  area: { x: number; y: number; width: number; height: number };
}

// Interface for a blob element
export interface Blob {
  x: number;
  y: number;
  radius: number;
  xSpeed: number;
  ySpeed: number;
  amplitude: number;
  frequency: number;
  phase: number;
  area: { x: number; y: number; width: number; height: number };
}

// Interface for an emoji element
export interface Emoji {
  sprite: number;
  x: number;
  y: number;
  size: number;
  angle: number;
  rotationSpeed: number;
  rotationAmplitude: number;
  baseAngle: number;
}

// Emoji sprite position information
export const emojiPositions = [
  { x: 0, y: 0, width: 61, height: 61 },    // Thumbs up (blue)
  { x: 61, y: 0, width: 61, height: 61 },   // Clapping hands (green)
  { x: 122, y: 0, width: 61, height: 61 },  // Heart (purple)
  { x: 183, y: 0, width: 61, height: 61 },  // Heart (red)
  { x: 244, y: 0, width: 61, height: 61 },  // Lightbulb (yellow)
  { x: 305, y: 0, width: 61, height: 61 },  // Smile (blue)
];

// Draw squiggles on the canvas
export const drawSquiggles = (
  ctx: CanvasRenderingContext2D,
  squiggles: Squiggle[],
  time: number
) => {
  squiggles.forEach((squiggle) => {
    ctx.beginPath();
    ctx.strokeStyle = '#2563eb';
    ctx.lineWidth = squiggle.width;
    ctx.globalAlpha = 0.25; // Reduced opacity from 0.4 to 0.25
    
    for (let i = 0; i < squiggle.length; i += 5) {
      const x = squiggle.x + i;
      const y = squiggle.y + Math.sin((time * squiggle.speed + i) * squiggle.frequency + squiggle.offset) * squiggle.amplitude;
      
      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    
    ctx.stroke();

    // Move squiggle within its designated area
    squiggle.x -= squiggle.speed;
    if (squiggle.x + squiggle.length < squiggle.area.x) {
      squiggle.x = squiggle.area.x + squiggle.area.width;
      squiggle.y = squiggle.area.y + Math.random() * squiggle.area.height;
    }
  });
};

// Draw blobs on the canvas
export const drawBlobs = (
  ctx: CanvasRenderingContext2D,
  blobs: Blob[],
  time: number
) => {
  blobs.forEach((blob) => {
    ctx.beginPath();
    
    // Create a pulsating effect
    const pulsatingRadius = blob.radius + Math.sin(time * blob.frequency + blob.phase) * blob.amplitude;
    
    // Draw the blob
    ctx.fillStyle = '#2563eb';
    ctx.globalAlpha = 0.15; // Reduced opacity from 0.2 to 0.15
    ctx.arc(blob.x, blob.y, pulsatingRadius, 0, Math.PI * 2);
    ctx.fill();

    // Move blob within its designated area
    blob.x += blob.xSpeed;
    blob.y += blob.ySpeed;

    // Bounce off area boundaries
    const { x, y, width, height } = blob.area;
    if (blob.x - pulsatingRadius < x) {
      blob.x = x + pulsatingRadius;
      blob.xSpeed = Math.abs(blob.xSpeed);
    }
    if (blob.x + pulsatingRadius > x + width) {
      blob.x = x + width - pulsatingRadius;
      blob.xSpeed = -Math.abs(blob.xSpeed);
    }
    if (blob.y - pulsatingRadius < y) {
      blob.y = y + pulsatingRadius;
      blob.ySpeed = Math.abs(blob.ySpeed);
    }
    if (blob.y + pulsatingRadius > y + height) {
      blob.y = y + height - pulsatingRadius;
      blob.ySpeed = -Math.abs(blob.ySpeed);
    }
  });
};

// Create a fallback emoji if the image fails to load
export const createFallbackEmoji = (
  ctx: CanvasRenderingContext2D, 
  x: number, 
  y: number, 
  size: number, 
  rotation: number, 
  index: number
) => {
  const colors = ['#4267B2', '#34A853', '#A020F0', '#E41B17', '#FFD700', '#1DA1F2']; // LinkedIn-like colors
  
  ctx.save();
  ctx.translate(x, y);
  ctx.rotate(rotation);
  
  // Draw a circle with the color based on index
  ctx.beginPath();
  ctx.fillStyle = colors[index % colors.length];
  ctx.arc(0, 0, size/2, 0, Math.PI * 2);
  ctx.fill();
  
  // Add simple face to make it look like an emoji
  ctx.fillStyle = '#FFFFFF';
  ctx.beginPath();
  ctx.arc(-size/6, -size/8, size/10, 0, Math.PI * 2); // Left eye
  ctx.arc(size/6, -size/8, size/10, 0, Math.PI * 2); // Right eye
  ctx.fill();
  
  // Add smile
  ctx.beginPath();
  ctx.strokeStyle = '#FFFFFF';
  ctx.lineWidth = size/15;
  ctx.arc(0, size/8, size/5, 0, Math.PI);
  ctx.stroke();
  
  ctx.restore();
};

// Draw emojis on the canvas
export const drawEmojis = (
  ctx: CanvasRenderingContext2D,
  emojis: Emoji[],
  time: number,
  emojiImageLoaded: boolean,
  linkedInEmojiImage: HTMLImageElement
) => {
  ctx.globalAlpha = 0.9; // Make emojis more visible
  
  emojis.forEach((emoji) => {
    // Calculate oscillating rotation
    const rotation = Math.sin(time * emoji.rotationSpeed + emoji.baseAngle) * emoji.rotationAmplitude;
    
    if (emojiImageLoaded) {
      // Use the sprite sheet if loaded
      const position = emojiPositions[emoji.sprite];
      
      // Save the current state
      ctx.save();
      
      // Move to the emoji's position, then rotate around that point
      ctx.translate(emoji.x, emoji.y);
      ctx.rotate(rotation);
      
      // Draw the emoji (centered)
      ctx.drawImage(
        linkedInEmojiImage, 
        position.x, position.y, position.width, position.height,
        -emoji.size/2, -emoji.size/2, emoji.size, emoji.size
      );
      
      // Restore the context
      ctx.restore();
    } else {
      // Use fallback emojis if image didn't load
      createFallbackEmoji(ctx, emoji.x, emoji.y, emoji.size, rotation, emoji.sprite);
    }
  });
  
  ctx.globalAlpha = 1.0;
};

// Initialize canvas areas
export const initializeAreas = (canvasWidth: number, canvasHeight: number) => {
  return [
    { x: canvasWidth * 0.1, y: canvasHeight * 0.1, width: canvasWidth * 0.3, height: canvasHeight * 0.3 },
    { x: canvasWidth * 0.6, y: canvasHeight * 0.1, width: canvasWidth * 0.3, height: canvasHeight * 0.3 },
    { x: canvasWidth * 0.2, y: canvasHeight * 0.6, width: canvasWidth * 0.3, height: canvasHeight * 0.3 },
    { x: canvasWidth * 0.7, y: canvasHeight * 0.6, width: canvasWidth * 0.25, height: canvasHeight * 0.3 }
  ];
};

// Initialize squiggles in designated areas
export const initializeSquiggles = (areas: { x: number; y: number; width: number; height: number }[]) => {
  const squiggles: Squiggle[] = [];
  
  for (let i = 0; i < 3; i++) {
    const area = areas[i % areas.length];
    squiggles.push({
      x: area.x + Math.random() * area.width,
      y: area.y + Math.random() * area.height,
      amplitude: 10 + Math.random() * 15, // Reduced amplitude from 20-50 to 10-25
      frequency: 0.01 + Math.random() * 0.02,
      speed: 0.3 + Math.random() * 1, // Reduced speed from 0.5-2.5 to 0.3-1.3
      width: 1 + Math.random() * 2, // Thinner lines from 2-6 to 1-3
      length: 100 + Math.random() * 150, // Shorter length from 200-500 to 100-250
      offset: Math.random() * Math.PI * 2,
      area: area
    });
  }
  
  return squiggles;
};

// Initialize blobs in designated areas
export const initializeBlobs = (areas: { x: number; y: number; width: number; height: number }[]) => {
  const blobs: Blob[] = [];
  
  for (let i = 0; i < 4; i++) {
    const area = areas[i % areas.length];
    blobs.push({
      x: area.x + Math.random() * area.width,
      y: area.y + Math.random() * area.height,
      radius: 20 + Math.random() * 40, // Smaller blobs from 40-140 to 20-60
      xSpeed: (Math.random() - 0.5) * 0.8, // Slower movement from ±1.5 to ±0.8
      ySpeed: (Math.random() - 0.5) * 0.8,
      amplitude: 3 + Math.random() * 10, // Less pulsating from 5-25 to 3-13
      frequency: 0.001 + Math.random() * 0.003, // Slower pulsating
      phase: Math.random() * Math.PI * 2,
      area: area
    });
  }
  
  return blobs;
};

// Initialize emojis in fixed positions
export const initializeEmojis = (canvasWidth: number, canvasHeight: number) => {
  const emojis: Emoji[] = [];
  
  // Position emojis in fixed locations around the page with some spacing
  const emojiLocations = [
    { x: canvasWidth * 0.15, y: canvasHeight * 0.2 },
    { x: canvasWidth * 0.85, y: canvasHeight * 0.15 },
    { x: canvasWidth * 0.25, y: canvasHeight * 0.85 },
    { x: canvasWidth * 0.8, y: canvasHeight * 0.75 },
    { x: canvasWidth * 0.5, y: canvasHeight * 0.3 },
    { x: canvasWidth * 0.65, y: canvasHeight * 0.5 }
  ];
  
  for (let i = 0; i < emojiPositions.length; i++) {
    const location = emojiLocations[i % emojiLocations.length];
    
    emojis.push({
      sprite: i, // Each emoji gets its own sprite
      x: location.x,
      y: location.y,
      size: 45 + Math.random() * 20, // Size between 45-65px
      angle: Math.random() * Math.PI * 0.1, // Initial slight angle
      rotationSpeed: 0.002 + Math.random() * 0.003, // How fast it rotates
      rotationAmplitude: Math.PI * 0.05 + Math.random() * Math.PI * 0.05, // How far it rotates (5-10 degrees)
      baseAngle: Math.random() * Math.PI * 2 // Reference angle for oscillation
    });
  }
  
  return emojis;
};
