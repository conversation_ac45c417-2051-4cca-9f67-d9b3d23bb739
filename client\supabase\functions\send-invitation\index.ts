
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { Resend } from "https://esm.sh/resend@2.0.0";

// Set up CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Create a Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const resendApiKey = Deno.env.get('RESEND_API_KEY');

const supabase = createClient(supabaseUrl!, supabaseServiceKey!);
const resend = new Resend(resendApiKey);

function generateToken() {
  // Generate a random token using crypto
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

async function sendInvitationEmail(email: string, token: string, companyName: string, role: string) {
  const inviteLink = `${Deno.env.get('SITE_URL') || 'https://companyvoice.app'}/accept-invite?token=${token}`;
  
  try {
    const { data, error } = await resend.emails.send({
      from: 'CompanyVoice <<EMAIL>>',
      to: [email],
      subject: `You've been invited to join ${companyName} on CompanyVoice`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>You've been invited to join ${companyName}</h2>
          <p>You've been invited to join ${companyName} as a ${role} on CompanyVoice, the platform for employee advocacy.</p>
          <p>Click the button below to accept the invitation and create your account:</p>
          <a href="${inviteLink}" style="display: inline-block; background-color: #0066FF; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin-top: 16px; margin-bottom: 16px;">
            Accept Invitation
          </a>
          <p>This invitation will expire in 7 days.</p>
          <p>If you have any questions, please contact your company administrator.</p>
          <hr style="border: 0; height: 1px; background-color: #eaeaea; margin: 20px 0;">
          <p style="font-size: 12px; color: #666;">If you were not expecting this invitation, you can safely ignore this email.</p>
        </div>
      `,
    });

    if (error) {
      console.error("Error sending email:", error);
      return { success: false, error: error.message };
    }

    return { success: true, messageId: data?.id };
  } catch (error) {
    console.error("Exception sending email:", error);
    return { success: false, error: error.message };
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders, status: 200 });
  }

  try {
    if (req.method === 'POST') {
      const body = await req.json();
      const { invitations, companyId, companyName, invitedBy } = body;

      if (!Array.isArray(invitations) || !companyId || !companyName) {
        return new Response(
          JSON.stringify({ error: "Missing required parameters" }),
          { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
        );
      }

      // Process invitations in batches to prevent timeouts
      const results = [];
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now

      for (const invitation of invitations) {
        const { email, role, department, jobTitle } = invitation;
        const token = generateToken();

        // Create invitation record in database
        const { data: inviteData, error: inviteError } = await supabase
          .from('invitations')
          .insert({
            email,
            company_id: companyId,
            role: role || 'user',
            department,
            job_title: jobTitle,
            invited_by: invitedBy,
            token,
            expires_at: expiresAt.toISOString(),
            status: 'pending'
          })
          .select('id')
          .single();

        if (inviteError) {
          // Check if it's a duplicate error
          if (inviteError.message.includes('unique constraint') || inviteError.message.includes('duplicate key')) {
            results.push({
              email,
              success: false,
              error: 'User already invited',
            });
            continue;
          }
          
          results.push({
            email,
            success: false,
            error: inviteError.message,
          });
          continue;
        }

        // Send invitation email
        const emailResult = await sendInvitationEmail(email, token, companyName, role || 'user');
        
        if (!emailResult.success) {
          // If email fails, delete the invitation
          await supabase
            .from('invitations')
            .delete()
            .eq('id', inviteData.id);
            
          results.push({
            email,
            success: false,
            error: `Failed to send email: ${emailResult.error}`,
          });
          continue;
        }
        
        results.push({
          email,
          success: true,
          invitationId: inviteData.id,
        });
      }

      return new Response(
        JSON.stringify({ results }),
        { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { status: 405, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error processing request:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error", details: error.message }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }
});
