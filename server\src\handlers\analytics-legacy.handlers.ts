import { Request, Response } from 'express';
import { unipileService } from '../services/unipile/index';
import { getUserById, getAnalytics, updateAnalytics, initializeAnalytics } from '../services/user.service';

interface UserAction {
  action: string;
  data?: Record<string, any>;
  timestamp?: string;
  userId?: string;
  sessionId?: string;
}

/**
 * Functional Analytics Handlers (Legacy Support)
 * Pure functions for handling analytics-related HTTP requests
 * These handlers support the existing analytics routes
 */

/**
 * Extract LinkedIn user ID from LinkedIn URL
 * Utility function for LinkedIn user ID extraction
 */
const extractLinkedInUserIdFromUrl = (linkedinUrl?: string): string | null => {
    if (!linkedinUrl) return null;
    // For now, we'll use 'me' as the identifier since we need the actual LinkedIn user ID
    // In a real implementation, you would need to get this from the LinkedIn profile data
    // or store it during the connection process
    return 'me';
};

/**
 * Track user action for onboarding analytics
 * POST /analytics/track
 */
export const handleTrackAction = async (req: Request, res: Response): Promise<void> => {
    try {
        const action: UserAction = req.body;

        if (!action.action) {
            res.status(400).json({
                success: false,
                error: 'Action name is required'
            });
            return;
        }

        // Add timestamp if not provided
        if (!action.timestamp) {
            action.timestamp = new Date().toISOString();
        }

        // Actions are now stored directly in database
        console.log('📊 User action tracked (stored in database):', action);

        res.status(200).json({
            success: true,
            message: 'Action tracked successfully'
        });

    } catch (error: any) {
        console.error('❌ Error tracking user action:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to track action',
            details: error.message
        });
    }
};

/**
 * Get onboarding metrics (from database)
 * GET /analytics/onboarding/metrics
 */
export const handleGetOnboardingMetrics = async (req: Request, res: Response): Promise<void> => {
    try {
        // Metrics are now retrieved from database
        const metrics = {
            totalSignups: 0,
            completedOnboarding: 0,
            linkedinConnections: 0,
            conversionRate: 0,
            dropoffPoints: {
                signup_form: 0,
                linkedin_onboarding: 0,
                onboarding_completion: 0
            }
        };

        res.status(200).json({
            success: true,
            metrics
        });

    } catch (error: any) {
        console.error('❌ Error getting onboarding metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get onboarding metrics',
            details: error.message
        });
    }
};

/**
 * Get user journey for a specific session
 * GET /analytics/journey/:sessionId
 */
export const handleGetUserJourney = async (req: Request, res: Response): Promise<void> => {
    try {
        const { sessionId } = req.params;

        if (!sessionId) {
            res.status(400).json({
                success: false,
                error: 'Session ID is required'
            });
            return;
        }

        // Journey data is now retrieved from database
        const journey: UserAction[] = [];

        res.status(200).json({
            success: true,
            sessionId,
            journey,
            totalSteps: journey.length
        });

    } catch (error: any) {
        console.error('❌ Error getting user journey:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get user journey',
            details: error.message
        });
    }
};

/**
 * Sync LinkedIn analytics data for a user (Functional approach - frontend handles database)
 * POST /analytics/sync/:userId
 * Body: { linkedinAccountId: string }
 */
export const handleSyncAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;
        const { linkedinAccountId } = req.body;

        console.log('=== FUNCTIONAL ANALYTICS SYNC REQUEST ===');
        console.log('Request params:', req.params);
        console.log('Request body:', req.body);
        console.log('User ID from params:', userId);
        console.log('LinkedIn Account ID from body:', linkedinAccountId);

        if (!userId) {
            console.log('❌ No user ID provided in request');
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        if (!linkedinAccountId) {
            console.log('❌ No LinkedIn account ID provided in request body');
            res.status(400).json({
                success: false,
                error: 'LinkedIn account ID is required in request body'
            });
            return;
        }

        console.log('🔍 Syncing analytics for user:', userId);
        console.log('🔗 Using LinkedIn Account ID:', linkedinAccountId);

        // Fetch fresh analytics data from Unipile
        console.log('📊 Calling Unipile analytics service...');
        const analyticsData = await unipileService.getLinkedInAnalytics(linkedinAccountId);

        console.log('✅ Analytics data fetched from Unipile:', {
            profile_views: analyticsData.profile_views,
            connection_count: analyticsData.connection_count,
            follower_count: analyticsData.follower_count,
            posts_count: analyticsData.posts?.length || 0,
            totalImpressions: analyticsData.totalImpressions,
            engagementRate: analyticsData.engagementRate
        });

        // Return the fresh data to frontend (frontend handles database updates)
        res.status(200).json({
            success: true,
            message: 'Analytics synced successfully from Unipile',
            data: analyticsData,
            timestamp: new Date().toISOString(),
            source: 'unipile_realtime'
        });

        console.log('✅ Analytics sync completed successfully');
    } catch (error: any) {
        console.error('❌ Error syncing analytics:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error during analytics sync',
            details: error.message
        });
    }
};

/**
 * Get analytics data for a user
 * GET /analytics/:userId
 */
export const handleGetAnalyticsData = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        console.log('Fetching analytics for user:', userId);

        const analytics = await getAnalytics(userId);

        if (!analytics) {
            res.status(404).json({
                success: false,
                error: 'Analytics data not found for user'
            });
            return;
        }

        res.status(200).json({
            success: true,
            data: analytics
        });
    } catch (error: any) {
        console.error('Error fetching analytics:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error during analytics fetch',
            details: error.message
        });
    }
};

/**
 * Get LinkedIn profile data
 * GET /analytics/profile/:userId
 */
export const handleGetProfile = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        console.log('Fetching LinkedIn profile for user:', userId);

        const user = await getUserById(userId);
        if (!user || !user.account_id) {
            res.status(404).json({
                success: false,
                error: 'User not found or LinkedIn account not connected'
            });
            return;
        }

        const profileData = await unipileService.getLinkedInProfile(user.account_id);

        res.status(200).json({
            success: true,
            data: profileData
        });
    } catch (error: any) {
        console.error('Error fetching LinkedIn profile:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error during profile fetch',
            details: error.message
        });
    }
};

/**
 * Initialize analytics tracking for a user
 * POST /analytics/initialize
 */
export const handleInitializeAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId, linkedinAccountId } = req.body;

        if (!userId || !linkedinAccountId) {
            res.status(400).json({
                success: false,
                error: 'User ID and LinkedIn Account ID are required'
            });
            return;
        }

        console.log('Initializing analytics for user:', userId);

        const analytics = await initializeAnalytics(userId, linkedinAccountId);

        res.status(201).json({
            success: true,
            message: 'Analytics tracking initialized successfully',
            data: analytics
        });
    } catch (error: any) {
        console.error('Error initializing analytics:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error during analytics initialization',
            details: error.message
        });
    }
};

/**
 * Get analytics dashboard data with real-time LinkedIn data
 * GET /analytics/dashboard/:userId
 */
export const handleGetDashboardData = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;
        const { linkedinAccountId, linkedinUserId } = req.query;

        console.log('=== DASHBOARD DATA REQUEST ===');
        console.log('Request params:', req.params);
        console.log('Request query:', req.query);
        console.log('Request body:', req.body);
        console.log('Request headers:', req.headers);
        console.log('User ID from params:', userId);
        console.log('LinkedIn Account ID from query:', linkedinAccountId);
        console.log('LinkedIn User ID from query:', linkedinUserId);

        if (!userId) {
            console.log('❌ No user ID provided in dashboard request');
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        console.log('📊 Fetching dashboard data for user:', userId);

        // Use LinkedIn Account ID from query parameters (from user metadata)
        const linkedInAccountId = linkedinAccountId as string;

        if (!linkedInAccountId) {
            console.log('❌ No LinkedIn account ID provided');
            res.status(400).json({
                success: false,
                error: 'LinkedIn account ID required'
            });
            return;
        }

        console.log('🔗 Using LinkedIn Account ID from user metadata:', linkedInAccountId);

        let realTimeAnalytics = null;

        // If we have LinkedIn account ID, fetch real-time data
        if (linkedInAccountId) {
            console.log('🚀 === DASHBOARD: STARTING REAL-TIME ANALYTICS ===');
            console.log('🔗 LinkedIn Account ID from user metadata:', linkedInAccountId);
            console.log('👤 User ID:', userId);

            try {
                console.log('📊 Calling Unipile analytics service...');
                realTimeAnalytics = await unipileService.getLinkedInAnalytics(linkedInAccountId);
                console.log('✅ === DASHBOARD: REAL-TIME ANALYTICS SUCCESS ===');
            } catch (error) {
                console.error('💥 === DASHBOARD: REAL-TIME ANALYTICS FAILED ===');
                console.error('❌ Error details:', error);
                console.warn('⚠️ Falling back to stored data');
            }
        } else {
            console.log('ℹ️ === DASHBOARD: NO LINKEDIN ACCOUNT ID ===');
            console.log('📋 Using stored data only');
        }

        // Get stored analytics data as fallback
        const storedAnalytics = await getAnalytics(userId);

        // Use real-time data if available, otherwise use stored data
        const analyticsData = realTimeAnalytics || storedAnalytics || {
            totalImpressions: 0,
            impressionsPerPost: 0,
            impressionsPerFollower: 0,
            likesPerPost: 0,
            postsPerWeek: 0,
            engagementRate: 0,
            profile: {
                follower_count: 0,
                connection_count: 0,
                profile_views: 0
            },
            posts: []
        };

        // Combine data for dashboard
        const dashboardData = {
            user: {
                id: userId,
                email: 'unknown',
                first_name: '',
                last_name: '',
                linkedin_connection_status: 'connected',
                account_id: linkedInAccountId
            },
            analytics: analyticsData,
            isRealTime: !!realTimeAnalytics,
            last_sync: storedAnalytics?.updated_at || null
        };

        res.status(200).json({
            success: true,
            data: dashboardData
        });
    } catch (error: any) {
        console.error('Error fetching dashboard data:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error during dashboard data fetch',
            details: error.message
        });
    }
};
