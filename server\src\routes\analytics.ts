import { Router } from 'express';
import { ValidationMiddleware } from '../middleware/validation';
import {
  handleTrackAction,
  handleGetOnboardingMetrics,
  handleGetUserJourney,
  handleSyncAnalytics,
  handleGetAnalyticsData,
  handleGetProfile,
  handleInitializeAnalytics,
  handleGetDashboardData
} from '../handlers/analytics-legacy.handlers';
import {
  handleGetLinkedInPosts,
  handleGetLinkedInPost,
  handleGetLinkedInPostComments,
  handleGetLinkedInPostReactions
} from '../handlers/linkedin.handlers';
import {
  handleGetComprehensivePostData
} from '../handlers/analytics.handlers';

const router = Router();

// Sync analytics data for a user
router.post('/sync/:userId',
  ValidationMiddleware.validateAnalyticsRequest,
  handleSyncAnalytics
);

// Get analytics data for a user
router.get('/:userId',
  ValidationMiddleware.validateAnalyticsRequest,
  handleGetAnalyticsData
);

// Get LinkedIn profile data
router.get('/profile/:userId',
  ValidationMiddleware.validateAnalyticsRequest,
  handleGetProfile
);

// Initialize analytics tracking
router.post('/initialize',
  ValidationMiddleware.validateAnalyticsInit,
  handleInitializeAnalytics
);

// User tracking routes for onboarding analytics
router.post('/track', handleTrackAction);
router.get('/onboarding/metrics', handleGetOnboardingMetrics);
router.get('/journey/:sessionId', handleGetUserJourney);

// Get dashboard data
router.get('/dashboard/:userId',
  ValidationMiddleware.validateAnalyticsRequest,
  handleGetDashboardData
);

// LinkedIn specific routes
router.get('/linkedin/posts/:userId',
  ValidationMiddleware.validateAnalyticsRequest,
  handleGetLinkedInPosts
);

router.get('/linkedin/post/:userId/:postId',
  ValidationMiddleware.validateAnalyticsRequest,
  handleGetLinkedInPost
);

// Get LinkedIn post comments (Official Unipile API)
router.get('/linkedin/post/:userId/:postId/comments',
  ValidationMiddleware.validateAnalyticsRequest,
  handleGetLinkedInPostComments
);

// Get LinkedIn post reactions (Official Unipile API)
router.get('/linkedin/post/:userId/:postId/reactions',
  ValidationMiddleware.validateAnalyticsRequest,
  handleGetLinkedInPostReactions
);

// Get comprehensive post data (post + comments + reactions) with detailed logging
router.get('/posts/:postId/comprehensive',
  handleGetComprehensivePostData
);

export default router;
