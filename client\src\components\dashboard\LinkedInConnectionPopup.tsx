import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { X, LinkIcon, BarChart3, Users, TrendingUp, Shield, Mail, Linkedin } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UnipileHostedAuth, UnipileReconnectAccount } from '@/components/auth/UnipileHostedAuth';
import { useAuth } from '@/context/AuthContext';
import { analyticsService } from '@/services/analytics';
import { ToastManager } from '@/utils/toastManager';
import { getApiUrl } from '@/config/env';
import { supabase } from '@/integrations/supabase/client';

interface LinkedInConnectionPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (accountId: string) => void;
  isFirstTime?: boolean;
}

const LinkedInConnectionPopup: React.FC<LinkedInConnectionPopupProps> = ({
  isOpen,
  onClose,
  onSuccess,
  isFirstTime = false
}) => {
  const { user } = useAuth();
  const [showLinkedInAuth, setShowLinkedInAuth] = useState(false);
  const [isCheckingAccount, setIsCheckingAccount] = useState(false);
  const [linkedinEmail, setLinkedinEmail] = useState('');
  const [showEmailInput, setShowEmailInput] = useState(true);
  const [isReconnection, setIsReconnection] = useState(false);
  const [reconnectAccountId, setReconnectAccountId] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && user?.id) {
      analyticsService.setUserId(user.id);
      analyticsService.trackUserAction('linkedin_popup_shown', {
        context: isFirstTime ? 'dashboard_first_login' : 'dashboard_returning_user',
        isFirstTime
      });
    }
  }, [isOpen, user, isFirstTime]);

  // Pre-fill with user's email when popup opens and reset state
  useEffect(() => {
    if (isOpen && user?.email) {
      setLinkedinEmail(user.email);
      setShowEmailInput(true);
      setShowLinkedInAuth(false);
      setIsReconnection(false);
      setReconnectAccountId(null);
    }
  }, [isOpen, user?.email]);

  const handleConnectLinkedIn = async () => {
    if (!linkedinEmail.trim()) {
      ToastManager.error('Please enter your LinkedIn email address.');
      return;
    }

    console.log('🔍 Debug user state:', {
      userId: user?.id,
      userEmail: user?.email,
      linkedinEmail: linkedinEmail.trim()
    });

    if (!user?.id || !user?.email) {
      ToastManager.error('User session not found. Please try logging in again.');
      console.error('❌ Missing user data:', { userId: user?.id, userEmail: user?.email });
      return;
    }

    setIsCheckingAccount(true);

    try {
      // Track LinkedIn connection attempt
      await analyticsService.trackLinkedInVerificationAttempt(linkedinEmail);

      console.log('🔍 Checking for existing LinkedIn account for:', linkedinEmail);

      const response = await fetch(`${import.meta.env.SERVER_URL || 'http://localhost:3000'}/auth/get-account-by-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: linkedinEmail.trim() }),
      });

      const data = await response.json();

      if (response.ok && data.success && data.account_id) {
        // Account already exists - check if it needs reconnection
        console.log('✅ Found existing account:', {
          accountId: data.account_id,
          shouldReconnect: data.shouldReconnect,
          status: data.status
        });

        if (data.shouldReconnect) {
          // Account exists but needs reconnection - show reconnect flow
          console.log('🔄 Account needs reconnection - showing reconnect flow');

          const message = data.message || 'Found existing LinkedIn account. Reconnecting...';
          ToastManager.info(message);

          // Set reconnection state
          setIsReconnection(true);
          setReconnectAccountId(data.account_id);

          // Hide email input and show auth flow for reconnection
          setShowEmailInput(false);
          setShowLinkedInAuth(true);
          setIsCheckingAccount(false);
          return;
        } else {
          // Account exists and is already connected - validate and save data
          console.log('✅ Account already connected - validating and updating local data');

          // Validate existing account data
          const accountData = {
            account_id: data.account_id,
            name: data.profile?.name || 'LinkedIn User',
            email: linkedinEmail.trim(),
            profile: data.profile || {},
            status: 'EXISTING'
          };

          // Validate that this matches existing user data
          const isValidExisting = await validateReconnectionData(accountData);
          if (!isValidExisting) {
            setIsCheckingAccount(false);
            return; // Error message already shown
          }

          const saved = await saveLinkedInDataToSupabase(accountData);

          if (!saved) {
            ToastManager.error('Found LinkedIn account but failed to save data. Please try again.');
            setIsCheckingAccount(false);
            return;
          }

          ToastManager.linkedin.alreadyConnected();

          // Track successful connection
          await analyticsService.trackLinkedInVerificationSuccess(linkedinEmail, data.account_id);

          // Call success callback and close popup
          onSuccess?.(data.account_id);
          onClose();
          return;
        }
      }
    } catch (error) {
      console.log('⚠️ Error checking existing account:', error);
      // Continue with normal verification flow
    }

    setIsCheckingAccount(false);

    // No existing account found, proceed with verification
    // Hide email input and show auth flow
    setShowEmailInput(false);
    setShowLinkedInAuth(true);
  };

  const validateReconnectionData = async (account: any): Promise<boolean> => {
    try {
      console.log('🔍 Validating reconnection data:', {
        newAccountId: account.account_id,
        newEmail: account.email,
        newLinkedInUserId: account.profile?.id || account.profile?.publicIdentifier
      });

      // Get existing LinkedIn data from BOTH user metadata AND profiles table
      const userMetadata = user?.user_metadata;
      const existingEmailFromMeta = userMetadata?.linkedinEmail || userMetadata?.linkedin_email;
      const existingAccountIdFromMeta = userMetadata?.linkedinAccountId || userMetadata?.linkedin_account_id;

      // Also check profiles table for existing data (using any type to avoid TypeScript issues)
      const { data: existingProfile, error: profileError } = await supabase
        .from('profiles')
        .select('linkedin_email, account_id, linkedin_user_id')
        .eq('id', user?.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') { // PGRST116 = no rows found
        console.error('❌ Error fetching existing profile data:', profileError);
        ToastManager.error('Failed to validate reconnection. Please try again.');
        return false;
      }

      const existingEmailFromDB = existingProfile?.linkedin_email;
      const existingAccountIdFromDB = existingProfile?.account_id;
      const existingUserIdFromDB = existingProfile?.linkedin_user_id;

      console.log('📊 Existing LinkedIn data:', {
        fromMetadata: { existingEmailFromMeta, existingAccountIdFromMeta },
        fromDatabase: { existingEmailFromDB, existingAccountIdFromDB, existingUserIdFromDB }
      });

      // Use database data as primary source, fallback to metadata
      const existingEmail = existingEmailFromDB || existingEmailFromMeta;
      const existingAccountId = existingAccountIdFromDB || existingAccountIdFromMeta;
      const existingUserId = existingUserIdFromDB;

      // If no existing LinkedIn data found, this is a first connection
      if (!existingEmail && !existingAccountId) {
        console.log('ℹ️ No existing LinkedIn data found - treating as first connection');
        return true;
      }

      // Extract new LinkedIn data
      const newEmail = account.email;
      const newAccountId = account.account_id;
      const newLinkedInUserId = account.profile?.id || account.profile?.publicIdentifier;

      // Check if email and account_id match existing records
      const emailMatches = existingEmail === newEmail;
      const accountIdMatches = existingAccountId === newAccountId;
      const userIdMatches = existingUserId === newLinkedInUserId;

      console.log('🔍 Validation results:', {
        emailMatches,
        accountIdMatches,
        userIdMatches,
        existingEmail,
        newEmail,
        existingAccountId,
        newAccountId,
        existingUserId,
        newUserId: newLinkedInUserId
      });

      // If email and account_id match, allow reconnection
      if (emailMatches && accountIdMatches) {
        console.log('✅ Reconnection validation passed - same account');
        ToastManager.success('Account validation successful - same LinkedIn account detected');
        return true;
      }

      // If email or account_id don't match, show error
      console.log('❌ Reconnection validation failed - different account');

      // Create detailed error message
      let errorMessage = 'Account mismatch detected. You\'re trying to connect a different LinkedIn account.';

      if (existingEmail) {
        errorMessage += ` Please use your original LinkedIn account (${existingEmail}) to reconnect.`;
      } else {
        errorMessage += ' Please use your original LinkedIn account to reconnect.';
      }

      ToastManager.error(errorMessage, {
        duration: 10000,
        style: {
          maxWidth: '500px'
        }
      });

      return false;

    } catch (error) {
      console.error('❌ Error validating reconnection data:', error);
      ToastManager.error('Failed to validate reconnection. Please try again.');
      return false;
    }
  };

  const saveLinkedInDataToSupabase = async (accountData: any) => {
    try {
      console.log('💾 Saving LinkedIn data to profiles table and user metadata:', accountData);

      const isReconnection = accountData.status === 'RECONNECTED';
      const now = new Date().toISOString();

      // Extract first and last name from LinkedIn profile
      const extractNamesFromProfile = (accountData: any) => {
        let firstName = '';
        let lastName = '';

        if (accountData.profile?.name) {
          // Split full name into first and last name
          const nameParts = accountData.profile.name.trim().split(' ');
          firstName = nameParts[0] || '';
          lastName = nameParts.slice(1).join(' ') || '';
        }

        // Also check if names are available in connection_params
        if (accountData.profile?.connection_params?.im?.firstName) {
          firstName = accountData.profile.connection_params.im.firstName;
        }
        if (accountData.profile?.connection_params?.im?.lastName) {
          lastName = accountData.profile.connection_params.im.lastName;
        }

        console.log('📝 Extracted names from LinkedIn profile:', {
          fullName: accountData.profile?.name,
          firstName,
          lastName
        });

        return { firstName, lastName };
      };

      const { firstName, lastName } = extractNamesFromProfile(accountData);

      // Check existing LinkedIn data in profiles table
      console.log('🔍 Checking for existing LinkedIn data in profiles table...');
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('account_id, linkedin_email, linkedin_user_id, linkedin_connected_at, linkedin_reconnection_count')
        .eq('id', user?.id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = no rows found
        console.error('❌ Error fetching existing profile:', fetchError);
        throw new Error('Failed to check existing LinkedIn data');
      }

      const hasExistingData = existingProfile?.account_id && existingProfile?.linkedin_email;

      console.log('📊 Connection analysis:', {
        isReconnection,
        hasExistingData,
        existingAccountId: existingProfile?.account_id,
        existingEmail: existingProfile?.linkedin_email,
        newAccountId: accountData.account_id,
        newEmail: accountData.email
      });

      if (isReconnection || hasExistingData) {
        // Validate reconnection data
        console.log('🔄 Validating reconnection data...');

        const emailMatches = existingProfile?.linkedin_email === accountData.email;
        const accountIdMatches = existingProfile?.account_id === accountData.account_id;

        if (!emailMatches || !accountIdMatches) {
          const errorMsg = `Reconnection validation failed. Please use your original LinkedIn account (${existingProfile?.linkedin_email}) to reconnect.`;
          console.error('❌ Reconnection validation failed:', {
            emailMatches,
            accountIdMatches,
            existingEmail: existingProfile?.linkedin_email,
            existingAccountId: existingProfile?.account_id,
            newEmail: accountData.email,
            newAccountId: accountData.account_id
          });
          throw new Error(errorMsg);
        }

        console.log('✅ Reconnection validation passed - updating non-unique fields only');

        // Update only non-unique fields for reconnection
        const reconnectionData = {
          first_name: firstName || undefined, // Update name if available
          last_name: lastName || undefined,   // Update name if available
          linkedin_url: accountData.profile?.publicIdentifier
            ? `https://linkedin.com/in/${accountData.profile.publicIdentifier}`
            : null,
          linkedin_connection_status: 'connected',
          linkedin_last_sync: now,
          linkedin_last_reconnection: now,
          linkedin_reconnection_count: (existingProfile?.linkedin_reconnection_count || 0) + 1,
          linkedin_profile_data: accountData.profile || {},
          updated_at: now
          // Note: NOT updating account_id, linkedin_email, linkedin_user_id to avoid unique constraint violations
        };

        const { error: reconnectError } = await supabase
          .from('profiles')
          .update(reconnectionData)
          .eq('id', user?.id);

        if (reconnectError) {
          console.error('❌ Reconnection update error:', reconnectError);
          throw reconnectError;
        }

        console.log('✅ Reconnection data updated successfully');

      } else {
        // First-time connection - save all data including unique fields
        console.log('🆕 First-time connection - saving all LinkedIn data...');

        const firstTimeData = {
          first_name: firstName || undefined, // Save name from LinkedIn
          last_name: lastName || undefined,   // Save name from LinkedIn
          linkedin_email: accountData.email,
          account_id: accountData.account_id,
          linkedin_user_id: accountData.profile?.id || accountData.profile?.publicIdentifier,
          linkedin_url: accountData.profile?.publicIdentifier
            ? `https://linkedin.com/in/${accountData.profile.publicIdentifier}`
            : null,
          linkedin_connected_at: now,
          linkedin_last_sync: now,
          linkedin_connection_status: 'connected',
          linkedin_profile_data: accountData.profile || {},
          linkedin_reconnection_count: 0,
          updated_at: now
        };

        const { error: firstTimeError } = await supabase
          .from('profiles')
          .update(firstTimeData)
          .eq('id', user?.id);

        if (firstTimeError) {
          console.error('❌ First-time connection error:', firstTimeError);

          // Handle unique constraint violations for first-time connections
          if (firstTimeError.code === '23505') {
            let errorMessage = 'This LinkedIn account is already connected to another user.';

            if (firstTimeError.message.includes('account_id')) {
              errorMessage = 'This LinkedIn account is already connected to another user. Please use a different LinkedIn account.';
            } else if (firstTimeError.message.includes('linkedin_email')) {
              errorMessage = 'This LinkedIn email is already connected to another user. Please use a different LinkedIn account.';
            } else if (firstTimeError.message.includes('linkedin_user_id')) {
              errorMessage = 'This LinkedIn user ID is already connected to another user. Please use a different LinkedIn account.';
            }

            throw new Error(errorMessage);
          }

          throw firstTimeError;
        }

        console.log('✅ First-time connection data saved successfully');
      }

      console.log('✅ Profiles table updated successfully');

      // Update user metadata with comprehensive LinkedIn data
      const existingMetadata = user?.user_metadata || {};

      // Prepare metadata update - preserve existing data and update LinkedIn info
      const metadataUpdate = {
        ...existingMetadata,
        linkedinAccountId: accountData.account_id,
        linkedin_account_id: accountData.account_id,
        linkedinUserId: accountData.profile?.id || accountData.profile?.publicIdentifier,
        linkedin_user_id: accountData.profile?.id || accountData.profile?.publicIdentifier,
        linkedinEmail: accountData.email,
        linkedin_email: accountData.email,
        linkedinUrl: accountData.profile?.publicIdentifier
          ? `https://linkedin.com/in/${accountData.profile.publicIdentifier}`
          : null,
        linkedin_url: accountData.profile?.publicIdentifier
          ? `https://linkedin.com/in/${accountData.profile.publicIdentifier}`
          : null,
        linkedin_connected: true,
        linkedin_connected_at: isReconnection ? existingMetadata.linkedin_connected_at || now : now,
        linkedin_last_reconnection: isReconnection ? now : undefined,
        linkedin_reconnection_count: isReconnection ? (existingMetadata.linkedin_reconnection_count || 0) + 1 : 0,
        linkedin_profile_name: accountData.profile?.name
      };

      // Update user metadata
      const { error: metadataError } = await supabase.auth.updateUser({
        data: metadataUpdate
      });

      if (metadataError) {
        console.error('⚠️ Warning: Failed to update user metadata:', metadataError);
        // Don't throw error - profiles table update succeeded
      } else {
        console.log('✅ User metadata updated successfully');
      }

      console.log('✅ LinkedIn data saved to both profiles table and user metadata successfully');
      return true;
    } catch (error) {
      console.error('❌ Error saving LinkedIn data:', error);
      return false;
    }
  };

  const handleLinkedInSuccess = async (account: any) => {
    console.log('✅ LinkedIn connected successfully:', account.account_id);

    const isReconnection = account.status === 'RECONNECTED';

    // Validate reconnection data if this is a reconnection
    if (isReconnection) {
      const isValidReconnection = await validateReconnectionData(account);
      if (!isValidReconnection) {
        return; // Error message already shown in validateReconnectionData
      }
    }

    // Save LinkedIn data to Supabase
    const saved = await saveLinkedInDataToSupabase(account);

    if (!saved) {
      ToastManager.error('LinkedIn connected but failed to save data. Please try again.');
      return;
    }

    // Track successful LinkedIn connection
    if (user?.email) {
      await analyticsService.trackLinkedInVerificationSuccess(user.email, account.account_id);
    }

    ToastManager.linkedin.connectionSuccess(isReconnection);

    // Show processing message
    ToastManager.info('Processing LinkedIn data and syncing analytics...');

    // Close the auth popup first
    setShowLinkedInAuth(false);
    setShowEmailInput(false);

    // Wait for webhook processing and data sync
    setTimeout(async () => {
      try {
        // Trigger dashboard data refresh to get updated LinkedIn info
        const response = await fetch(getApiUrl(`/analytics/${user?.id}`), {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          console.log('✅ Dashboard data refreshed after LinkedIn connection');
          ToastManager.success('LinkedIn data synchronized successfully!');
        }
      } catch (error) {
        console.error('⚠️ Error refreshing dashboard data:', error);
      }

      // Call success callback and close popup
      onSuccess?.(account.account_id);
      onClose();

      // Show final success message and refresh page
      setTimeout(() => {
        ToastManager.success('LinkedIn connection complete! Refreshing dashboard...');
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      }, 1000);

    }, 3000); // Wait 3 seconds for webhook processing
  };

  const handleLinkedInError = (error: string) => {
    console.error('❌ LinkedIn connection failed:', error);

    // Clear all existing toasts first
    toast.dismiss();

    toast.error(`LinkedIn connection failed: ${error}`);
    setShowLinkedInAuth(false);
  };

  const handleSkip = async () => {
    // Track LinkedIn skipped
    await analyticsService.trackLinkedInSkipped();

    // Clear all existing toasts first
    toast.dismiss();

    toast.info('LinkedIn connection skipped. You can connect later from your profile settings.');
    onClose();
  };

  if (!isOpen) return null;

  // Don't render if user is not loaded yet
  if (!user?.id) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading user session...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show LinkedIn auth flow
  if (showLinkedInAuth) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="w-full max-w-lg bg-white rounded-xl shadow-2xl max-h-[90vh] overflow-auto">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold text-black">Connect Your LinkedIn</h2>
              <Button
                onClick={() => setShowLinkedInAuth(false)}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <p className="text-gray-600 mb-6">
              Securely connect your LinkedIn account to unlock analytics and insights.
            </p>

            {isReconnection && reconnectAccountId ? (
              <UnipileReconnectAccount
                userId={user?.email || ''}
                accountId={reconnectAccountId}
                onSuccess={handleLinkedInSuccess}
                onError={handleLinkedInError}
              />
            ) : (
              <UnipileHostedAuth
                userId={user?.email || ''}
                onSuccess={handleLinkedInSuccess}
                onError={handleLinkedInError}
                providers={['LINKEDIN']}
              />
            )}

            <div className="mt-4 text-center">
              <Button
                onClick={() => setShowLinkedInAuth(false)}
                variant="outline"
                className="w-full"
              >
                ← Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show main connection popup
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl bg-white rounded-xl shadow-2xl max-h-[90vh] overflow-auto">
        <div className="p-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <LinkIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-black">
                  {isFirstTime ? 'Welcome! Connect Your LinkedIn' : 'LinkedIn Connection Required'}
                </h2>
                <p className="text-gray-600">
                  {isFirstTime
                    ? 'Unlock powerful analytics for your LinkedIn presence'
                    : 'Connect your LinkedIn account to access this feature'
                  }
                </p>
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Email Input Section */}
          {showEmailInput && (
            <div className="mb-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Mail className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-blue-900">LinkedIn Email Address</h3>
                  <p className="text-sm text-blue-700">Enter the email associated with your LinkedIn account</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="linkedin-email" className="text-sm font-medium text-blue-900">
                  LinkedIn Email
                </Label>
                <Input
                  id="linkedin-email"
                  type="email"
                  value={linkedinEmail}
                  onChange={(e) => setLinkedinEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full border-blue-300 focus:border-blue-500 focus:ring-blue-500"
                />
                <p className="text-xs text-blue-600">
                  💡 We'll check if you already have a LinkedIn account connected with this email
                </p>
              </div>
            </div>
          )}

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <Card className="border-2 border-gray-100 hover:border-blue-200 transition-colors">
              <CardHeader className="text-center pb-2">
                <div className="flex justify-center mb-2">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle className="text-lg">LinkedIn Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center">
                  Track your post performance, engagement rates, and audience growth
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="border-2 border-gray-100 hover:border-green-200 transition-colors">
              <CardHeader className="text-center pb-2">
                <div className="flex justify-center mb-2">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle className="text-lg">Audience Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center">
                  Understand your followers and optimize content for better reach
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="border-2 border-gray-100 hover:border-purple-200 transition-colors">
              <CardHeader className="text-center pb-2">
                <div className="flex justify-center mb-2">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle className="text-lg">Growth Tracking</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center">
                  Monitor your LinkedIn presence and identify growth opportunities
                </CardDescription>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <Button
              onClick={handleConnectLinkedIn}
              disabled={isCheckingAccount}
              className="w-full rounded-xl py-4 px-8 text-lg font-semibold bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
            >
              {isCheckingAccount ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Checking Account...
                </div>
              ) : (
                <>
                  <Linkedin className="mr-2 h-5 w-5" />
                  {showEmailInput ? 'Verify LinkedIn Account' : 'Connect LinkedIn Account'}
                </>
              )}
            </Button>

            {isFirstTime && (
              <div className="flex justify-center">
                <Button
                  onClick={handleSkip}
                  variant="ghost"
                  className="text-gray-500 hover:text-gray-700"
                >
                  Skip for now
                </Button>
              </div>
            )}
          </div>

          {/* LinkedIn Auth Component */}
          {showLinkedInAuth && (
            <div className="mt-6">
              <div className="mb-4 flex items-center justify-between">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowLinkedInAuth(false);
                    setShowEmailInput(true);
                  }}
                  className="text-blue-600 border-blue-300 hover:bg-blue-50"
                >
                  ← Back to Email
                </Button>
                <p className="text-sm text-gray-600">
                  Connecting: {linkedinEmail}
                </p>
              </div>

              {isReconnection && reconnectAccountId ? (
                <UnipileReconnectAccount
                  userId={user?.email || ''}
                  accountId={reconnectAccountId}
                  onSuccess={(account) => {
                    console.log('✅ UnipileReconnectAccount success:', account);
                    handleLinkedInSuccess(account);
                  }}
                  onError={(error) => {
                    console.error('❌ UnipileReconnectAccount error:', error);
                    handleLinkedInError(error);
                  }}
                />
              ) : (
                <UnipileHostedAuth
                  userId={user?.email || ''}
                  onSuccess={(account) => {
                    console.log('✅ UnipileHostedAuth success:', account);
                    handleLinkedInSuccess(account);
                  }}
                  onError={(error) => {
                    console.error('❌ UnipileHostedAuth error:', error);
                    handleLinkedInError(error);
                  }}
                  providers={['LINKEDIN']}
                />
              )}
            </div>
          )}

          {/* Security Notice */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-blue-600" />
              <p className="text-sm text-blue-800">
                🔒 Your LinkedIn data is secure and encrypted. We only access analytics data to provide insights.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

  );
};

export default LinkedInConnectionPopup;
