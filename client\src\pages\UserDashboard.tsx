import React, { useState, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import UserHeader from '@/components/dashboard/UserHeader';
import MetricsGrid from '@/components/dashboard/MetricsGrid';
import TopPerformingPost from '@/components/dashboard/TopPerformingPost';
import LinkedInPostsTable from '@/components/dashboard/LinkedInPostsTable';
import SavedPostsTable from '@/components/dashboard/SavedPostsTable';
import PageBackground from '@/components/layout/PageBackground';

import LinkedInConnectionPopup from '@/components/dashboard/LinkedInConnectionPopup';
import LinkedInReconnectPopup from '@/components/dashboard/LinkedInReconnectPopup';
import ComprehensivePostView from '@/components/dashboard/ComprehensivePostView';

import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { Linkedin, Loader2, RefreshCw, BarChart3, Users, TrendingUp } from 'lucide-react';
import { toast } from 'sonner';
import { analyticsApi, type DashboardData, type AnalyticsData } from '@/services/analyticsApi';
import { postSyncService } from '@/services/postSyncService';
import { useSessionManager } from '@/utils/sessionManager';

// Default empty analytics data (no mock data)
const emptyAnalyticsData: AnalyticsData = {
  totalImpressions: 0,
  impressionsPerPost: 0,
  impressionsPerFollower: 0,
  likesPerPost: 0,
  postsPerWeek: 0,
  engagementRate: 0,
  profile: {
    follower_count: 0,
    connection_count: 0,
    profile_views: 0,
  
  },
  posts: []
};

// Helper function to convert stored posts to analytics format
const convertPostsToAnalytics = (posts: any[]): AnalyticsData => {
  if (!posts || posts.length === 0) {
    return emptyAnalyticsData;
  }

  // Calculate metrics from posts
  const totalImpressions = posts.reduce((sum, post) => sum + (post.impressions || 0), 0);
  const totalLikes = posts.reduce((sum, post) => sum + (post.likes || 0), 0);
  const totalComments = posts.reduce((sum, post) => sum + (post.comments || 0), 0);
  const totalShares = posts.reduce((sum, post) => sum + (post.shares || 0), 0);

  const impressionsPerPost = posts.length > 0 ? totalImpressions / posts.length : 0;
  const likesPerPost = posts.length > 0 ? totalLikes / posts.length : 0;

  // Calculate engagement rate
  const totalEngagement = totalLikes + totalComments + totalShares;
  const engagementRate = totalImpressions > 0 ? (totalEngagement / totalImpressions) * 100 : 0;

  // Calculate posts per week (based on last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const recentPosts = posts.filter(post => new Date(post.post_date) >= thirtyDaysAgo);
  const postsPerWeek = (recentPosts.length / 30) * 7;

  // Convert posts to expected format
  const formattedPosts = posts.map(post => ({
    id: post.linkedin_post_id || post.id,
    social_id: post.linkedin_post_id,
    text: post.content || post.title,
    created_at: post.post_date,
    impressions: post.impressions || 0,
    likes: post.likes || 0,
    comments: post.comments || 0,
    shares: post.shares || 0,
    engagement_rate: post.engagement_rate || 0
  }));

  return {
    totalImpressions,
    impressionsPerPost,
    impressionsPerFollower: 0, // Would need follower count from profile
    likesPerPost,
    postsPerWeek,
    engagementRate,
    profile: {
      follower_count: 0, // Would need to fetch from profile
      connection_count: 0,
      profile_views: 0,
    },
    posts: formattedPosts,
  };
};

const UserDashboard = () => {
  const { user, profile } = useAuth();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>(emptyAnalyticsData);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showLinkedInPopup, setShowLinkedInPopup] = useState(false);
  const [showReconnectPopup, setShowReconnectPopup] = useState(false);
  const [isFirstTimePopup, setIsFirstTimePopup] = useState(false);
  const [showComprehensivePost, setShowComprehensivePost] = useState(false);
  const [selectedPostId, setSelectedPostId] = useState<string | null>(null);
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(null);

  // Session manager for controlling automatic syncs
  const {
    shouldAllowSync,
    markSyncCompleted,
    logSessionState,
    getSessionInfo
  } = useSessionManager(user?.id);

  // Debug session state
  const sessionInfo = getSessionInfo();

  // Check if LinkedIn is connected
  const userMetadata = user?.user_metadata;
  const hasLinkedInAccountId = userMetadata?.linkedinAccountId || userMetadata?.account_id;
  const hasLinkedInProfile = profile?.account_id || profile?.linkedin_url;
  const isLinkedInConnected = hasLinkedInAccountId || hasLinkedInProfile;

  // Load dashboard data following the finalized flow
  useEffect(() => {
    if (!user?.id) return;

    console.log('🔍 Dashboard useEffect triggered for user:', user.id);
    console.log('🔍 Session should allow sync:', shouldAllowSync());

    // Check LinkedIn connection status
    const userMetadata = user?.user_metadata;
    const hasLinkedInAccountId = userMetadata?.linkedinAccountId || userMetadata?.account_id;
    const hasLinkedInProfile = profile?.account_id || profile?.linkedin_url;
    const isConnected = hasLinkedInAccountId || hasLinkedInProfile;

    // 1. USER NOT CONNECTED TO LINKEDIN (FIRST VISIT)
    if (!isConnected) {
      console.log('🚫 User not connected to LinkedIn - no data loading');
      return; // Exit early unless user connects LinkedIn account
    }

    // 2. ONLY LOAD DATA IF SESSION ALLOWS (INITIAL LOGIN)
    if (!shouldAllowSync()) {
      console.log('⏭️ Skipping data load - not initial login (route navigation/refresh)');
      return;
    }

    // 3. RETURNING USERS (ALREADY CONNECTED TO LINKEDIN) - INITIAL LOGIN ONLY
    console.log('✅ Initial login detected - loading dashboard data');
    loadDashboardDataWithFinalizedFlow();
  }, [user?.id]); // Only depend on user.id, not profile or connection status

  // Show LinkedIn connection popup for new users (only on initial login)
  useEffect(() => {
    if (!user?.id || !user?.email) return;

    // Only show popup on initial login, not on route navigation
    if (!shouldAllowSync()) {
      console.log('⏭️ Skipping LinkedIn popup check - not initial login');
      return;
    }

    // Check LinkedIn connection status locally
    const userMetadata = user?.user_metadata;
    const hasLinkedInAccountId = userMetadata?.linkedinAccountId || userMetadata?.account_id;
    const hasLinkedInProfile = profile?.account_id || profile?.linkedin_url;
    const isConnected = hasLinkedInAccountId || hasLinkedInProfile;

    if (!isConnected) {
      // Check if this is a new user (just signed up)
      const isNewUser = !localStorage.getItem('linkedin_popup_shown');

      console.log('🔍 LinkedIn popup check (initial login):', {
        userId: user.id,
        userEmail: user.email,
        isConnected,
        isNewUser
      });

      if (isNewUser) {
        // Show popup after a short delay for first-time users
        const timer = setTimeout(() => {
          console.log('✅ Showing LinkedIn popup for new user');
          setIsFirstTimePopup(true);
          setShowLinkedInPopup(true);
          localStorage.setItem('linkedin_popup_shown', 'true');
        }, 1500);

        return () => clearTimeout(timer);
      }
    }
  }, [user?.id, user?.email]); // Only depend on stable user properties

  const loadDashboardData = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      console.log('=== FRONTEND USER DATA ===');
      console.log('🔍 Loading dashboard data for user:', user.id);
      console.log('👤 Full user object:', user);
      console.log('📋 User metadata:', user.user_metadata);
      console.log('🔗 LinkedIn account ID from metadata:', user.user_metadata?.linkedinAccountId);
      console.log('🔗 LinkedIn user ID from metadata:', user.user_metadata?.linkedinUserId);
      console.log('📧 LinkedIn email from metadata:', user.user_metadata?.linkedinEmail);
      console.log('🌐 LinkedIn URL from metadata:', user.user_metadata?.linkedinUrl);
      console.log('👤 Profile data:', profile);

      // Get LinkedIn data from user metadata
      const linkedinAccountId = user.user_metadata?.linkedinAccountId;
      const linkedinUserId = user.user_metadata?.linkedinUserId;

      const data = await analyticsApi.getDashboardData(user.id, linkedinAccountId, linkedinUserId);
      setDashboardData(data);
      setAnalyticsData(data.analytics);

      if (data.isRealTime) {
        toast.success('Real-time LinkedIn data loaded successfully!');
      } else {
        console.log('Using stored analytics data');
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load analytics data. Please try refreshing or reconnecting your LinkedIn account.');
      // Keep using empty data instead of mock data
      setAnalyticsData(emptyAnalyticsData);
    } finally {
      setIsLoading(false);
    }
  };

  // FINALIZED FLOW: Load data following the exact specified flow
  const loadDashboardDataWithFinalizedFlow = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      console.log('🔄 STEP 1: Loading data from Supabase (cache-first)');

      // STEP 1: Load all data from Supabase to display UI
      const postsResult = await postSyncService.getUserPosts(user.id, {
        limit: 50,
        offset: 0,
        skipRefresh: true // Cache-only for initial load
      });

      if (postsResult.success && postsResult.data && postsResult.data.posts.length > 0) {
        const posts = postsResult.data.posts;
        const analyticsFromPosts = convertPostsToAnalytics(posts);
        setAnalyticsData(analyticsFromPosts);

        console.log(`✅ STEP 1 Complete: Loaded ${posts.length} posts from cache`);

        // STEP 2: In background, call Unipile to fetch latest data (only on initial login)
        if (shouldAllowSync()) {
          console.log('🔄 STEP 2: Starting background sync (initial login detected)');
          performBackgroundSync();
          markSyncCompleted();
        } else {
          console.log('⏭️ STEP 2: Skipping background sync (route navigation/refresh detected)');
          console.log('💡 Use "Refresh Data" button for manual sync');
        }

      } else {
        // No cached data - check if LinkedIn is connected but data not synced yet
        console.log('⚠️ No cached data found - checking LinkedIn connection status');
        await handleLinkedInConnectedButNoData();
        markSyncCompleted(); // Mark sync completed after first-time sync
      }
    } catch (error) {
      console.error('❌ Error in finalized flow:', error);
      toast.error('Failed to load data. Please try refreshing.');
      setAnalyticsData(emptyAnalyticsData);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle LinkedIn connected but no data synced yet
  const handleLinkedInConnectedButNoData = async () => {
    if (!user?.id) return;

    try {
      console.log('🔍 Checking if LinkedIn is connected but data not synced...');

      // Check if user has a real LinkedIn account ID (not mock)
      const linkedinAccountId = user.user_metadata?.linkedinAccountId ||
        user.user_metadata?.account_id ||
        profile?.account_id;
      const linkedinUserId = user.user_metadata?.linkedinUserId ||
        user.user_metadata?.linkedin_user_id;

      console.log('🔍 Checking LinkedIn IDs:', {
        linkedinAccountId,
        linkedinUserId,
        userMetadata: user.user_metadata,
        profile: profile
      });

      if (!linkedinAccountId || !linkedinUserId) {
        // No valid LinkedIn connection - show connection prompt
        console.log('❌ No valid LinkedIn connection found', {
          hasAccountId: !!linkedinAccountId,
          hasUserId: !!linkedinUserId
        });
        await handleFirstTimeLinkedInConnection();
        return;
      }

      // LinkedIn is connected but no data - trigger initial sync
      console.log('✅ LinkedIn connected but no data found - triggering initial sync');
      toast.info('LinkedIn connected! Syncing your posts for the first time...', {
        duration: 5000
      });

      const syncResult = await postSyncService.fullSync(user.id, linkedinAccountId, linkedinUserId);

      if (syncResult.success && syncResult.data) {
        const { sync } = syncResult.data;
        console.log('✅ Initial sync completed:', sync.posts);

        // Reload data after sync
        const postsResult = await postSyncService.getUserPosts(user.id, {
          limit: 50,
          skipRefresh: true
        });

        if (postsResult.success && postsResult.data) {
          const analyticsFromPosts = convertPostsToAnalytics(postsResult.data.posts);
          setAnalyticsData(analyticsFromPosts);
        }

        toast.success(
          `LinkedIn data synced! ${sync.posts.stored} posts imported`,
          { description: 'Your analytics are now available' }
        );
      } else {
        throw new Error(syncResult.error || 'Initial sync failed');
      }
    } catch (error: any) {
      console.error('❌ LinkedIn connected but sync failed:', error);

      // Check if it's a network connection issue
      if (error.message?.includes('Failed to fetch') ||
          error.message?.includes('Network connection failed') ||
          error.message?.includes('fetch')) {
        toast.error('Network connection failed', {
          description: 'Please check your internet connection and try again',
          duration: 6000
        });
      }
      // Check if it's a LinkedIn connection issue
      else if (error.message.includes('LinkedIn account not properly connected') ||
        error.message.includes('invalid_linkedin_account')) {
        toast.error('LinkedIn account connection issue', {
          description: 'Please reconnect your LinkedIn account',
          action: {
            label: 'Reconnect',
            onClick: () => handleManualLinkedInConnect()
          }
        });
      } else {
        toast.error('Failed to sync LinkedIn data', {
          description: 'Please try refreshing or reconnecting LinkedIn'
        });
      }

      // Fallback to showing empty state
      setAnalyticsData(emptyAnalyticsData);
    }
  };

  // 2. USER CONNECTS LINKEDIN FOR THE FIRST TIME (Legacy - now used for invalid connections)
  const handleFirstTimeLinkedInConnection = async () => {
    if (!user?.id) return;

    try {
      console.log('🆕 First-time LinkedIn connection detected');
      toast.info('Syncing your LinkedIn data...', { duration: 5000 });

      const linkedinAccountId = user.user_metadata?.linkedinAccountId;
      const linkedinUserId = user.user_metadata?.linkedinUserId;

      // Trigger full sync for first-time users
      const syncResult = await postSyncService.fullSync(user.id, linkedinAccountId, linkedinUserId);

      if (syncResult.success && syncResult.data) {
        const { sync } = syncResult.data;
        console.log('✅ First-time sync completed:', sync.posts);

        // Reload data after first sync
        await loadDashboardDataWithFinalizedFlow();

        toast.success(
          `LinkedIn data synced! ${sync.posts.stored} posts imported`,
          { description: 'Your analytics are now available' }
        );
      } else {
        throw new Error(syncResult.error || 'First-time sync failed');
      }
    } catch (error: any) {
      console.error('❌ First-time sync error:', error);

      // Check if it's a LinkedIn connection issue
      if (error.message.includes('LinkedIn account not properly connected') ||
        error.message.includes('invalid_linkedin_account')) {
        toast.error('LinkedIn account not properly connected', {
          description: 'Please reconnect your LinkedIn account to sync data',
          action: {
            label: 'Reconnect',
            onClick: () => handleManualLinkedInConnect()
          }
        });
      } else {
        toast.error('Failed to sync LinkedIn data. Please try again.');
      }
    }
  };

  // Background sync function (STEP 2 of returning users)
  const performBackgroundSync = async () => {
    if (!user?.id) return;

    try {
      console.log('🔄 Performing background sync...');

      // Use incremental sync for background updates
      const syncResult = await postSyncService.incrementalSync(user.id);

      if (syncResult.success && syncResult.data) {
        const { sync } = syncResult.data;

        if (sync.posts.stored === 0 && sync.posts.updated === 0) {
          console.log('✅ Background sync: Data is up-to-date');
          // Optionally show subtle notification
        } else {
          console.log(`✅ Background sync: ${sync.posts.stored + sync.posts.updated} posts updated`);

          // Softly update frontend states
          const postsResult = await postSyncService.getUserPosts(user.id, {
            limit: 50,
            skipRefresh: true // Get fresh data without triggering another sync
          });

          if (postsResult.success && postsResult.data) {
            const analyticsFromPosts = convertPostsToAnalytics(postsResult.data.posts);
            setAnalyticsData(analyticsFromPosts);

            // Show subtle update notification
            toast.success(
              `${sync.posts.stored + sync.posts.updated} posts updated in background`,
              { duration: 3000 }
            );
          }
        }
      }
    } catch (error: any) {
      console.error('❌ Background sync error:', error);
      // Don't show error toast for background sync failures
    }
  };

  // Legacy function for compatibility
  const loadDashboardDataWithSmartRefresh = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      console.log('🔍 Loading dashboard data with smart refresh for user:', user.id);

      // Step 1: Get posts from Supabase (fast, cached data)
      const postsResult = await postSyncService.getUserPosts(user.id, {
        limit: 50,
        offset: 0
      });

      if (postsResult.success && postsResult.data) {
        // Convert posts to analytics format
        const posts = postsResult.data.posts;
        const analyticsFromPosts = convertPostsToAnalytics(posts);

        setAnalyticsData(analyticsFromPosts);

        // Show data age info
        if (postsResult.data.refresh?.dataAge) {
          const { minutes, status } = postsResult.data.refresh.dataAge;
          if (status === 'fresh') {
            toast.success(`Data loaded (${minutes}m old) - Fresh!`);
          } else if (status === 'stale') {
            toast.info(`Data loaded (${minutes}m old) - Refreshing in background...`);
          } else {
            toast.warning(`Data loaded (${minutes}m old) - Updating...`);
          }
        }

        // If refresh was triggered, show update info
        if (postsResult.data.refresh?.triggered && postsResult.data.refresh.result) {
          const result = postsResult.data.refresh.result;
          if (result.success && result.postsUpdated > 0) {
            toast.success(`${result.postsUpdated} posts updated from LinkedIn!`);
          }
        }
      } else {
        // Fallback to old method if no cached data
        console.log('No cached data found, falling back to direct API call');
        await loadDashboardData();
      }
    } catch (error) {
      console.error('Error loading dashboard data with smart refresh:', error);
      // Fallback to old method on error
      await loadDashboardData();
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAnalytics = async () => {
    if (!user?.id) return;

    try {
      setIsRefreshing(true);
      console.log('=== FRONTEND REFRESH REQUEST ===');
      console.log('🔄 Refreshing analytics for user:', user.id);
      console.log('🔗 LinkedIn account ID from metadata:', user.user_metadata?.linkedinAccountId);
      console.log('🔗 LinkedIn account ID from profile:', profile?.account_id);

      // Get LinkedIn IDs from user metadata or profile
      const linkedinAccountId = user.user_metadata?.linkedinAccountId ||
        user.user_metadata?.account_id ||
        profile?.account_id;

      const linkedinUserId = user.user_metadata?.linkedinUserId ||
        user.user_metadata?.linkedin_user_id;

      if (!linkedinAccountId || !linkedinUserId) {
        toast.error('LinkedIn account not properly connected. Please reconnect your LinkedIn account.');
        return;
      }

      console.log('✅ Using LinkedIn IDs for sync:', {
        accountId: linkedinAccountId,
        userId: linkedinUserId
      });

      // 6. REFRESH BUTTON LOGIC - Following exact specified flow
      // 1. Fetch fresh data from Unipile
      const syncResult = await postSyncService.syncPosts({
        userId: user.id,
        accountId: linkedinAccountId,
        userIdentifier: linkedinUserId,
        limit: 50,
        fullSync: true // Force full sync for manual refresh
      });

      if (syncResult.success && syncResult.data) {
        const { sync } = syncResult.data;
        console.log('✅ Fresh data fetched from Unipile:', sync.posts);

        // 2. Compare with Supabase data (using post IDs) - handled in backend
        // 3. Check if same or updated/new data found
        if (sync.posts.stored === 0 && sync.posts.updated === 0) {
          // 3. If same → Show toast: "No new data. Already up to date."
          toast.info('No new data. Already up to date.', {
            description: `Checked ${sync.posts.fetched} posts from LinkedIn`
          });
        } else {
          // 4. If updated/new data found:
          // - Update Supabase (already done in backend)
          // - Update frontend state
          await loadDashboardDataWithFinalizedFlow();

          // - Show toast: "New data fetched successfully."
          toast.success('New data fetched successfully!', {
            description: `${sync.posts.stored} new posts, ${sync.posts.updated} updated posts`
          });
        }
      } else {
        throw new Error(syncResult.error || 'Sync failed');
      }
    } catch (error) {
      console.error('Error refreshing analytics:', error);
      toast.error('Failed to refresh analytics data');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Get top performing post from analytics data
  const getTopPost = () => {
    if (analyticsData.posts && analyticsData.posts.length > 0) {
      const topPost = analyticsData.posts.reduce((prev, current) =>
        (prev.engagement_rate > current.engagement_rate) ? prev : current
      );

      // Format date properly
      const formatPostDate = (dateString: string) => {
        try {
          const date = new Date(dateString);
          return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
          });
        } catch {
          return 'N/A';
        }
      };

      return {
        title: topPost.text.substring(0, 50) + (topPost.text.length > 50 ? '...' : ''),
        date: formatPostDate(topPost.created_at),
        preview: topPost.text,
        impressions: topPost.impressions || 0,
        likes: topPost.likes || 0,
        comments: topPost.comments || 0,
        shares: topPost.shares || 0,
        engagementRate: topPost.engagement_rate || 0
      };
    }

    // Return empty data instead of mock data
    return {
      title: "No posts available",
      date: "N/A",
      preview: "Connect your LinkedIn account and publish posts to see analytics here.",
      impressions: 0,
      likes: 0,
      comments: 0,
      shares: 0,
      engagementRate: 0
    };
  };

  const handleLinkedInSuccess = (accountId: string) => {
    console.log('✅ LinkedIn connected successfully from popup:', accountId);

    // Refresh the page to update the LinkedIn connection status
    window.location.reload();
  };

  const handleManualLinkedInConnect = () => {
    console.log('🔗 Manual LinkedIn connection requested', {
      userId: user?.id,
      userEmail: user?.email,
      hasUser: !!user
    });

    if (!user?.id || !user?.email) {
      console.error('❌ Cannot open LinkedIn popup - user not loaded');
      toast.error('Please refresh the page and try again.');
      return;
    }

    // Manual connection is not first-time (user clicked connect button)
    setIsFirstTimePopup(false);
    setShowLinkedInPopup(true);
  };

  const handleLinkedInReconnect = () => {
    console.log('🔄 LinkedIn reconnection requested', {
      userId: user?.id,
      userEmail: user?.email,
      hasUser: !!user
    });

    if (!user?.id || !user?.email) {
      console.error('❌ Cannot reconnect LinkedIn - user not loaded');
      toast.error('Please refresh the page and try again.');
      return;
    }

    // Show reconnect popup
    setShowReconnectPopup(true);
  };

  // Function to show LinkedIn popup for returning users (without skip option)
  const handleLinkedInRequired = () => {
    console.log('🔗 LinkedIn connection required for returning user');
    setIsFirstTimePopup(false); // Not first time, so no skip option
    setShowLinkedInPopup(true);
  };

  const handleViewPostDetails = (postId: string) => {
    console.log(`🔍 [${new Date().toISOString()}] User requested comprehensive post view`);
    console.log(`📊 Post ID: ${postId}`);

    // Get LinkedIn account ID from user metadata
    const linkedinAccountId = user?.user_metadata?.linkedinAccountId ||
      user?.user_metadata?.linkedin_account_id ||
      profile?.linkedin_account_id;

    if (!linkedinAccountId) {
      console.error('❌ Cannot view post details - LinkedIn account ID not found');
      toast.error('LinkedIn account not connected. Please reconnect your LinkedIn account.');
      return;
    }

    console.log(`🔗 Using LinkedIn account ID: ${linkedinAccountId}`);

    setSelectedPostId(postId);
    setSelectedAccountId(linkedinAccountId);
    setShowComprehensivePost(true);

    console.log(`✅ Opening comprehensive post view for post: ${postId}`);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <PageBackground />
      <Navbar />
      <div className="flex-1 container mx-auto px-4 py-8">
        <UserHeader onLinkedInRequired={handleLinkedInRequired} />

        <div className="grid grid-cols-1 gap-8">
          {/* LinkedIn Connection Section */}
          {!isLinkedInConnected && (
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Linkedin className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-blue-900">Connect Your LinkedIn Account</h3>
                    <p className="text-blue-700">
                      Get insights into your LinkedIn posts, engagement, and professional network growth
                    </p>
                  </div>
                </div>
                <Button
                  onClick={handleManualLinkedInConnect}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Linkedin className="w-4 h-4 mr-2" />
                  Connect LinkedIn
                </Button>
              </div>

              {/* Feature highlights */}
              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2 text-blue-700">
                  <BarChart3 className="w-4 h-4" />
                  <span className="text-sm">Post Analytics</span>
                </div>
                <div className="flex items-center space-x-2 text-blue-700">
                  <Users className="w-4 h-4" />
                  <span className="text-sm">Engagement Metrics</span>
                </div>
                <div className="flex items-center space-x-2 text-blue-700">
                  <TrendingUp className="w-4 h-4" />
                  <span className="text-sm">Growth Tracking</span>
                </div>
              </div>
            </div>
          )}

          {isLinkedInConnected ? (
            <>
              {/* LinkedIn Profile Status */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <Linkedin className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-sm font-semibold text-green-900">LinkedIn Connected</h3>
                      <p className="text-sm text-green-700">
                        LinkedIn account connected • {user?.email}
                        {userMetadata?.linkedin_connected_at && (
                          <span className="ml-2">
                            • Connected {new Date(userMetadata.linkedin_connected_at).toLocaleDateString()}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {profile?.linkedin_url && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(profile.linkedin_url, '_blank')}
                        className="text-green-700 border-green-300 hover:bg-green-50"
                      >
                        View Profile
                      </Button>
                    )}
                    <Button
                      disabled={isLoading}
                      variant="outline"
                      size="sm"
                      onClick={handleLinkedInReconnect}
                      className="text-green-700 border-green-300 hover:bg-green-700"
                    >
                      Reconnect Your Account
                    </Button>
                  </div>
                </div>
              </div>

              {/* Analytics Header with Refresh Button */}
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">LinkedIn Analytics</h2>
                  <p className="text-gray-600">
                    {dashboardData?.isRealTime ? 'Real-time data' : 'Stored data'}
                    {dashboardData?.last_sync && (
                      <span className="ml-2">
                        • Last sync: {new Date(dashboardData.last_sync).toLocaleDateString()}
                      </span>
                    )}
                  </p>
                </div>
                <Button
                  onClick={refreshAnalytics}
                  disabled={isRefreshing || isLoading}
                  variant="outline"
                  className="flex items-center gap-2 text-green-700 border-green-300 hover:bg-green-700"
                >
                  <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                  {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
                </Button>
              </div>

              {/* Loading State */}
              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Loading analytics data...</span>
                </div>
              ) : (
                <>
                  <MetricsGrid metrics={analyticsData} />
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                    <TopPerformingPost
                      post={getTopPost()}
                      onViewDetails={handleViewPostDetails}
                      topPostData={analyticsData.posts && analyticsData.posts.length > 0 ?
                        analyticsData.posts.reduce((prev, current) =>
                          (prev.engagement_rate > current.engagement_rate) ? prev : current
                        ) : null
                      }
                    />
                  </div>

                  <Tabs defaultValue="all-posts" className="mb-8 mt-8">
                    <TabsList className="mb-4">
                      <TabsTrigger value="all-posts">All Posts</TabsTrigger>
                      <TabsTrigger value="linkedin-posts">LinkedIn Posts</TabsTrigger>
                      <TabsTrigger value="draft-posts">Draft & Saved Posts</TabsTrigger>
                    </TabsList>

                    <TabsContent value="all-posts" className="space-y-8">
                      <LinkedInPostsTable posts={analyticsData.posts} onViewDetails={handleViewPostDetails} />
                      <SavedPostsTable />
                    </TabsContent>

                    <TabsContent value="linkedin-posts">
                      <LinkedInPostsTable posts={analyticsData.posts} onViewDetails={handleViewPostDetails} />
                    </TabsContent>

                    <TabsContent value="draft-posts">
                      <SavedPostsTable />
                    </TabsContent>
                  </Tabs>
                </>
              )}
            </>
          ) : (
            <div className="text-center py-12 bg-white rounded-lg shadow-sm mt-8">
              <div className="max-w-lg mx-auto">
                <Linkedin className="mx-auto h-12 w-12 text-[#0A66C2]" />
                <h2 className="mt-4 text-2xl font-semibold text-gray-900">Connect Your LinkedIn Account</h2>
                <p className="mt-2 text-gray-600">
                  Connect your LinkedIn account to view your post metrics, engagement statistics,
                  and track your professional content performance.
                </p>
                <div className="mt-6">
                  <p className="text-sm text-gray-500">Use the LinkedIn connector above to get started</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <Footer />

      {/* LinkedIn Connection Popup */}
      <LinkedInConnectionPopup
        isOpen={showLinkedInPopup}
        onClose={() => setShowLinkedInPopup(false)}
        onSuccess={handleLinkedInSuccess}
        isFirstTime={isFirstTimePopup}
      />

      {/* LinkedIn Reconnect Popup */}
      <LinkedInReconnectPopup
        isOpen={showReconnectPopup}
        onClose={() => setShowReconnectPopup(false)}
        onSuccess={(accountId) => {
          console.log('✅ LinkedIn reconnection successful:', accountId);
          setShowReconnectPopup(false);
          // Refresh the page to show updated LinkedIn status
          window.location.reload();
        }}
        existingAccountId={userMetadata?.linkedinAccountId}
        existingAccountName={userMetadata?.linkedinName || 'Your LinkedIn Account'}
      />

      {/* Comprehensive Post View */}
      {selectedPostId && selectedAccountId && (
        <ComprehensivePostView
          postId={selectedPostId}
          accountId={selectedAccountId}
          isOpen={showComprehensivePost}
          onClose={() => {
            console.log('🔒 Closing comprehensive post view');
            setShowComprehensivePost(false);
            setSelectedPostId(null);
            setSelectedAccountId(null);
          }}
        />
      )}

    </div>
  );
};

export default UserDashboard;
