
import React from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import ContentCreator from '@/components/dashboard/ContentCreator';
import { Linkedin } from 'lucide-react';
import JiveBackground from '@/components/jive/JiveBackground';

const ContentCreatorPage = () => {
  return (
    <div className="min-h-screen flex flex-col relative overflow-hidden">
      <JiveBackground />
      <Navbar />
      
      <div className="flex-1 container mx-auto px-4 py-8 relative z-10">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2 bg-gradient-to-r from-enterprise-blue to-enterprise-teal bg-clip-text text-transparent">
              <Linkedin className="h-7 w-7 text-[#0A66C2]" />
              Create LinkedIn Post
            </h1>
            <p className="text-enterprise-gray-600 animate-typing overflow-hidden whitespace-nowrap border-r-4 border-enterprise-blue pr-1">
              Share your insights and grow your professional network.
            </p>
          </div>
        </div>
        
        <ContentCreator />
      </div>
      
      <Footer />
    </div>
  );
};

export default ContentCreatorPage;
