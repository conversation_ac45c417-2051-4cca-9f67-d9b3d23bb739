import axios from 'axios';
import { environmentConfig, buildWebhookUrl, buildClientUrl } from '../../config/environment';

// Types from official Unipile documentation
interface CreateHostedAuthRequest {
  userId: string;
  providers: string[];
  type: 'create' | 'reconnect';
  accountId?: string; // Required for reconnect
}

interface HostedAuthResponse {
  object: string;
  url: string;
}

interface WebhookPayload {
  status: 'CREATION_SUCCESS' | 'RECONNECTED';
  account_id: string;
  name: string;
}

interface UnipileConfig {
  apiKey: string;
  apiUrl: string;
  baseUrl: string; // Your app's base URL
}

// Configuration from environment variables with intelligent detection
const config: UnipileConfig = {
  apiKey: environmentConfig.UNIPILE_API_ACCESS_TOKEN,
  apiUrl: environmentConfig.UNIPILE_API_URL,
  baseUrl: environmentConfig.CLIENT_URL
};

// Utility function to generate expiration date (2 hours from now)
const generateExpirationDate = (): string => {
  const now = new Date();
  now.setHours(now.getHours() + 2);
  return now.toISOString();
};

// Official Unipile Hosted Auth Service
export class OfficialUnipileHostedAuth {
  
  /**
   * Create hosted auth link using official Unipile API
   */
  static async createHostedAuthLink(
    userId: string,
    providers: string[] = ['LINKEDIN'],
    type: 'create' | 'reconnect' = 'create',
    reconnectAccountId?: string
  ): Promise<HostedAuthResponse> {
    try {
      console.log('🚀 Creating official Unipile hosted auth link:', { userId, providers, type, reconnectAccountId });

      // Validate configuration
      console.log('🔧 Checking Unipile configuration:', {
        hasApiKey: !!config.apiKey,
        apiKeyLength: config.apiKey?.length || 0,
        apiUrl: config.apiUrl,
        baseUrl: config.baseUrl
      });

      if (!config.apiKey || !config.apiUrl) {
        const error = `Unipile API configuration missing. UNIPILE_API_ACCESS_TOKEN: ${!!config.apiKey}, UNIPILE_API_URL: ${!!config.apiUrl}`;
        console.error('❌ Configuration error:', error);
        throw new Error(error);
      }

      // Official Unipile payload format with intelligent environment detection
      const payload = {
        type,
        providers: providers.length === 1 && providers[0] === '*' ? '*' : providers,
        api_url: config.apiUrl,
        expiresOn: generateExpirationDate(),
        success_redirect_url: type === 'reconnect' && reconnectAccountId
          ? buildClientUrl(`/auth/unipile-callback?success=true&userId=${userId}&accountId=${reconnectAccountId}&type=reconnect`)
          : buildClientUrl(`/auth/unipile-callback?success=true&userId=${userId}`),
        failure_redirect_url: buildClientUrl(`/auth/unipile-callback?success=false&userId=${userId}`),
        notify_url: buildWebhookUrl('/auth/webhook/unipile-auth'),
        name: userId,
        ...(type === 'reconnect' && reconnectAccountId && { reconnect_account: reconnectAccountId })
      };

      console.log('🌐 Environment-aware URLs:', {
        environment: environmentConfig.NODE_ENV,
        isLive: environmentConfig.isLive,
        isLocal: environmentConfig.isLocal,
        success_redirect_url: payload.success_redirect_url,
        failure_redirect_url: payload.failure_redirect_url,
        notify_url: payload.notify_url,
        webhook_base: environmentConfig.WEBHOOK_BASE_URL,
        client_base: environmentConfig.CLIENT_URL
      });

      console.log('📤 Sending request to Unipile API:', {
        url: `${config.apiUrl}/api/v1/hosted/accounts/link`,
        payload: JSON.stringify(payload, null, 2)
      });

      // Call official Unipile hosted auth API
      const response = await axios.post(
        `${config.apiUrl}/api/v1/hosted/accounts/link`,
        payload,
        {
          headers: {
            'X-API-KEY': config.apiKey,
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          timeout: 30000 // 30 second timeout
        }
      );

      console.log('✅ Official Unipile hosted auth response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ Error creating official hosted auth link:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          apiUrl: config.apiUrl,
          hasApiKey: !!config.apiKey,
          baseUrl: config.baseUrl
        }
      });
      
      if (error.response?.status === 401) {
        throw new Error('Invalid Unipile API key. Check UNIPILE_API_ACCESS_TOKEN environment variable.');
      } else if (error.response?.status === 400) {
        throw new Error(`Invalid request to Unipile API: ${error.response.data?.message || 'Bad request'}`);
      } else {
        throw new Error(`Failed to create hosted auth link: ${error.message}`);
      }
    }
  }

  /**
   * Handle webhook payload from Unipile
   */
  static async handleWebhook(payload: WebhookPayload): Promise<void> {
    try {
      console.log('🔔 Processing official Unipile webhook:', payload);

      // Validate webhook payload
      if (!payload.account_id || !payload.name || !payload.status) {
        throw new Error('Invalid webhook payload - missing required fields');
      }

      // Store the account connection
      await this.handleAccountConnection(payload);
      
      console.log('✅ Webhook processed successfully');

    } catch (error: any) {
      console.error('❌ Error processing webhook:', error);
      throw error;
    }
  }

  /**
   * Handle account connection from webhook
   */
  private static async handleAccountConnection(payload: WebhookPayload): Promise<void> {
    try {
      const accountData = {
        userId: payload.name, // This is the userId we sent in the 'name' field
        unipileAccountId: payload.account_id,
        status: payload.status,
        connectedAt: payload.status === 'CREATION_SUCCESS' ? new Date() : undefined,
        lastReconnectedAt: payload.status === 'RECONNECTED' ? new Date() : undefined,
      };

      // Store in our account mapping system
      const { storeUserAccountMapping } = await import('./account-management');
      await storeUserAccountMapping(payload.name, payload.account_id, payload.name);
      
      console.log(`✅ Account ${payload.status.toLowerCase()} for user ${payload.name}: ${payload.account_id}`);

    } catch (error: any) {
      console.error('❌ Error handling account connection:', error);
      throw error;
    }
  }

  /**
   * Get connected accounts for a user
   */
  static async getConnectedAccounts(userId: string): Promise<any[]> {
    try {
      const { getUserAccountMapping } = await import('./account-management');
      const mapping = await getUserAccountMapping(userId);
      
      if (mapping) {
        return [{
          id: mapping.account_id,
          userId: userId,
          unipileAccountId: mapping.account_id,
          status: 'CONNECTED',
          provider: 'LINKEDIN',
          connectedAt: new Date()
        }];
      }
      
      return [];
    } catch (error: any) {
      console.error('❌ Error getting connected accounts:', error);
      return [];
    }
  }

  /**
   * Disconnect account using Unipile API
   */
  static async disconnectAccount(userId: string, accountId: string): Promise<boolean> {
    try {
      console.log('🔌 Disconnecting account:', { userId, accountId });

      // Call Unipile API to disconnect the account
      await axios.delete(`${config.apiUrl}/api/v1/accounts/${accountId}`, {
        headers: {
          'X-API-KEY': config.apiKey,
          'Accept': 'application/json',
        },
      });

      // Remove from our account mapping
      // Note: We don't have a remove function yet, but we could add one
      console.log(`✅ Account disconnected: ${accountId} for user: ${userId}`);
      return true;

    } catch (error: any) {
      console.error('❌ Error disconnecting account:', error);
      return false;
    }
  }

  /**
   * Check account status using Unipile API
   */
  static async checkAccountStatus(accountId: string): Promise<any> {
    try {
      console.log('🔍 Checking account status:', accountId);

      const response = await axios.get(`${config.apiUrl}/api/v1/accounts/${accountId}`, {
        headers: {
          'X-API-KEY': config.apiKey,
          'Accept': 'application/json',
        },
      });

      console.log('✅ Account status:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ Error checking account status:', error);
      throw error;
    }
  }

  /**
   * Verify webhook signature (if Unipile provides it)
   */
  static verifyWebhookSignature(
    payload: string,
    signature: string,
    secret: string
  ): boolean {
    // TODO: Implement webhook signature verification if supported by Unipile
    // This is a security best practice for webhooks
    console.log('⚠️ Webhook signature verification not implemented yet');
    return true; // For now, accept all webhooks
  }
}

export default OfficialUnipileHostedAuth;
