
import React from 'react';

const EmojiRow = () => {
  // Define a function to generate staggered delays for each emoji
  // with complete animation properties matching AnimatedEmoji.tsx
  const generateDelayStyle = (index: number) => {
    return {
      animationDelay: `${index * 0.2}s`, // Stagger the animation by 0.2s per emoji
      animationDuration: '3s', // Match the duration from AnimatedEmoji
      animationIterationCount: 'infinite', // Make it loop forever
      animationDirection: 'alternate', // Match the direction from AnimatedEmoji
    };
  };

  // Array of emoji image paths
  const emojiImages = [
    "/lovable-uploads/06141301-44c3-47ed-a2c2-88bff5cbd18d.png",
    "/lovable-uploads/761b58a0-3e7f-4505-9762-eda5185a36d1.png",
    "/lovable-uploads/946025dd-6e88-4410-88f3-3adb1c9df18e.png",
    "/lovable-uploads/79213b99-81c8-469c-8927-704db97b2cbd.png",
    "/lovable-uploads/c099ce42-a903-4c4d-9c2f-4842120c8785.png",
    "/lovable-uploads/b3cf57e9-056d-4621-8fb6-d93ce5953fcc.png"
  ];

  return (
    <div className="w-full flex justify-center items-center py-6 overflow-hidden">
      <div className="flex gap-4 md:gap-6">
        {emojiImages.map((src, index) => (
          <img 
            key={index}
            src={src} 
            alt={`Emoji ${index + 1}`} 
            className="h-12 w-12 md:h-16 md:w-16 animate-float-bounce"
            style={generateDelayStyle(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default EmojiRow;
