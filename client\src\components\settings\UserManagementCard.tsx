
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import InvitationDialog from '../user/InvitationDialog';
import UserTable from './UserTable';
import InvitationsTable from './InvitationsTable';
import { User, Invitation } from '@/types/settings';

interface UserManagementCardProps {
  users: User[];
  invitations: Invitation[];
  isLoading: boolean;
  resendingId: string | null;
  resendInvitation: (invitationId: string) => Promise<void>;
}

const UserManagementCard: React.FC<UserManagementCardProps> = ({
  users,
  invitations,
  isLoading,
  resendingId,
  resendInvitation
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('users');

  // Filter users based on search query
  const filteredUsers = users.filter(user => 
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
    user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.department.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Filter invitations based on search query
  const filteredInvitations = invitations.filter(invite => 
    invite.email.toLowerCase().includes(searchQuery.toLowerCase()) || 
    invite.department.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>User Management</CardTitle>
            <CardDescription>
              Manage users and their permissions
            </CardDescription>
          </div>
          <InvitationDialog />
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <Input 
            placeholder="Search users..." 
            className="max-w-xs" 
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="users">Active Users</TabsTrigger>
            <TabsTrigger value="invitations">
              Pending Invitations
              {invitations.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {invitations.length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="users">
            <UserTable 
              users={filteredUsers} 
              isLoading={isLoading} 
            />
          </TabsContent>
          
          <TabsContent value="invitations">
            <InvitationsTable 
              invitations={filteredInvitations} 
              isLoading={isLoading} 
              resendingId={resendingId}
              resendInvitation={resendInvitation}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default UserManagementCard;
