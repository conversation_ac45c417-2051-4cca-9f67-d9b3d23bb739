import { UnipileConfig } from './config';
import { getLinkedInProfile } from './account-management';
import { getLinkedInPosts, getPostReactions, getPostComments } from './linkedin-posts';

/**
 * Enrich posts with detailed analytics (reactions and comments)
 */
export async function enrichPostsWithAnalytics(config: UnipileConfig, accountId: string, postsResponse: any): Promise<any[]> {
    try {
        const posts = postsResponse.items || postsResponse || [];
        const enrichedPosts = [];

        for (const post of posts) {
            try {
                // Use social_id (URN format) for API calls, fallback to id if social_id not available
                const postIdentifier = post.social_id || post.id;

                console.log(`📊 Enriching post analytics:`, {
                    post_id: post.id,
                    social_id: post.social_id,
                    using_identifier: postIdentifier
                });

                // Get reactions for this post using correct identifier
                const reactions = await getPostReactions(config, accountId, postIdentifier);

                // Get comments for this post using correct identifier
                const comments = await getPostComments(config, accountId, postIdentifier);

                // Enrich post with analytics data
                const enrichedPost = {
                    ...post,
                    reactions_count: reactions?.items?.length || 0,
                    comments_count: comments?.items?.length || 0,
                    reactions_data: reactions?.items || [],
                    comments_data: comments?.items || []
                };

                enrichedPosts.push(enrichedPost);
            } catch (postError) {
                console.warn(`Failed to get analytics for post ${post.social_id || post.id}:`, postError);
                // Include post without detailed analytics
                enrichedPosts.push({
                    ...post,
                    reactions_count: 0,
                    comments_count: 0,
                    reactions_data: [],
                    comments_data: []
                });
            }
        }

        return enrichedPosts;
    } catch (error: any) {
        console.error('Error enriching posts with analytics:', error);
        return postsResponse.items || postsResponse || [];
    }
}

/**
 * Calculate analytics from profile and posts data
 */
export async function calculateAnalytics(profile: any, posts: any[]): Promise<any> {
    try {
        // Handle posts data - could be array or object with items property
        const postsData: any[] = Array.isArray(posts) ? posts : ((posts as any)?.items || []);

        // Calculate metrics using real-time data from Unipile
        const totalPosts = postsData.length;

        // Calculate engagement metrics from reactions and comments
        const totalReactions = postsData.reduce((sum: number, post: any) =>
            sum + (post.reaction_counter || post.reactions_count || 0), 0);
        const totalComments = postsData.reduce((sum: number, post: any) =>
            sum + (post.comment_counter || post.comments_count || 0), 0);

        // Use real impressions data from Unipile instead of estimates
        const totalImpressions = postsData.reduce((sum: number, post: any) =>
            sum + (post.impressions_counter || post.impressions || 0), 0);

        console.log('📊 Real-time metrics calculated:', {
            totalPosts,
            totalReactions,
            totalComments,
            totalImpressions,
            source: 'unipile_real_data'
        });

        const totalEngagements = totalReactions + totalComments;
        const followerCount = profile?.follower_count || profile?.connections_count || 0;

        // Calculate averages using real data
        const impressionsPerPost = totalPosts > 0 ? Math.round(totalImpressions / totalPosts) : 0;
        const reactionsPerPost = totalPosts > 0 ? Math.round(totalReactions / totalPosts) : 0;
        const engagementRate = totalImpressions > 0 ?
            Number(((totalEngagements / totalImpressions) * 100).toFixed(1)) : 0;

        // Calculate posts per week (assuming last 30 days of data)
        const postsPerWeek = Number((totalPosts * 7 / 30).toFixed(1));

        const impressionsPerFollower = followerCount > 0 ?
            Number((totalImpressions / followerCount).toFixed(1)) : 0;

        return {
            totalImpressions: Math.round(totalImpressions),
            impressionsPerPost,
            impressionsPerFollower,
            likesPerPost: reactionsPerPost, // Using reactions as likes
            postsPerWeek,
            engagementRate,
            profile: {
                follower_count: followerCount,
                connection_count: profile?.connections_count || 0,
                profile_views: profile?.profile_views || 0,
                name: profile?.name || '',
                headline: profile?.headline || ''
            },
            posts: postsData.map((post: any) => {
                // Use real impression data from Unipile
                const postImpressions = post.impressions_counter || post.impressions || 0;
                const postReactions = post.reaction_counter || post.reactions_count || 0;
                const postComments = post.comment_counter || post.comments_count || 0;
                const postShares = post.repost_counter || post.shares || 0;

                const postEngagements = postReactions + postComments + postShares;
                const postEngagementRate = postImpressions > 0 ?
                    Number(((postEngagements / postImpressions) * 100).toFixed(1)) : 0;

                console.log(`📊 Post ${post.id} real metrics:`, {
                    impressions: postImpressions,
                    reactions: postReactions,
                    comments: postComments,
                    shares: postShares,
                    engagement_rate: postEngagementRate
                });

                return {
                    id: post.id,
                    social_id: post.social_id,
                    text: post.text || post.content || '',
                    created_at: post.created_at || post.parsed_datetime || post.date,
                    impressions: postImpressions, // Real data from Unipile
                    likes: postReactions, // Real data from Unipile
                    comments: postComments, // Real data from Unipile
                    shares: postShares, // Real data from Unipile
                    engagement_rate: postEngagementRate
                };
            })
        };
    } catch (error: any) {
        console.error('Error calculating analytics:', error);
        throw new Error(`Failed to calculate analytics: ${error.message}`);
    }
}

/**
 * Get comprehensive LinkedIn analytics
 */
export async function getLinkedInAnalytics(config: UnipileConfig, linkedinAccountId: string): Promise<any> {
    console.log('🚀 === STARTING LINKEDIN ANALYTICS ===');
    console.log('📋 Step 1: Using LinkedIn Account ID:', linkedinAccountId);

    // Step 2: Get account data to extract LinkedIn user ID
    console.log('📋 Step 2: Getting account data');
    const account = await getLinkedInProfile(config, linkedinAccountId);

    // Step 3: Extract LinkedIn user ID from account connection_params
    const linkedinUserId = account.connection_params?.im?.id;
    console.log('✅ Step 3: Extracted LinkedIn user ID:', linkedinUserId);

    if (!linkedinUserId) {
        throw new Error('Could not extract LinkedIn user ID from account data');
    }

    // Step 4: Get posts using both IDs
    console.log('📝 Step 4: Getting posts');
    const postsResponse = await getLinkedInPosts(config, linkedinAccountId, linkedinUserId, 50);

    // Step 5: Enrich posts with analytics
    console.log('📊 Step 5: Enriching posts with analytics');
    const postsWithAnalytics = await enrichPostsWithAnalytics(config, linkedinAccountId, postsResponse);

    // Step 6: Calculate analytics
    console.log('🧮 Step 6: Calculating analytics');
    const analytics = await calculateAnalytics(account, postsWithAnalytics);

    console.log('✅ === LINKEDIN ANALYTICS COMPLETED ===');
    return analytics;
}
