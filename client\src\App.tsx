
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import UserDashboard from "./pages/UserDashboard";
import AdminDashboard from "./pages/AdminDashboard";
import ContentCreatorPage from "./pages/ContentCreator";
import Settings from "./pages/Settings";
import Profile from "./pages/Profile";
import NotFound from "./pages/NotFound";
// import SignUp from "./pages/SignUp"; // still used for 2FA
// import HostedAuthSignup from "./components/auth/HostedAuthSignup"; // still used for 2FA
import QuickSignup from "./components/auth/QuickSignup";
import UnipileAuthCallbackClean from "./pages/auth/UnipileAuthCallbackClean";
import Login from "./pages/Login";
import JiveLanding from "./pages/JiveLanding";
import LinkedInSuccess from "./pages/auth/LinkedInSuccess";
import LinkedInError from "./pages/auth/LinkedInError";
import VerificationWaiting from "./pages/VerificationWaiting";
import VerificationComplete from "./pages/VerificationComplete";
import { AuthProvider } from "./context/AuthContext";
import { ProtectedRoute } from "./components/auth/ProtectedRoute";
import { AdminRoute } from "./components/auth/AdminRoute";

const App = () => {
  // Create the query client inside the component
  const queryClient = new QueryClient();
  
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
            <Routes>
              <Route path="/" element={<JiveLanding />} />
              <Route path="/signup" element={<QuickSignup />} />
              {/* <Route path="/signup-linkedin" element={<HostedAuthSignup />} /> */}
              {/* <Route path="/signup-old" element={<SignUp />} /> */}
              <Route path="/login" element={<Login />} />
              <Route path="/auth/linkedin-success" element={<LinkedInSuccess />} />
              <Route path="/auth/linkedin-error" element={<LinkedInError />} />
              <Route path="/auth/unipile-callback" element={<UnipileAuthCallbackClean />} />
              <Route path="/auth/verification-waiting" element={<VerificationWaiting />} />
              <Route path="/auth/verification-complete" element={<VerificationComplete />} />
              <Route path="/terms" element={<NotFound />} />
              <Route path="/best-practices" element={<NotFound />} />
              
              {/* Protected routes - require authentication */}
              <Route element={<ProtectedRoute />}>
                <Route path="/dashboard" element={<UserDashboard />} />
                <Route path="/user-dashboard" element={<UserDashboard />} />
                <Route path="/content-creator" element={<ContentCreatorPage />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/profile" element={<Profile />} />
              </Route>
              
              {/* Admin routes - require admin role */}
              <Route element={<AdminRoute />}>
                <Route path="/admin-dashboard" element={<AdminDashboard />} />
              </Route>
              
              <Route path="*" element={<NotFound />} />
            </Routes>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
