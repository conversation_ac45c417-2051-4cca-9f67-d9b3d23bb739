import { useState, useEffect, useRef, useCallback } from 'react';

interface PostMetrics {
  impressions: number;
  likes: number;
  comments: number;
  shares: number;
  engagement_rate: number;
}

interface AnimationState {
  [key: string]: boolean; // postId-metric -> isAnimating
}

interface MetricChange {
  postId: string;
  metric: string;
  oldValue: number;
  newValue: number;
}

export const useSimpleAnimations = (posts: any[] = []) => {
  const [animatingMetrics, setAnimatingMetrics] = useState<AnimationState>({});
  const [metricChanges, setMetricChanges] = useState<Record<string, MetricChange>>({});
  const previousPostsRef = useRef<Record<string, PostMetrics>>({});
  const isFirstRender = useRef(true);

  // Track which metrics are currently animating
  const isMetricAnimating = useCallback((postId: string, metric: string) => {
    return animatingMetrics[`${postId}-${metric}`] || false;
  }, [animatingMetrics]);

  // Get previous value for a metric
  const getPreviousValue = useCallback((postId: string, metric: string) => {
    const change = metricChanges[`${postId}-${metric}`];
    return change?.oldValue;
  }, [metricChanges]);

  // Start animation for a metric
  const startAnimation = useCallback((postId: string, metric: string, oldValue: number, newValue: number) => {
    const key = `${postId}-${metric}`;
    
    console.log(`🎬 Starting animation for ${key}: ${oldValue} → ${newValue}`);
    
    // Store the change
    setMetricChanges(prev => ({
      ...prev,
      [key]: {
        postId,
        metric,
        oldValue,
        newValue
      }
    }));

    // Mark as animating
    setAnimatingMetrics(prev => ({
      ...prev,
      [key]: true
    }));

    // Stop animation after 3 seconds
    setTimeout(() => {
      setAnimatingMetrics(prev => ({
        ...prev,
        [key]: false
      }));
      
      // Clean up old changes after animation
      setTimeout(() => {
        setMetricChanges(prev => {
          const newChanges = { ...prev };
          delete newChanges[key];
          return newChanges;
        });
      }, 1000);
    }, 3000);
  }, []);

  // Check if any metric in a post is animating
  const isPostAnimating = useCallback((postId: string) => {
    const metrics = ['impressions', 'likes', 'comments', 'shares', 'engagement_rate'];
    return metrics.some(metric => isMetricAnimating(postId, metric));
  }, [isMetricAnimating]);

  // Manual trigger for testing
  const triggerTestAnimation = useCallback((postId: string, currentMetrics: PostMetrics) => {
    console.log('🧪 Triggering test animations for post:', postId);
    
    // Simulate metric changes with random increases
    const testChanges = [
      { 
        metric: 'impressions', 
        oldValue: currentMetrics.impressions, 
        newValue: currentMetrics.impressions + Math.floor(Math.random() * 100) + 50 
      },
      { 
        metric: 'likes', 
        oldValue: currentMetrics.likes, 
        newValue: currentMetrics.likes + Math.floor(Math.random() * 10) + 5 
      },
      { 
        metric: 'comments', 
        oldValue: currentMetrics.comments, 
        newValue: currentMetrics.comments + Math.floor(Math.random() * 5) + 2 
      },
      { 
        metric: 'shares', 
        oldValue: currentMetrics.shares, 
        newValue: currentMetrics.shares + Math.floor(Math.random() * 3) + 1 
      }
    ];

    testChanges.forEach(({ metric, oldValue, newValue }, index) => {
      setTimeout(() => {
        startAnimation(postId, metric, oldValue, newValue);
      }, index * 300); // Stagger animations by 300ms
    });
  }, [startAnimation]);

  // Compare posts and detect changes
  useEffect(() => {
    if (!posts || posts.length === 0) return;

    // Skip first render to avoid false positives
    if (isFirstRender.current) {
      console.log('📊 First render - storing initial post metrics');
      // Store initial metrics
      const initialMetrics: Record<string, PostMetrics> = {};
      posts.forEach(post => {
        if (post && post.id) {
          initialMetrics[post.id] = {
            impressions: post.impressions || 0,
            likes: post.likes || 0,
            comments: post.comments || 0,
            shares: post.shares || 0,
            engagement_rate: post.engagement_rate || 0
          };
        }
      });
      previousPostsRef.current = initialMetrics;
      isFirstRender.current = false;
      return;
    }

    console.log('📊 Checking for metric changes in', posts.length, 'posts');

    // Compare current posts with previous
    posts.forEach(post => {
      if (!post || !post.id) return;

      const postId = post.id;
      const previousMetrics = previousPostsRef.current[postId];
      
      if (!previousMetrics) {
        // New post, store its metrics
        previousPostsRef.current[postId] = {
          impressions: post.impressions || 0,
          likes: post.likes || 0,
          comments: post.comments || 0,
          shares: post.shares || 0,
          engagement_rate: post.engagement_rate || 0
        };
        return;
      }

      // Check each metric for changes
      const currentMetrics = {
        impressions: post.impressions || 0,
        likes: post.likes || 0,
        comments: post.comments || 0,
        shares: post.shares || 0,
        engagement_rate: post.engagement_rate || 0
      };

      Object.entries(currentMetrics).forEach(([metric, currentValue]) => {
        const previousValue = previousMetrics[metric as keyof PostMetrics];
        
        if (previousValue !== currentValue) {
          console.log(`📈 Metric changed: ${postId} ${metric} ${previousValue} → ${currentValue}`);
          startAnimation(postId, metric, previousValue, currentValue);
        }
      });

      // Update stored metrics
      previousPostsRef.current[postId] = currentMetrics;
    });
  }, [posts, startAnimation]);

  // Test function that uses the first available post
  const testAnimations = useCallback(() => {
    if (posts && posts.length > 0) {
      const firstPost = posts[0];
      const currentMetrics = {
        impressions: firstPost.impressions || 0,
        likes: firstPost.likes || 0,
        comments: firstPost.comments || 0,
        shares: firstPost.shares || 0,
        engagement_rate: firstPost.engagement_rate || 0
      };
      triggerTestAnimation(firstPost.id, currentMetrics);
    } else {
      console.log('❌ No posts available for testing');
    }
  }, [posts, triggerTestAnimation]);

  return {
    isMetricAnimating,
    getPreviousValue,
    testAnimations,
    isPostAnimating,
    animatingMetrics,
    metricChanges
  };
};
