// Phase 2: Post Sync API Handlers
// Express handlers for LinkedIn post synchronization endpoints

import { Request, Response } from 'express';
import { postSyncOrchestrator, SyncOptions } from '../services/syncOrchestrator';
import { getUserPosts } from '../services/postStorageService';
import { getUserById } from '../services/user.service';
import { UnipileService } from '../services/unipile';
import { softRefreshService } from '../services/softRefreshService';

/**
 * Trigger LinkedIn post sync for a user
 * POST /api/posts/sync
 */
export const handleSyncPosts = async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();

    try {
        console.log('🚀 POST /api/posts/sync - Starting post sync request');

        const {
            userId,
            accountId,
            userIdentifier,
            //department = 'general',
            //limit = 50,
            fullSync = false
        } = req.body;

        // Validate required fields
        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'userId is required'
            });
            return;
        }

        // If accountId or userIdentifier not provided, try to get from user profile
        let finalAccountId = accountId;
        let finalUserIdentifier = userIdentifier;

        // Log what we received
        console.log('📋 Sync parameters received:', {
            userId,
            providedAccountId: accountId,
            providedUserIdentifier: userIdentifier
        });

        if (!finalAccountId || !finalUserIdentifier) {
            console.log('🔍 Looking up user LinkedIn account info for user:', userId);

            try {
                // Get user's LinkedIn account ID (following existing pattern)
                const user = await getUserById(userId);
                console.log('👤 User data from database:', {
                    userId: user?.id,
                    accountId: user?.account_id,
                    linkedinUserId: user?.linkedin_user_id
                });

                // Use account_id field (which maps to account_id in database)
                const dbAccountId = user?.account_id;

                if (user && dbAccountId) {

                    finalAccountId = dbAccountId;

                    // Get LinkedIn user ID (required for posts API)
                    if (!finalUserIdentifier) {
                        // First try to get from database
                        if (user.linkedin_user_id) {
                            finalUserIdentifier = user.linkedin_user_id;
                            console.log('✅ Using LinkedIn user ID from database:', finalUserIdentifier);
                        } else {
                            // Fallback: fetch from LinkedIn profile
                            try {
                                const unipileService = new UnipileService();
                                console.log('🔍 Fetching LinkedIn profile to get user identifier...');
                                const linkedInAccount = await unipileService.getLinkedInProfile(dbAccountId);

                                // Extract the LinkedIn user ID from connection_params
                                const linkedInUserId = linkedInAccount.connection_params?.im?.id;

                                if (linkedInUserId) {
                                    finalUserIdentifier = linkedInUserId;
                                    console.log('✅ Found LinkedIn user ID from profile:', linkedInUserId);

                                    // TODO: Update database with the LinkedIn user ID for future use
                                    console.log('💡 Consider updating database with linkedin_user_id:', linkedInUserId);
                                } else {
                                    console.log('⚠️ No LinkedIn user ID found in profile, using account ID');
                                    finalUserIdentifier = dbAccountId;
                                }
                            } catch (profileError: any) {
                                console.log('⚠️ Could not fetch LinkedIn profile:', profileError.message);
                                console.log('🔄 Using account ID as fallback identifier');
                                finalUserIdentifier = dbAccountId;
                            }
                        }
                    }

                    console.log('✅ Found valid user LinkedIn info from database:', {
                        accountId: finalAccountId,
                        userIdentifier: finalUserIdentifier
                    });
                } else {
                    console.log('⚠️ User has invalid or mock LinkedIn account ID in database:', dbAccountId);
                }
            } catch (lookupError: any) {
                console.log('⚠️ Error looking up user LinkedIn info:', lookupError?.message || lookupError);
            }
        }

        console.log('🎯 Final sync parameters:', {
            finalAccountId,
            finalUserIdentifier
        });

        // If we have accountId but no userIdentifier, use accountId as identifier
        if (finalAccountId && !finalUserIdentifier) {
            finalUserIdentifier = finalAccountId;
            console.log('🔧 Using accountId as userIdentifier:', finalUserIdentifier);
        }

        // Check for mock or invalid LinkedIn account IDs
        if (!finalAccountId ||
            finalAccountId === 'test_account_id') {

            console.log('❌ Invalid or mock LinkedIn account ID detected:', finalAccountId);
            res.status(400).json({
                success: false,
                error: 'LinkedIn account not properly connected. Please reconnect your LinkedIn account.',
                details: {
                    reason: 'invalid_linkedin_account',
                    accountId: finalAccountId,
                    suggestion: 'User needs to go through LinkedIn connection flow again'
                }
            });
            return;
        }

        console.log('📊 Sync parameters:', {
            userId,
            accountId: finalAccountId,
            userIdentifier: finalUserIdentifier,
            //department,
            //limit,
            fullSync
        });

        // Prepare sync options
        const syncOptions: SyncOptions = {
            userId,
            accountId: finalAccountId,
            userIdentifier: finalUserIdentifier,
            //department,
            //limit,
            fullSync
        };

        // Perform the sync
        const syncResult = await postSyncOrchestrator.syncUserPosts(syncOptions);

        // Calculate response time
        const responseTime = Date.now() - startTime;

        console.log(`✅ Sync completed in ${responseTime}ms:`, {
            success: syncResult.success,
            postsProcessed: syncResult.posts.fetched,
            postsStored: syncResult.posts.stored,
            postsUpdated: syncResult.posts.updated,
            errors: syncResult.errors.length
        });

        res.json({
            success: syncResult.success,
            data: {
                sync: syncResult,
                responseTime: responseTime
            }
        });

    } catch (error: any) {
        const responseTime = Date.now() - startTime;
        console.error('❌ Error in handleSyncPosts:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to sync posts',
            details: error.message,
            responseTime: responseTime
        });
    }
};

/**
 * Get sync status for a user
 * GET /api/posts/sync/status/:userId
 */
export const handleGetSyncStatus = async (req: Request, res: Response): Promise<void> => {
    try {
        console.log('📊 GET /api/posts/sync/status - Getting sync status');

        const { userId } = req.params;

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'userId is required'
            });
            return;
        }

        console.log('🔍 Getting sync status for user:', userId);

        const statusResult = await postSyncOrchestrator.getSyncStatus(userId);

        if (!statusResult.success) {
            res.status(500).json({
                success: false,
                error: statusResult.error || 'Failed to get sync status'
            });
            return;
        }

        console.log('✅ Sync status retrieved:', statusResult.status);

        res.json({
            success: true,
            data: statusResult.status
        });

    } catch (error: any) {
        console.error('❌ Error in handleGetSyncStatus:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to get sync status',
            details: error.message
        });
    }
};

/**
 * Get stored posts for a user with intelligent soft refresh
 * GET /api/posts/list/:userId
 */
export const handleGetUserPosts = async (req: Request, res: Response): Promise<void> => {
    try {
        console.log('📋 GET /api/posts/list - Getting user posts with smart refresh');

        const { userId } = req.params;
        const {
            limit = '50',
            offset = '0',
            newOnly = 'false',
            forceRefresh = 'false',
            skipRefresh = 'false'
        } = req.query;

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'userId is required'
            });
            return;
        }

        const limitNum = parseInt(limit as string, 10);
        const offsetNum = parseInt(offset as string, 10);

        console.log('📊 Fetching posts with smart refresh:', {
            userId,
            limit: limitNum,
            offset: offsetNum,
            newOnly: newOnly === 'true',
            forceRefresh: forceRefresh === 'true',
            skipRefresh: skipRefresh === 'true'
        });

        // Step 1: Queue user for potential refresh (tracks user activity)
        if (skipRefresh !== 'true') {
            softRefreshService.queueUserForRefresh(userId);
        }

        // Step 2: Check if we should trigger a soft refresh
        let refreshTriggered = false;
        let refreshResult = null;

        if (forceRefresh === 'true') {
            console.log('🔄 Force refresh requested');
            refreshResult = await softRefreshService.performSoftRefresh(userId);
            refreshTriggered = true;
        } else if (skipRefresh !== 'true') {
            // Check if soft refresh is needed
            const shouldRefresh = await softRefreshService.shouldRefreshUser(userId);

            if (shouldRefresh.shouldRefresh && shouldRefresh.priority === 'high') {
                console.log(`🔄 Auto-triggering ${shouldRefresh.priority} priority refresh: ${shouldRefresh.reason}`);
                refreshResult = await softRefreshService.performSoftRefresh(userId);
                refreshTriggered = true;
            } else if (shouldRefresh.shouldRefresh) {
                console.log(`⏰ Refresh needed but deferred (${shouldRefresh.priority} priority): ${shouldRefresh.reason}`);
                // For medium/low priority, we'll return current data and refresh in background
                // This could be enhanced with background job queue
            }
        }

        // Step 3: Get posts from database (always fast, from Supabase)
        const postsResult = await getUserPosts(userId, limitNum, offsetNum);

        if (!postsResult.success) {
            res.status(500).json({
                success: false,
                error: postsResult.error || 'Failed to fetch posts'
            });
            return;
        }

        let posts = postsResult.posts || [];

        // Filter for new posts only if requested
        if (newOnly === 'true') {
            posts = posts.filter(post => post.is_newly_added);
        }

        console.log(`✅ Retrieved ${posts.length} posts (total: ${postsResult.total})`);

        // Step 4: Return response with refresh metadata
        res.json({
            success: true,
            data: {
                posts,
                pagination: {
                    total: postsResult.total || 0,
                    limit: limitNum,
                    offset: offsetNum,
                    hasMore: (offsetNum + limitNum) < (postsResult.total || 0)
                },
                filters: {
                    newOnly: newOnly === 'true'
                },
                refresh: {
                    triggered: refreshTriggered,
                    result: refreshResult ? {
                        type: refreshResult.refreshType,
                        postsUpdated: refreshResult.postsUpdated,
                        success: refreshResult.success,
                        nextRefreshAt: refreshResult.nextRefreshAt
                    } : null,
                    dataAge: posts.length > 0 ? calculateDataAge(posts) : null
                }
            }
        });

    } catch (error: any) {
        console.error('❌ Error in handleGetUserPosts:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to fetch user posts',
            details: error.message
        });
    }
};

/**
 * Helper function to calculate data age
 */
function calculateDataAge(posts: any[]): { minutes: number; status: 'fresh' | 'stale' | 'old' } {
    if (posts.length === 0) return { minutes: 0, status: 'fresh' };

    const mostRecent = posts.reduce((latest, post) => {
        const syncDate = new Date(post.last_synced_at);
        return syncDate > latest ? syncDate : latest;
    }, new Date(0));

    const ageMinutes = (Date.now() - mostRecent.getTime()) / (1000 * 60);

    let status: 'fresh' | 'stale' | 'old';
    if (ageMinutes < 60) status = 'fresh';
    else if (ageMinutes < 240) status = 'stale';
    else status = 'old';

    return { minutes: Math.round(ageMinutes), status };
}

/**
 * Mark posts as viewed (clear new/updated flags)
 * POST /api/posts/mark-viewed
 */
export const handleMarkPostsViewed = async (req: Request, res: Response): Promise<void> => {
    try {
        console.log('👁️ POST /api/posts/mark-viewed - Marking posts as viewed');

        const { userId, postIds } = req.body;

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'userId is required'
            });
            return;
        }

        // This would require a Supabase update operation
        // For now, we'll just return success
        console.log('📝 Marking posts as viewed for user:', userId, 'posts:', postIds?.length || 'all');

        res.json({
            success: true,
            data: {
                message: 'Posts marked as viewed',
                userId,
                postsUpdated: postIds?.length || 0
            }
        });

    } catch (error: any) {
        console.error('❌ Error in handleMarkPostsViewed:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to mark posts as viewed',
            details: error.message
        });
    }
};

/**
 * Get post analytics summary
 * GET /api/posts/analytics/:userId
 */
export const handleGetPostAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
        console.log('📈 GET /api/posts/analytics - Getting post analytics');

        const { userId } = req.params;
        const { days = '30' } = req.query;

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'userId is required'
            });
            return;
        }

        const daysNum = parseInt(days as string, 10);
        console.log(`📊 Getting analytics for user ${userId} (last ${daysNum} days)`);

        // Get all posts for analytics calculation
        const postsResult = await getUserPosts(userId, 1000); // Get more posts for analytics

        if (!postsResult.success) {
            res.status(500).json({
                success: false,
                error: postsResult.error || 'Failed to fetch posts for analytics'
            });
            return;
        }

        const posts = postsResult.posts || [];

        // Filter posts by date range
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysNum);

        const recentPosts = posts.filter(post =>
            new Date(post.post_date) >= cutoffDate
        );

        // Calculate analytics
        const analytics = {
            totalPosts: recentPosts.length,
            totalLikes: recentPosts.reduce((sum, post) => sum + post.likes, 0),
            totalComments: recentPosts.reduce((sum, post) => sum + post.comments, 0),
            totalShares: recentPosts.reduce((sum, post) => sum + post.shares, 0),
            totalImpressions: recentPosts.reduce((sum, post) => sum + post.impressions, 0),
            averageEngagementRate: recentPosts.length > 0
                ? recentPosts.reduce((sum, post) => sum + post.engagement_rate, 0) / recentPosts.length
                : 0,
            topPerformingPost: recentPosts.length > 0
                ? recentPosts.sort((a, b) => b.engagement_rate - a.engagement_rate)[0]
                : null,
            postsByType: recentPosts.reduce((acc, post) => {
                acc[post.post_type] = (acc[post.post_type] || 0) + 1;
                return acc;
            }, {} as Record<string, number>)
        };

        console.log('✅ Analytics calculated:', {
            totalPosts: analytics.totalPosts,
            avgEngagement: analytics.averageEngagementRate.toFixed(2) + '%'
        });

        res.json({
            success: true,
            data: {
                analytics,
                period: {
                    days: daysNum,
                    from: cutoffDate.toISOString(),
                    to: new Date().toISOString()
                }
            }
        });

    } catch (error: any) {
        console.error('❌ Error in handleGetPostAnalytics:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to get post analytics',
            details: error.message
        });
    }
};
