import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from "sonner";
import { Loader2, Unlink, <PERSON>edin, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { LinkedInConnectionStatus } from '@/types/auth';
import { client } from '@/integrations/unipile';
import PhylloSDK from '@/integrations/phylloSDK';

type ConnectionStatus = 'checking' | 'connected' | 'disconnected' | 'connecting';

const LinkedInConnector = () => {
  const { user, profile } = useAuth();
  const [status, setStatus] = useState<ConnectionStatus>('checking');
  const [phylloId, setPhylloId] = useState<string | undefined>(undefined);
  
  useEffect(() => {
    checkLinkedInConnection();
  }, []);
  
  const checkLinkedInConnection = async () => {
    setStatus('checking');
    
    try {
      console.log('profile', profile);
      // If we have a linkedin_url in the profile, we'll consider it connected
      if (profile?.linkedin_url) {
        setStatus('connected');
        setPhylloId(profile?.phyllo_id);
      } else {
        setStatus('disconnected');
      }
    } catch (error) {
      console.error("Error checking LinkedIn connection:", error);
      setStatus('disconnected');
      toast.error("Failed to check LinkedIn connection status");
    }
  };
  
  const connectLinkedIn = async () => {
    setStatus('connecting');
    
    try {
      const phylloSDK = new PhylloSDK();
      await phylloSDK.openPhylloSDK(user.id);
      
    } catch (error) {
      console.error("Error connecting to LinkedIn:", error);
      setStatus('disconnected');
      toast.error("Failed to connect LinkedIn account");
    }
  };
  
  const disconnectLinkedIn = async () => {
    try {
      if (user) {
        const { error } = await supabase
          .from('profiles')
          .update({ 
            linkedin_url: null,
            linkedin_connection_status: 'disconnected' as LinkedInConnectionStatus,
            // We'll keep the phyllo_id for reference but mark the connection as disconnected
          })
          .eq('id', user.id);
        
        if (error) {
          throw error;
        }
        
        setStatus('disconnected');
        toast.success("LinkedIn account disconnected");
        
        // Refresh the profile in the auth context
        if (profile) {
          // In a real implementation, you would use a proper method to update the profile in context
          window.location.reload();
        }
      }
    } catch (error) {
      console.error("Error disconnecting from LinkedIn:", error);
      toast.error("Failed to disconnect LinkedIn account");
    }
  };
  
  return (
    <Card className="mb-8 overflow-hidden">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-xl font-semibold">
          <Linkedin className="mr-2 h-5 w-5" />
          LinkedIn Connection
        </CardTitle>
        <CardDescription>
          Connect your LinkedIn account to track your post performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        {status === 'checking' && (
          <div className="flex items-center gap-2 py-2">
            <Loader2 className="h-5 w-5 animate-spin text-gray-500" />
            <span>Checking connection status...</span>
          </div>
        )}
        
        {status === 'connected' && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">LinkedIn Connected</h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>Your LinkedIn account is connected and ready to use.</p>
                  {profile?.linkedin_url && (
                    <p className="mt-1">
                      Profile: <a href={profile.linkedin_url} target="_blank" rel="noopener noreferrer" className="font-medium underline">
                        {profile.linkedin_url.replace('https://linkedin.com/in/', '')}
                      </a>
                    </p>
                  )}
                  {phylloId && (
                    <p className="mt-1">
                      Phyllo ID: <span className="font-mono text-xs bg-green-100 px-2 py-0.5 rounded">{phylloId}</span>
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
        
        {status === 'disconnected' && (
          <div className="rounded-md bg-gray-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-gray-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-800">LinkedIn Not Connected</h3>
                <div className="mt-2 text-sm text-gray-700">
                  <p>Connect your LinkedIn account to track your post performance and engagement.</p>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {status === 'connecting' && (
          <div className="flex flex-col items-center justify-center gap-3 py-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-center text-sm text-gray-600">
              Connecting to LinkedIn...
              <br />
              <span className="text-xs">You'll be redirected to LinkedIn to authorize access</span>
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter className="bg-gray-50 px-6 py-4">
        {status === 'connected' && (
          <Button 
            variant="outline" 
            onClick={disconnectLinkedIn}
            className="w-full sm:w-auto"
          >
            <Unlink className="mr-2 h-4 w-4" />
            Disconnect LinkedIn
          </Button>
        )}
        
        {status === 'disconnected' && (
          <Button 
            onClick={connectLinkedIn} 
            className="w-full bg-[#0A66C2] hover:bg-[#084E96] sm:w-auto"
          >
            <Linkedin className="mr-2 h-4 w-4" />
            Connect with LinkedIn
          </Button>
        )}
        
        {(status === 'checking' || status === 'connecting') && (
          <Button 
            disabled
            className="w-full sm:w-auto"
          >
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {status === 'checking' ? 'Checking...' : 'Connecting...'}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default LinkedInConnector;
