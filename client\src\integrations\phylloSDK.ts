import { createUser, createUserToken } from "./phyllo";
import { CLIENT_URL } from '@/config/env';

// Declare the Phyllo SDK types
declare global {
  interface Window {
    PhylloConnect: {
      initialize: (config: PhylloConfig) => PhylloConnect;
    };
  }
}

interface PhylloConfig {
  clientDisplayName: string;
  environment: 'sandbox' | 'production' | 'staging';
  userId: string;
  token: string;
  workPlatformId: string | null;
  redirect: boolean;
  redirectURL?: string;
}

interface PhylloConnect {
  on: (event: PhylloEvent, callback: (...args: unknown[]) => void) => void;
  open: () => void;
}

type PhylloEvent = 
  | 'accountConnected'
  | 'accountDisconnected'
  | 'tokenExpired'
  | 'exit'
  | 'connectionFailure';

class PhylloSDK {
  async openPhylloSDK(userId?: string): Promise<void> {
    try {
      const timeStamp = new Date();
      const userIdResult = await createUser("CompanyVoice", userId ?? timeStamp.getTime().toString());
      console.log({userIdResult})
      if (typeof userIdResult !== 'string') {
        throw new Error('Failed to get valid user ID from Phyllo');
      }

      const tokenResult = await createUserToken(userIdResult);
      
      if (typeof tokenResult !== 'string') {
        throw new Error('Failed to get valid token from Phyllo');
      }

      const config: PhylloConfig = {
        clientDisplayName: "CompanyVoice",
        environment: "staging",
        userId: userIdResult,
        token: tokenResult,
        workPlatformId: null,
        redirect: true,
        redirectURL: CLIENT_URL
      };

      const phylloConnect = window.PhylloConnect.initialize(config);

      // phylloConnect.open();
      phylloConnect.on("accountConnected", (accountId: string, workplatformId: string, userId: string) => {
        console.log(`onAccountConnected: ${accountId}, ${workplatformId}, ${userId}`);
      });

      phylloConnect.on("accountDisconnected", (accountId: string, workplatformId: string, userId: string) => {
        console.log(`onAccountDisconnected: ${accountId}, ${workplatformId}, ${userId}`);
      });

      phylloConnect.on("tokenExpired", (userId: string) => {
        console.log(`onTokenExpired: ${userId}`);
        if (window.confirm("Your session has expired, but we can help you fix it")) {
          // Reinitiating Phyllo SDK
          localStorage.removeItem("PHYLLO_SDK_TOKEN");
          this.openPhylloSDK();
        } else {
          window.location.href = "/";
        }
      });

      phylloConnect.on("exit", (reason: string, userId: string) => {
        console.log(`onExit: ${reason}, ${userId}`);
        alert("Phyllo SDK exit reason: " + reason);
        window.location.href = "/accounts";
      });

      phylloConnect.on("connectionFailure", (reason: string, workplatformId: string, userId: string) => {
        console.log(`onConnectionFailure: ${reason}, ${workplatformId}, ${userId}`);
        alert("WorkPlatform connection failure reason: " + reason);
      });

      phylloConnect.open();
    } catch (error) {
      console.error('Error initializing Phyllo SDK:', error.message, error);
      throw error;
    }
  }
}

export default PhylloSDK;