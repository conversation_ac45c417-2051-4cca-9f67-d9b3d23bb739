
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Settings, User, Menu, LogOut } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Badge } from '@/components/ui/badge';

const Navbar = () => {
  const {
    user,
    signOut,
    isAdmin
  } = useAuth();
  
  // Debug user role in navbar
  // console.log('Navbar - User:', user);
  // console.log('Navbar - Is admin:', isAdmin());
  
  // Function to scroll to the approvals section in admin dashboard
  const scrollToApprovals = () => {
    document.getElementById('approvals-section')?.scrollIntoView({ behavior: 'smooth' });
  };
  
  return <nav className="sticky top-0 z-30 w-full bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 md:px-6 flex items-center justify-between h-16 relative">
        <div className="flex items-center space-x-4">
          <Link to="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-md bg-black flex items-center justify-center">
              <span className="font-bold text-white">CV</span>
            </div>
            <span className="font-semibold text-lg hidden md:inline-block text-black">CompanyVoice</span>
          </Link>
        </div>
        
        {user ? <div className="hidden md:flex items-center space-x-6">
            {!isAdmin() && <Link to="/user-dashboard" className="text-gray-600 hover:text-black transition-colors">
                Dashboard
              </Link>}
            {isAdmin() && <Link to="/admin-dashboard" className="text-gray-600 hover:text-black transition-colors">
                Admin Dashboard
              </Link>}
          </div> : <div className="absolute right-16 top-1/2 transform -translate-y-1/2 hidden md:block">
            <Link to="/login" className="text-black hover:text-black/90">
              Login
            </Link>
          </div>}
        
        <div className="flex items-center space-x-3">
          {user && <>
              {isAdmin() ? (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="text-enterprise-gray-600 relative"
                  onClick={scrollToApprovals}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-bell">
                    <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                    <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
                  </svg>
                  {/* We're using the same notification badge style as in AdminDashboardLayout */}
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 opacity-0"
                    id="navbar-notification-badge"
                  >
                    0
                  </Badge>
                </Button>
              ) : (
                <Button variant="ghost" size="icon" className="text-gray-600 hover:text-black">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-bell">
                    <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                    <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
                  </svg>
                </Button>
              )}
              <Link to="/settings">
                <Button variant="ghost" size="icon" className="text-gray-600 hover:text-black">
                  <Settings size={20} />
                </Button>
              </Link>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <User size={20} className="text-gray-600" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Link to="/profile" className="flex w-full">Profile</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Link to="/settings" className="flex w-full">Settings</Link>
                  </DropdownMenuItem>
                  {isAdmin() && (
                    <DropdownMenuItem>
                      <Link to="/admin-dashboard" className="flex w-full">Admin Dashboard</Link>
                    </DropdownMenuItem>
                  )}
                  {!isAdmin() && (
                    <DropdownMenuItem>
                      <Link to="/user-dashboard" className="flex w-full">Dashboard</Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => signOut()}>
                    <LogOut className="mr-2 h-4 w-4" /> Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>}
          
          {!user && <Link to="/login" className="text-black hover:text-black/90 md:hidden">
              Login
            </Link>}
          
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu size={20} />
          </Button>
        </div>
      </div>
    </nav>;
};

export default Navbar;
