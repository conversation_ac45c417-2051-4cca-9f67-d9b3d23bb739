import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

const ScrollableDialogDemo: React.FC = () => {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Test Scrollable Dialog</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Scrollable Dialog Demo</DialogTitle>
          <DialogDescription>
            This dialog demonstrates scrollable content on small screens.
            Try resizing your browser window or viewing on mobile.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input id="name" placeholder="Enter your name" />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" placeholder="Enter your email" />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input id="phone" type="tel" placeholder="Enter your phone" />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="address">Address</Label>
            <Textarea id="address" placeholder="Enter your address" rows={3} />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="bio">Bio</Label>
            <Textarea id="bio" placeholder="Tell us about yourself" rows={4} />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea id="notes" placeholder="Any additional information" rows={3} />
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">Important Information</h4>
            <p className="text-sm text-blue-700 mb-2">
              This is a long form that demonstrates how the dialog handles overflow content.
              On small screens, the content area becomes scrollable while keeping the header
              and footer visible.
            </p>
            <ul className="text-sm text-blue-700 space-y-1 list-disc list-inside">
              <li>Header stays at the top</li>
              <li>Footer stays at the bottom</li>
              <li>Content area scrolls independently</li>
              <li>Works on all screen sizes</li>
              <li>Maintains good user experience</li>
            </ul>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-800 mb-2">Mobile Optimization</h4>
            <p className="text-sm text-yellow-700">
              On mobile devices, the dialog takes up most of the screen height (95vh)
              and positions itself near the top for better accessibility.
            </p>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-medium text-green-800 mb-2">Responsive Design</h4>
            <p className="text-sm text-green-700">
              The dialog automatically adjusts its layout based on screen size,
              ensuring optimal viewing experience across all devices.
            </p>
          </div>
        </div>

        <DialogFooter className="space-y-2">
          <Button type="submit" className="w-full">Save Changes</Button>
          <Button type="button" variant="outline" onClick={() => setOpen(false)} className="w-full">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ScrollableDialogDemo;
