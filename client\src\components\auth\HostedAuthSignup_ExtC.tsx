import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { Mail, Lock, User, LinkIcon, LogIn } from 'lucide-react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import { UnipileHostedAuth } from './UnipileHostedAuth';
import { useAuthState } from '@/hooks/useAuthState';
import { showSignupCompletionToasts } from '@/utils/toastHelpers';
import PageBackground from '@/components/layout/PageBackground';
import { getApiUrl } from '@/config/env';

const formSchema = z.object({
  linkedinEmail: z.string().email('Please enter a valid LinkedIn email'),
  useSameCredentials: z.boolean().default(false),
  email: z.string().email('Please enter a valid email').optional(),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  linkedinUrl: z.string().url('Please enter a valid LinkedIn URL').optional(),
}).refine((data) => {
  // When using same credentials, only LinkedIn email is used, but password is still required for the app
  if (data.useSameCredentials) {
    return data.password && data.confirmPassword;
  }
  // When not using same credentials, both email and password are required
  return data.email && data.password && data.confirmPassword;
}, {
  message: "Email and password are required",
  path: ["email"]
}).refine((data) => {
  if (data.password !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

type FormValues = z.infer<typeof formSchema>;

const HostedAuthSignup: React.FC = () => {
  const navigate = useNavigate();
  const { signUp } = useAuthState();
  const [useSameCredentials, setUseSameCredentials] = useState(false);
  const [showLinkedInAuth, setShowLinkedInAuth] = useState(false);
  const [linkedinAccountId, setLinkedinAccountId] = useState<string | null>(null);
  const [isLinkedInVerified, setIsLinkedInVerified] = useState(false);
  const [verifiedLinkedInEmail, setVerifiedLinkedInEmail] = useState<string>('');
  const [linkedInProfileData, setLinkedInProfileData] = useState<any>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      useSameCredentials: false,
      linkedinEmail: "",
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
      linkedinUrl: "",
    },
  });

  // Watch the LinkedIn email field for validation
  const linkedinEmail = form.watch('linkedinEmail');
  const isEmailValid = linkedinEmail && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(linkedinEmail);

  const onSubmit = async (values: FormValues) => {
    if (!isLinkedInVerified || !linkedinAccountId) {
      toast.error('Please verify your LinkedIn account first');
      setShowLinkedInAuth(true);
      return;
    }

    // Email logic: Use LinkedIn email if same credentials, otherwise use provided email
    const finalEmail = useSameCredentials ? values.linkedinEmail : values.email!;
    // Password: Always use the password provided by user (never LinkedIn password)
    const finalPassword = values.password!;

    try {
      const result = await signUp({
        email: finalEmail,
        password: finalPassword,
        options: {
          data: {
            firstName: values.firstName,
            lastName: values.lastName,
            linkedinUrl: values.linkedinUrl || '', // Optional field
            linkedinEmail: values.linkedinEmail,
            linkedinAccountId: linkedinAccountId,
          },
        },
      });

      if (result?.error) {
        toast.error(result.error.message);
      } else {
        showSignupCompletionToasts(navigate);
      }
    } catch (err) {
      console.error(err);
      toast.error("Something went wrong during signup");
    }
  };

  const handleLinkedInSuccess = (accountId: string, isReconnection?: boolean) => {
    console.log('✅ LinkedIn verification successful:', accountId);

    // Store verification data
    const currentLinkedInEmail = form.getValues('linkedinEmail');
    setLinkedinAccountId(accountId);
    setIsLinkedInVerified(true);
    setVerifiedLinkedInEmail(currentLinkedInEmail);
    setShowLinkedInAuth(false);

    // If user wants to use same credentials, set the app email to LinkedIn email
    if (useSameCredentials) {
      form.setValue('email', currentLinkedInEmail);
    }

    if (isReconnection) {
      toast.success('You already have an account! Account updated successfully. You can now complete your signup.');
    } else {
      toast.success('LinkedIn account verified successfully! You can now complete your signup.');
    }
  };

  const handleLinkedInError = (error: string) => {
    console.error('❌ LinkedIn verification failed:', error);
    toast.error(`LinkedIn verification failed: ${error}`);
    setShowLinkedInAuth(false);
  };

  const handleLinkedInVerify = async () => {
    if (!isEmailValid) {
      toast.error('Please enter a valid LinkedIn email address');
      return;
    }

    // First, check if user already has a verified account
    try {
      console.log('🔍 Checking for existing account before verification...');

      const response = await fetch(getApiUrl('/auth/get-account-by-email'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: linkedinEmail }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Found existing account:', data);

        const message = data.shouldReconnect
          ? 'Existing LinkedIn account found and reconnected!'
          : 'Account found and automatically verified!';

        toast.success(message);

        // Automatically set as verified without showing auth flow
        handleLinkedInSuccess(data.account_id, data.shouldReconnect || true);
        return;
      }
    } catch (error) {
      console.log('⚠️ Error checking existing account:', error);
      // Continue with normal verification flow
    }

    // No existing account found, proceed with verification
    console.log('🔄 No existing account found, starting verification flow...');
    setShowLinkedInAuth(true);
  };

  if (showLinkedInAuth) {
    return (
      <div className="flex flex-col min-h-screen relative overflow-hidden bg-red-500">
        <PageBackground />
        <div className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="w-full max-w-lg p-8 space-y-8 bg-white rounded-lg shadow-xl bg-opacity-95 backdrop-blur-sm border border-opacity-20">
            <div className="text-center">
              <h1 className="text-3xl font-bold mb-2 text-black">
                Verify LinkedIn Account
              </h1>
              <p className="text-gray-700">
                Complete LinkedIn verification to continue signup
              </p>
            </div>

            <UnipileHostedAuth
              userId={form.getValues('linkedinEmail')}
              onSuccess={(account) => {
                const isReconnection = account.status === 'RECONNECTED';
                handleLinkedInSuccess(account.account_id, isReconnection);
              }}
              onError={handleLinkedInError}
              providers={['LINKEDIN']}
            />

            <div className="text-center">
              <Button
                onClick={() => setShowLinkedInAuth(false)}
                variant="outline"
                className="w-full rounded-xl py-3 px-6 font-semibold border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300"
              >
                ← Back to Signup Form
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen relative overflow-hidden">
      <PageBackground />
      <div className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-xl bg-opacity-95 backdrop-blur-sm border border-opacity-20">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-2 text-black">
              Create Your Account
            </h1>
            <p className="text-gray-700">
              Join CompanyVoice to amplify your brand
            </p>
          </div>



          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* LinkedIn Email or Verified Status */}
              {!isLinkedInVerified ? (
                <FormField
                  control={form.control}
                  name="linkedinEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black">
                        LinkedIn Email
                        <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input
                            placeholder="<EMAIL>"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <p className="text-sm text-gray-600">
                        Please add your LinkedIn email here to check whether your account exists or not
                      </p>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ) : (
                <div className="space-y-2">
                  <FormLabel className="text-black">LinkedIn Email</FormLabel>
                  <div className="flex items-center space-x-3 p-4 bg-green-50 border-2 border-green-200 rounded-xl">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-semibold text-green-800">LinkedIn Verified</p>
                      <p className="text-sm text-green-600">{verifiedLinkedInEmail}</p>
                    </div>
                    <div className="flex-shrink-0">
                      <LinkIcon className="h-5 w-5 text-green-600" />
                    </div>
                  </div>
                </div>
              )}

              {/* Verify LinkedIn Button - Only show when not verified */}
              {!isLinkedInVerified && (
                <div className="space-y-3">
                  <Button
                    type="button"
                    onClick={handleLinkedInVerify}
                    disabled={!isEmailValid}
                    className={`w-full rounded-xl py-3 px-6 font-semibold text-white transition-all duration-300 transform ${isEmailValid
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl hover:scale-105 ring-2 ring-blue-300 ring-opacity-50'
                        : 'bg-gray-400 cursor-not-allowed'
                      } disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none`}
                  >
                    <LinkIcon className="mr-2 h-5 w-5" />
                    Verify LinkedIn Account
                  </Button>

                  {/* Dynamic helper text */}
                  <div className="text-center">
                    {!linkedinEmail ? (
                      <p className="text-sm text-gray-500">
                        Enter your LinkedIn email above to verify your account
                      </p>
                    ) : !isEmailValid ? (
                      <p className="text-sm text-red-500">
                        Please enter a valid email address
                      </p>
                    ) : (
                      <p className="text-sm text-blue-600 font-medium animate-pulse">
                        ✨ Ready to verify! Click the button above
                      </p>
                    )}
                  </div>
                </div>
              )}

              <FormItem>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={useSameCredentials}
                    onCheckedChange={(checked) => {
                      setUseSameCredentials(!!checked);
                      form.setValue("useSameCredentials", !!checked);
                      if (checked) {
                        // Use verified LinkedIn email if available, otherwise current form value
                        const emailToUse = verifiedLinkedInEmail || form.getValues("linkedinEmail");
                        form.setValue("email", emailToUse);
                      }
                    }}
                  />
                  <FormLabel className="text-sm text-black">
                    Use LinkedIn email for CompanyVoice login
                  </FormLabel>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  ℹ️ You'll still need to create a unique password for your CompanyVoice account
                </p>
              </FormItem>

              {/* App Email Field - Only show when not using LinkedIn email */}
              {!useSameCredentials && (
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black">CompanyVoice Email</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input
                            placeholder="<EMAIL>"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Password Fields - Always required */}
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">
                      CompanyVoice Password
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          type="password"
                          placeholder="At least 8 characters"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <p className="text-xs text-gray-500 mt-1">
                      Create a unique password for your CompanyVoice account
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">Confirm Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          type="password"
                          placeholder="Confirm your password"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Name and LinkedIn URL */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black">First Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input placeholder="John" className="pl-10" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black">Last Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input placeholder="Doe" className="pl-10" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* LinkedIn URL - Optional field, only if needed */}
              <FormField
                control={form.control}
                name="linkedinUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">
                      LinkedIn Profile URL
                      <span className="text-gray-500 text-sm ml-1">(Optional)</span>
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <LinkIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          placeholder="https://linkedin.com/in/your-profile"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <p className="text-xs text-gray-500 mt-1">
                      We'll try to get this from your LinkedIn account automatically
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                disabled={!isLinkedInVerified}
                className={`w-full rounded-xl py-4 px-8 text-lg font-semibold transition-all duration-300 transform ${isLinkedInVerified
                    ? 'bg-gradient-to-r from-gray-800 to-black text-white hover:from-black hover:to-gray-900 shadow-lg hover:shadow-xl hover:scale-105'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  } disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none`}
              >
                {isLinkedInVerified ? '🚀 Create Account' : '🔒 Verify LinkedIn First'}
              </Button>

              <div className="text-center text-gray-700">
                <p>Already have an account?</p>
                <Link
                  to="/login"
                  className="flex items-center justify-center text-black hover:text-black/90 mt-2"
                >
                  <LogIn className="mr-2 h-4 w-4" /> Sign In
                </Link>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default HostedAuthSignup;
