import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Shield, Clock, CheckCircle, XCircle, Loader2, AlertTriangle } from 'lucide-react';
import { getApiUrl } from '@/config/env';

const VerificationWaiting: React.FC = () => {
  const [searchParams] = useSearchParams();
  const userId = searchParams.get('user');
  const type = searchParams.get('type');
  
  const [waitingTime, setWaitingTime] = useState(0);
  const [verificationStatus, setVerificationStatus] = useState<'waiting' | 'checking' | 'success' | 'failed'>('waiting');
  const [accountId, setAccountId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  useEffect(() => {
    console.log('🔄 VerificationWaiting page loaded:', { userId, type });

    if (!userId) {
      setError('Missing user identifier');
      setVerificationStatus('failed');
      return;
    }

    // Start timer
    const timer = setInterval(() => {
      setWaitingTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [userId, type]);

  // Poll for account creation
  const checkAccountCreation = async () => {
    if (isPolling || !userId) return;

    setIsPolling(true);
    setVerificationStatus('checking');

    try {
      console.log('🔍 Checking account creation for user:', userId);

      const response = await fetch(getApiUrl('/auth/verify-hosted-auth-account'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          internalUserId: userId,
          userEmail: userId
        }),
      });

      const data = await response.json();
      console.log('📊 Account verification response:', data);

      if (response.ok && data.success && data.account_id) {
        // Account successfully created and verified
        console.log('✅ LinkedIn account verified successfully:', data.account_id);
        setAccountId(data.account_id);
        setVerificationStatus('success');
        
        // Close popup and notify parent after a short delay
        setTimeout(() => {
          handleClose('success', data.account_id);
        }, 2000);
      } else {
        console.log('⏳ Account not yet created, continuing to wait...');
        setVerificationStatus('waiting');
      }
    } catch (error: any) {
      console.error('💥 Error checking account creation:', error);
      setError(`Error checking account: ${error.message}`);
      setVerificationStatus('failed');
    } finally {
      setIsPolling(false);
    }
  };

  const handleClose = (status: string, accountId?: string) => {
    try {
      // If this is a popup window, close it and notify parent
      if (window.opener && !window.opener.closed) {
        console.log('📤 Sending verification completion message to parent:', {
          type: 'VERIFICATION_COMPLETE',
          status: status,
          verificationType: type,
          accountId: accountId,
          userAction: status === 'success' ? 'auto_verified' : 'failed'
        });

        // Send message to parent window with error handling
        try {
          window.opener.postMessage({
            type: 'VERIFICATION_COMPLETE',
            status: status,
            verificationType: type,
            accountId: accountId,
            userAction: status === 'success' ? 'auto_verified' : 'failed',
            timestamp: new Date().toISOString()
          }, window.location.origin);
        } catch (postMessageError) {
          console.warn('⚠️ Failed to send message to parent window:', postMessageError);
          // This is expected if parent window is closed/navigated away
        }

        // Wait a bit for the message to be sent, then close
        setTimeout(() => {
          console.log('🔒 Closing popup window');
          window.close();
        }, 500);
      } else {
        console.log('⚠️ No opener window found, redirecting to login');
        // If not a popup, redirect to login page
        setTimeout(() => {
          window.location.href = '/login';
        }, 2000);
      }
    } catch (error) {
      console.error('💥 Error handling verification redirect:', error);
      // Fallback: redirect to login
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);
    }
  };

  const handleManualCheck = () => {
    checkAccountCreation();
  };

  const handleCancel = () => {
    console.log('❌ User cancelled verification');
    setVerificationStatus('failed');
    setError('Verification cancelled by user');
    handleClose('cancelled');
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusIcon = () => {
    switch (verificationStatus) {
      case 'waiting':
        return <Shield className="h-16 w-16 text-blue-500 animate-pulse" />;
      case 'checking':
        return <Loader2 className="h-16 w-16 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-16 w-16 text-green-500" />;
      case 'failed':
        return <XCircle className="h-16 w-16 text-red-500" />;
      default:
        return <Shield className="h-16 w-16 text-gray-500" />;
    }
  };

  const getStatusMessage = () => {
    switch (verificationStatus) {
      case 'waiting':
        return {
          title: 'Complete LinkedIn Verification',
          message: 'Please complete the LinkedIn verification process (CAPTCHA, OTP, or security check) in the LinkedIn page. Once you successfully log in, click "Check Account Creation" below.',
          color: 'text-blue-700'
        };
      case 'checking':
        return {
          title: 'Checking Account Creation',
          message: 'Verifying that your LinkedIn account was successfully created and is accessible...',
          color: 'text-blue-700'
        };
      case 'success':
        return {
          title: 'Verification Completed!',
          message: `Your LinkedIn account has been successfully created and verified. Account ID: ${accountId}`,
          color: 'text-green-700'
        };
      case 'failed':
        return {
          title: 'Verification Failed',
          message: error || 'There was an issue with the LinkedIn verification. Please try again.',
          color: 'text-red-700'
        };
      default:
        return {
          title: 'Processing...',
          message: 'Processing your verification request...',
          color: 'text-gray-700'
        };
    }
  };

  const statusInfo = getStatusMessage();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-lg w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="flex justify-center mb-6">
          {getStatusIcon()}
        </div>
        
        <h1 className={`text-2xl font-bold mb-4 ${statusInfo.color}`}>
          {statusInfo.title}
        </h1>
        
        <p className="text-gray-600 mb-6">
          {statusInfo.message}
        </p>

        {/* Waiting Timer */}
        {verificationStatus === 'waiting' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <span className="text-blue-800 font-medium">
                Time elapsed: {formatTime(waitingTime)}
              </span>
            </div>
            <p className="text-sm text-blue-700">
              Take your time to complete the LinkedIn verification process.
            </p>
          </div>
        )}

        {/* Action Buttons */}
        {verificationStatus === 'waiting' && (
          <div className="space-y-3">
            <button
              onClick={handleManualCheck}
              disabled={isPolling}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              {isPolling ? (
                <span className="flex items-center justify-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Checking Account Creation...
                </span>
              ) : (
                '✅ Check Account Creation'
              )}
            </button>
            
            <button
              onClick={handleCancel}
              disabled={isPolling}
              className="w-full bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              ❌ Cancel Verification
            </button>
            
            <div className="text-xs text-gray-500 mt-4">
              <p>⚠️ Only click "Check Account Creation" after you have:</p>
              <ul className="list-disc list-inside text-left mt-2 space-y-1">
                <li>Solved any CAPTCHA challenges</li>
                <li>Entered OTP/2FA codes if requested</li>
                <li>Completed any security verifications</li>
                <li>Successfully logged into LinkedIn</li>
              </ul>
            </div>
          </div>
        )}

        {/* Status Messages for Other Stages */}
        {verificationStatus === 'checking' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-center gap-3">
              <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
              <span className="text-blue-800 font-medium">
                Verifying account creation via webhook...
              </span>
            </div>
          </div>
        )}

        {verificationStatus === 'success' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span className="text-green-800 font-medium">
                Closing window and returning to application...
              </span>
            </div>
          </div>
        )}

        {verificationStatus === 'failed' && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center justify-center gap-3">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <div className="text-left">
                <p className="text-red-800 font-medium">Verification Failed</p>
                <p className="text-red-700 text-sm mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Manual redirect button as fallback */}
        {(verificationStatus === 'success' || verificationStatus === 'failed') && (
          <div className="mt-6">
            <button
              onClick={() => window.location.href = '/login'}
              className="text-blue-600 hover:text-blue-800 underline text-sm"
            >
              Click here if you're not redirected automatically
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default VerificationWaiting;
