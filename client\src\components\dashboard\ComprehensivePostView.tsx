import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Heart,
  MessageCircle,
  Eye,
  TrendingUp,
  User,
  Building2,
  Calendar,
  Loader2,
  ExternalLink,
  ThumbsUp,
  Lightbulb,
  Gift,
  HandHeart,
  Smile,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { analyticsApi } from '@/services/analyticsApi';
import { toast } from 'sonner';

interface ComprehensivePostViewProps {
  postId: string;
  accountId: string;
  isOpen: boolean;
  onClose: () => void;
  initialPostData?: any;
}

interface ReactionIconProps {
  type: string;
  className?: string;
}

const ReactionIcon: React.FC<ReactionIconProps> = ({ type, className = "w-4 h-4" }) => {
  switch (type.toUpperCase()) {
    case 'LIKE':
      return <ThumbsUp className={className} />;
    case 'LOVE':
      return <Heart className={className} />;
    case 'INSIGHTFUL':
      return <Lightbulb className={className} />;
    case 'CELEBRATE':
      return <Gift className={className} />;
    case 'SUPPORT':
      return <HandHeart className={className} />;
    case 'FUNNY':
      return <Smile className={className} />;
    case 'EMPATHY':
      return <Heart className={`${className} text-pink-600`} />;
    case 'PRAISE':
      return <HandHeart className={`${className} text-indigo-600`} />;
    default:
      return <ThumbsUp className={className} />;
  }
};

const ComprehensivePostView: React.FC<ComprehensivePostViewProps> = ({
  postId,
  accountId,
  isOpen,
  onClose,
  initialPostData
}) => {
  const [postData, setPostData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fetchStartTime, setFetchStartTime] = useState<number | null>(null);
  const [expandedReactions, setExpandedReactions] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    if (isOpen && postId && accountId) {
      fetchComprehensivePostData();
      // Reset expanded reactions when modal opens
      setExpandedReactions({});
    }
  }, [isOpen, postId, accountId]);

  const fetchComprehensivePostData = async () => {
    setIsLoading(true);
    setError(null);
    setFetchStartTime(Date.now());
    
    console.log(`🎬 [${new Date().toISOString()}] ===== UI: COMPREHENSIVE POST VIEW STARTED =====`);
    console.log(`📊 Post ID: ${postId}`);
    console.log(`🔗 Account ID: ${accountId}`);
    
    try {
      const data = await analyticsApi.getComprehensivePostData(postId, accountId);
      setPostData(data);
      
      const duration = fetchStartTime ? Date.now() - fetchStartTime : 0;
      console.log(`✅ [${new Date().toISOString()}] UI: Post data loaded successfully (${duration}ms)`);
      
      toast.success(`Post details loaded successfully! (${duration}ms)`, {
        description: `${data.metadata?.total_comments || 0} comments, ${data.metadata?.total_reactions || 0} reactions`
      });
      
    } catch (err: any) {
      const duration = fetchStartTime ? Date.now() - fetchStartTime : 0;
      console.error(`❌ [${new Date().toISOString()}] UI: Failed to load post data (${duration}ms):`, err);
      setError(err.message || 'Failed to load post data');
      toast.error('Failed to load post details', {
        description: err.message || 'Please try again later'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatEngagementRate = (impressions: number, reactions: number, comments: number) => {
    if (!impressions || impressions === 0) return '0%';
    const engagementRate = ((reactions + comments) / impressions) * 100;
    return `${engagementRate.toFixed(2)}%`;
  };

  const toggleReactionExpansion = (reactionType: string) => {
    setExpandedReactions(prev => ({
      ...prev,
      [reactionType]: !prev[reactionType]
    }));
  };

  const getReactionColor = (type: string) => {
    switch (type.toUpperCase()) {
      case 'LIKE':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'LOVE':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'INSIGHTFUL':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'CELEBRATE':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'SUPPORT':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'FUNNY':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'EMPATHY':
        return 'text-pink-600 bg-pink-50 border-pink-200';
      case 'PRAISE':
        return 'text-indigo-600 bg-indigo-50 border-indigo-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] flex flex-col overflow-hidden">
        {/* Header - Fixed */}
        <div className="flex items-center justify-between p-6 border-b bg-white flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Eye className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">Comprehensive Post Analysis</h2>
              <p className="text-sm text-gray-500">
                {isLoading ? 'Loading detailed post data...' : 'Complete post insights with comments and reactions'}
              </p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content - Scrollable */}
        <div className="flex-1 overflow-y-auto" style={{ scrollbarWidth: 'thin' }}>
          <div className="p-6 pb-8">
          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
                <p className="text-gray-600">Fetching comprehensive post data...</p>
                <p className="text-sm text-gray-400 mt-2">
                  Loading post details, comments, and reactions
                </p>
                {fetchStartTime && (
                  <p className="text-xs text-gray-400 mt-1">
                    {Math.round((Date.now() - fetchStartTime) / 1000)}s elapsed
                  </p>
                )}
              </div>
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">
                <MessageCircle className="w-12 h-12 mx-auto mb-2" />
                <p className="font-medium">Failed to load post data</p>
                <p className="text-sm text-gray-500 mt-1">{error}</p>
              </div>
              <Button onClick={fetchComprehensivePostData} variant="outline">
                Try Again
              </Button>
            </div>
          )}

          {postData && !isLoading && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-0">
              {/* Main Post Content */}
              <div className="lg:col-span-2 space-y-6 overflow-y-auto">
                {/* Post Details */}
                <Card>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-gray-100 rounded-full">
                          {postData.post?.author?.is_company ? (
                            <Building2 className="w-5 h-5 text-gray-600" />
                          ) : (
                            <User className="w-5 h-5 text-gray-600" />
                          )}
                        </div>
                        <div>
                          <h3 className="font-semibold">{postData.post?.author?.name || 'N/A'}</h3>
                          <p className="text-sm text-gray-500">
                            {postData.post?.author?.is_company ? 'Company Page' : 'Individual Profile'}
                          </p>
                          {postData.post?.author?.headline && (
                            <p className="text-xs text-gray-400 mt-1">{postData.post.author.headline}</p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Calendar className="w-4 h-4" />
                          {postData.post?.formatted_date?.readable || 'N/A'}
                        </div>
                        <p className="text-xs text-gray-400 mt-1">
                          {postData.post?.formatted_date?.relative || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Post Text */}
                      <div className="prose max-w-none">
                        <p className="text-gray-800 whitespace-pre-wrap">
                          {postData.post?.text || 'N/A'}
                        </p>
                      </div>

                      {/* Post Media */}
                      {postData.post?.media && postData.post.media.length > 0 && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-700">Media:</p>
                          <div className="grid grid-cols-2 gap-2">
                            {postData.post.media.map((media: any, index: number) => (
                              <div key={index} className="bg-gray-100 rounded-lg p-3 text-center">
                                <p className="text-sm text-gray-600">{media.type || 'Media'}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Engagement Metrics */}
                      <div className="grid grid-cols-4 gap-4 pt-4 border-t">
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 text-blue-600 mb-1">
                            <Eye className="w-4 h-4" />
                          </div>
                          <p className="text-lg font-semibold">{postData.post?.impressions_counter?.toLocaleString() || '0'}</p>
                          <p className="text-xs text-gray-500">Impressions</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 text-red-600 mb-1">
                            <Heart className="w-4 h-4" />
                          </div>
                          <p className="text-lg font-semibold">{postData.reactions?.paging?.total_count || postData.reactions?.items?.length || postData.metadata?.total_reactions || 0}</p>
                          <p className="text-xs text-gray-500">Reactions</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 text-green-600 mb-1">
                            <MessageCircle className="w-4 h-4" />
                          </div>
                          <p className="text-lg font-semibold">{postData.comments?.total_items || postData.comments?.paging?.total_count || postData.metadata?.total_comments || 0}</p>
                          <p className="text-xs text-gray-500">Comments</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 text-purple-600 mb-1">
                            <TrendingUp className="w-4 h-4" />
                          </div>
                          <p className="text-lg font-semibold">
                            {formatEngagementRate(
                              postData.post?.impressions_counter || 0,
                              postData.reactions?.paging?.total_count || postData.reactions?.items?.length || postData.metadata?.total_reactions || 0,
                              postData.comments?.total_items || postData.comments?.paging?.total_count || postData.metadata?.total_comments || 0
                            )}
                          </p>
                          <p className="text-xs text-gray-500">Engagement</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar - Reactions & Comments */}
              <div className="space-y-6 max-h-full overflow-y-auto" style={{ scrollbarWidth: 'thin' }}>
                {/* Reactions Breakdown */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Heart className="w-5 h-5 text-red-600" />
                      Reactions ({postData.reactions?.paging?.total_count || postData.reactions?.items?.length || postData.metadata?.total_reactions || 0})
                    </CardTitle>
                    {postData.reactions?.paging && (
                      <p className="text-sm text-gray-500">
                        Showing {postData.reactions.items?.length || 0} of {postData.reactions.paging.total_count || 0} reactions
                      </p>
                    )}
                  </CardHeader>
                  <CardContent className="max-h-80 overflow-y-auto" style={{ scrollbarWidth: 'thin' }}>
                    {postData.reactions?.items && postData.reactions.items.length > 0 ? (
                      <div className="space-y-3">
                        {/* Group reactions by type */}
                        {(() => {
                          const reactionGroups: { [key: string]: any[] } = {};
                          postData.reactions.items.forEach((reaction: any) => {
                            const type = reaction.value || 'LIKE';
                            if (!reactionGroups[type]) {
                              reactionGroups[type] = [];
                            }
                            reactionGroups[type].push(reaction);
                          });

                          return Object.entries(reactionGroups).map(([type, reactions]) => (
                            <div key={type} className="space-y-2">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <ReactionIcon type={type} />
                                  <span className="font-medium capitalize">{type.toLowerCase()}</span>
                                </div>
                                <Badge variant="outline" className={getReactionColor(type)}>
                                  {reactions.length}
                                </Badge>
                              </div>
                              <div className="ml-6 space-y-1">
                                {/* Show initial reactions (first 3) or all if expanded */}
                                {(expandedReactions[type] ? reactions : reactions.slice(0, 3)).map((reaction: any, index: number) => (
                                  <div key={reaction.author?.id || index} className="flex items-center gap-2 text-sm text-gray-600">
                                    {reaction.author?.type === 'COMPANY' ? (
                                      <Building2 className="w-3 h-3" />
                                    ) : (
                                      <User className="w-3 h-3" />
                                    )}
                                    <span>{reaction.author?.name || 'N/A'}</span>
                                    {reaction.author?.network_distance && (
                                      <Badge variant="outline" className="text-xs">
                                        {reaction.author.network_distance === 'FIRST_DEGREE' ? '1°' :
                                         reaction.author.network_distance === 'SECOND_DEGREE' ? '2°' :
                                         reaction.author.network_distance === 'THIRD_DEGREE' ? '3°' :
                                         reaction.author.network_distance === 'OUT_OF_NETWORK' ? 'Out of network' :
                                         reaction.author.network_distance}
                                      </Badge>
                                    )}
                                    {reaction.author?.profile_url && (
                                      <a
                                        href={reaction.author.profile_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-xs text-blue-600 hover:text-blue-800"
                                      >
                                        <ExternalLink className="w-3 h-3" />
                                      </a>
                                    )}
                                  </div>
                                ))}

                                {/* Show expand/collapse button if there are more than 3 reactions */}
                                {reactions.length > 3 && (
                                  <button
                                    onClick={() => toggleReactionExpansion(type)}
                                    className="text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50 ml-5 flex items-center gap-1 mt-2 px-2 py-1 rounded transition-colors"
                                  >
                                    {expandedReactions[type] ? (
                                      <>
                                        Show less
                                        <ChevronUp className="w-3 h-3" />
                                      </>
                                    ) : (
                                      <>
                                        +{reactions.length - 3} more
                                        <ChevronDown className="w-3 h-3" />
                                      </>
                                    )}
                                  </button>
                                )}
                              </div>
                            </div>
                          ));
                        })()}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-center py-4">No reactions yet</p>
                    )}
                  </CardContent>
                </Card>

                {/* Comments */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <MessageCircle className="w-5 h-5 text-green-600" />
                      Comments ({postData.comments?.total_items || postData.comments?.paging?.total_count || postData.metadata?.total_comments || 0})
                    </CardTitle>
                    {postData.comments?.paging && (
                      <p className="text-sm text-gray-500">
                        Showing {postData.comments.items?.length || 0} of {postData.comments.paging.total_count || 0} comments
                      </p>
                    )}
                  </CardHeader>
                  <CardContent className="max-h-96 overflow-y-auto" style={{ scrollbarWidth: 'thin' }}>
                    {postData.comments?.items && postData.comments.items.length > 0 ? (
                      <ScrollArea className="h-96">
                        <div className="space-y-4">
                          {postData.comments.items.map((comment: any, index: number) => (
                            <div key={comment.id || index} className="border-b pb-3 last:border-b-0">
                              <div className="flex items-start gap-3">
                                <div className="p-1 bg-gray-100 rounded-full">
                                  {comment.author_details?.is_company ? (
                                    <Building2 className="w-4 h-4 text-gray-600" />
                                  ) : (
                                    <User className="w-4 h-4 text-gray-600" />
                                  )}
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="font-medium text-sm">
                                      {comment.author || comment.author_details?.name || 'N/A'}
                                    </span>
                                    <Badge variant="outline" className="text-xs">
                                      {comment.author_details?.is_company ? 'Company' : 'Individual'}
                                    </Badge>
                                    {comment.author_details?.network_distance && (
                                      <Badge variant="outline" className="text-xs">
                                        {comment.author_details.network_distance.replace('DISTANCE_', '')}° connection
                                      </Badge>
                                    )}
                                  </div>

                                  {/* Author headline */}
                                  {comment.author_details?.headline && (
                                    <p className="text-xs text-gray-500 mb-1">
                                      {comment.author_details.headline}
                                    </p>
                                  )}

                                  {/* Comment text */}
                                  <p className="text-sm text-gray-800 mb-2">
                                    {comment.text || 'N/A'}
                                  </p>

                                  {/* Comment metadata */}
                                  <div className="flex items-center gap-4 text-xs text-gray-500">
                                    <span className="flex items-center gap-1">
                                      <Calendar className="w-3 h-3" />
                                      {comment.date ? new Date(comment.date).toLocaleDateString('en-US', {
                                        year: 'numeric',
                                        month: 'short',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      }) : 'N/A'}
                                    </span>

                                    {/* Reaction counter */}
                                    <span className="flex items-center gap-1">
                                      <Heart className="w-3 h-3" />
                                      {comment.reaction_counter || 0}
                                    </span>

                                    {/* Reply counter */}
                                    <span className="flex items-center gap-1">
                                      <MessageCircle className="w-3 h-3" />
                                      {comment.reply_counter || 0} replies
                                    </span>
                                  </div>

                                  {/* Profile link */}
                                  {comment.author_details?.profile_url && (
                                    <div className="mt-2">
                                      <a
                                        href={comment.author_details.profile_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                                      >
                                        <ExternalLink className="w-3 h-3" />
                                        View Profile
                                      </a>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    ) : (
                      <p className="text-gray-500 text-center py-4">No comments yet</p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
          </div>
        </div>

        {/* Footer - Fixed */}
        {postData && (
          <div className="border-t p-4 bg-gray-50 flex-shrink-0">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center gap-4">
                <span>Fetch Duration: {postData.metadata?.fetch_duration_ms || 0}ms</span>
                <span>•</span>
                <span>Fetched: {postData.metadata?.fetch_timestamp ? new Date(postData.metadata.fetch_timestamp).toLocaleTimeString() : 'N/A'}</span>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={fetchComprehensivePostData}>
                  Refresh Data
                </Button>
                {postData.post?.social_id && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={`https://linkedin.com/feed/update/${postData.post.social_id}`} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="w-4 h-4 mr-1" />
                      View on LinkedIn
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComprehensivePostView;
