
import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import UserManagementCard from './UserManagementCard';
import ApprovalWorkflowsCard from './ApprovalWorkflowsCard';
import { User, Invitation, BrandColors } from '@/types/settings';

const UsersTab = () => {
  const { isAdmin, profile } = useAuth();
  const { toast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [resendingId, setResendingId] = useState<string | null>(null);
  const [adminApprovalsEnabled, setAdminApprovalsEnabled] = useState(true);

  useEffect(() => {
    if (profile?.id) {
      fetchUsers();
      fetchInvitations();
    }
  }, [profile?.id]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      
      // Get current user's company
      const { data: companyData, error: companyError } = await supabase
        .from('company_members')
        .select('company_id')
        .eq('user_id', profile?.id)
        .single();
        
      if (companyError) {
        console.error('Error fetching company:', companyError);
        return;
      }
      
      const companyId = companyData?.company_id;
      
      if (!companyId) {
        console.error('No company ID found');
        return;
      }
      
      // Get all users from this company
      const { data: companyMembers, error: membersError } = await supabase
        .from('company_members')
        .select(`
          user_id,
          role,
          department,
          profiles:user_id (
            first_name,
            last_name,
            email
          )
        `)
        .eq('company_id', companyId);
        
      if (membersError) {
        console.error('Error fetching company members:', membersError);
        return;
      }
      
      const formattedUsers = companyMembers.map(member => ({
        id: member.user_id,
        name: `${member.profiles?.first_name || ''} ${member.profiles?.last_name || ''}`.trim() || 'Unknown User',
        email: member.profiles?.email || '<EMAIL>',
        role: member.role,
        department: member.department || 'Not specified',
        status: 'Active'
      }));
      
      setUsers(formattedUsers);
    } catch (error) {
      console.error('Error in fetchUsers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchInvitations = async () => {
    try {
      setIsLoading(true);
      
      // Get current user's company
      const { data: companyData, error: companyError } = await supabase
        .from('company_members')
        .select('company_id')
        .eq('user_id', profile?.id)
        .single();
        
      if (companyError) {
        console.error('Error fetching company for invitations:', companyError);
        return;
      }
      
      const companyId = companyData?.company_id;
      
      if (!companyId) {
        console.error('No company ID found for invitations');
        return;
      }
      
      // Get pending invitations
      const { data: invitationsData, error: invitationsError } = await supabase
        .from('invitations')
        .select('*')
        .eq('company_id', companyId)
        .order('created_at', { ascending: false });
        
      if (invitationsError) {
        console.error('Error fetching invitations:', invitationsError);
        return;
      }
      
      const formattedInvitations = invitationsData.map(invite => ({
        id: invite.id,
        email: invite.email,
        role: invite.role,
        department: invite.department || 'Not specified',
        status: invite.status,
        created_at: invite.created_at
      }));
      
      setInvitations(formattedInvitations);
    } catch (error) {
      console.error('Error in fetchInvitations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resendInvitation = async (invitationId: string) => {
    try {
      setResendingId(invitationId);
      
      // Get invitation details
      const { data: invitation, error: inviteError } = await supabase
        .from('invitations')
        .select('email, company_id, token')
        .eq('id', invitationId)
        .single();
        
      if (inviteError || !invitation) {
        console.error('Error fetching invitation:', inviteError);
        toast({
          title: "Error",
          description: "Could not find invitation details.",
          variant: "destructive"
        });
        return;
      }
      
      // Get company details
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('name')
        .eq('id', invitation.company_id)
        .single();
        
      if (companyError || !company) {
        console.error('Error fetching company for resend:', companyError);
        toast({
          title: "Error",
          description: "Could not find company details.",
          variant: "destructive"
        });
        return;
      }
      
      // Call the edge function to resend the email
      const { data, error } = await supabase.functions.invoke('send-invitation', {
        body: { 
          invitations: [{
            email: invitation.email,
            role: 'user' // Default if not specified
          }],
          companyId: invitation.company_id,
          companyName: company.name,
          invitedBy: profile?.id,
          resendToken: invitation.token
        }
      });
      
      if (error) {
        console.error('Error resending invitation:', error);
        toast({
          title: "Error",
          description: `Failed to resend invitation: ${error.message}`,
          variant: "destructive"
        });
        return;
      }
      
      const result = data.results?.[0];
      
      if (result?.success) {
        toast({
          title: "Invitation resent",
          description: `Successfully resent invitation to ${invitation.email}.`
        });
        
        // Update invitations list
        setInvitations(invites => invites.map(inv => 
          inv.id === invitationId ? { ...inv, status: 'pending' } : inv
        ));
      } else {
        toast({
          title: "Failed to resend",
          description: result?.error || "Unknown error occurred.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Exception resending invitation:', error);
      toast({
        title: "Error",
        description: `Exception: ${(error as Error).message}`,
        variant: "destructive"
      });
    } finally {
      setResendingId(null);
    }
  };

  // Update approval settings
  const updateApprovalSettings = async (enabled: boolean) => {
    setAdminApprovalsEnabled(enabled);
    
    try {
      // Get current user's company
      const { data: companyData, error: companyError } = await supabase
        .from('company_members')
        .select('company_id')
        .eq('user_id', profile?.id)
        .single();
        
      if (companyError) {
        console.error('Error fetching company:', companyError);
        toast({
          title: "Error",
          description: "Could not update approval settings.",
          variant: "destructive"
        });
        return;
      }
      
      // Get existing brand colors if any
      const { data: existingData } = await supabase
        .from('company_branding')
        .select('brand_colors')
        .eq('company_id', profile?.id)
        .single();
      
      // Prepare the brand colors object with existing data
      const brandColors: BrandColors = {
        ...(typeof existingData?.brand_colors === 'object' ? existingData?.brand_colors as object : {}),
        adminApprovalsEnabled: enabled
      };
      
      // Update or insert company branding settings
      const { error: updateError } = await supabase
        .from('company_branding')
        .upsert({
          company_id: profile?.id || '',  // Use profile ID as we need a non-null value
          company_ref_id: companyData?.company_id,  // Reference to actual company
          brand_colors: brandColors
        }, {
          onConflict: 'company_id'
        });
        
      if (updateError) {
        console.error('Error updating approval settings:', updateError);
        toast({
          title: "Error",
          description: "Failed to save approval settings.",
          variant: "destructive"
        });
        return;
      }
      
      toast({
        title: enabled ? "Approvals Enabled" : "Approvals Disabled",
        description: enabled 
          ? "All content now requires admin approval before publishing." 
          : "Content can now be published without admin approval."
      });
    } catch (error) {
      console.error('Error in updating approval settings:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred.",
        variant: "destructive"
      });
    }
  };

  // Fetch approval settings
  useEffect(() => {
    const fetchApprovalSettings = async () => {
      if (profile?.id) {
        try {
          const { data, error } = await supabase
            .from('company_branding')
            .select('brand_colors')
            .eq('company_id', profile.id)
            .single();
          
          if (error) {
            console.error('Error fetching approval settings:', error);
            return;
          }
          
          // Safely access the adminApprovalsEnabled property
          if (data?.brand_colors && 
              typeof data.brand_colors === 'object' &&
              'adminApprovalsEnabled' in (data.brand_colors as object)) {
            setAdminApprovalsEnabled((data.brand_colors as BrandColors).adminApprovalsEnabled || false);
          }
        } catch (error) {
          console.error('Error in fetchApprovalSettings:', error);
        }
      }
    };

    fetchApprovalSettings();
  }, [profile?.id]);

  return (
    <div className="space-y-6">
      <UserManagementCard 
        users={users}
        invitations={invitations}
        isLoading={isLoading}
        resendingId={resendingId}
        resendInvitation={resendInvitation}
      />
      
      <ApprovalWorkflowsCard 
        adminApprovalsEnabled={adminApprovalsEnabled}
        updateApprovalSettings={updateApprovalSettings}
      />
    </div>
  );
};

export default UsersTab;
