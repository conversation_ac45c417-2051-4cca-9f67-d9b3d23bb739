import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import PageBackground from '@/components/layout/PageBackground';

import { supabase } from '@/integrations/supabase/client';
import { analyticsService } from '@/services/analytics';
import { getApiUrl } from '@/config/env';

/**
 * CLEAN LINKEDIN CONNECTION CALLBACK
 * 
 * Flow:
 * 1. User completes Unipile hosted auth
 * 2. Unipile redirects here with success=true&userId=email
 * 3. Webhook runs in background (creates account)
 * 4. Frontend waits for webhook completion
 * 5. Fetch account data from Unipile
 * 6. Save to Supabase profiles table
 * 7. Show success and redirect to dashboard
 */
const UnipileAuthCallbackClean: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing your LinkedIn connection...');
  const [accountId, setAccountId] = useState<string | null>(null);

  /**
   * STEP 4: Wait for webhook completion and get account_id
   */
  const waitForWebhookCompletion = async (userEmail: string): Promise<string | null> => {
    console.log('⏳ STEP 4: Waiting for webhook completion for user:', userEmail);
    
    let attempts = 0;
    const maxAttempts = 15; // 45 seconds total (3 second intervals)

    return new Promise((resolve) => {
      const checkWebhook = async () => {
        attempts++;
        console.log(`🔄 Webhook check attempt ${attempts}/${maxAttempts}`);

        try {
          // Check if webhook has processed and created account
          const response = await fetch(getApiUrl('/auth/linkedin/latest'), {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
          });

          if (response.ok) {
            const data = await response.json();
            console.log('🔍 Backend response:', data);

            // The backend returns the account object with 'id' field, not 'account_id'
            const accountId = data.data?.id || data.data?.account_id || data.account_id || data.id;

            if (data.success && accountId) {
              console.log('✅ STEP 4 Complete: Webhook processed, account_id found:', accountId);
              resolve(accountId);
              return;
            }
          }

          if (attempts >= maxAttempts) {
            console.log('⏰ Webhook timeout reached');
            resolve(null);
            return;
          }

          // Wait 3 seconds before next attempt
          setTimeout(checkWebhook, 3000);

        } catch (error) {
          console.error('❌ Error checking webhook:', error);
          if (attempts >= maxAttempts) {
            resolve(null);
            return;
          }
          setTimeout(checkWebhook, 3000);
        }
      };

      checkWebhook();
    });
  };

  /**
   * STEP 5: Fetch LinkedIn profile data from Unipile
   */
  const fetchLinkedInProfile = async (accountId: string): Promise<any> => {
    console.log('📊 STEP 5: Fetching LinkedIn profile data for account:', accountId);

    try {
      const response = await fetch(getApiUrl(`/auth/get-linkedin-profile/${accountId}`), {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch profile: ${response.status}`);
      }

      const data = await response.json();
      console.log('🔍 Profile response from backend:', data);

      // Backend returns profile data in 'data' field, not 'profile' field
      const profileData = data.data || data.profile;

      if (data.success && profileData) {
        console.log('✅ STEP 5 Complete: LinkedIn profile fetched:', profileData.name || profileData.id);
        return profileData;
      } else {
        throw new Error(`Invalid profile response: ${JSON.stringify(data)}`);
      }

    } catch (error) {
      console.error('❌ Error fetching LinkedIn profile:', error);
      throw error;
    }
  };

  /**
   * STEP 6: Save LinkedIn data to Supabase profiles table AND update user metadata
   */
  const saveToSupabase = async (userEmail: string, accountId: string, profileData: any): Promise<boolean> => {
    console.log('💾 STEP 6: Saving LinkedIn data to Supabase for user:', userEmail);

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      const now = new Date().toISOString();

      // Extract first and last name from LinkedIn profile
      const extractNamesFromProfile = (profileData: any) => {
        let firstName = '';
        let lastName = '';

        if (profileData.name) {
          // Split full name into first and last name
          const nameParts = profileData.name.trim().split(' ');
          firstName = nameParts[0] || '';
          lastName = nameParts.slice(1).join(' ') || '';
        }

        // Also check if names are available in connection_params
        if (profileData.connection_params?.im?.firstName) {
          firstName = profileData.connection_params.im.firstName;
        }
        if (profileData.connection_params?.im?.lastName) {
          lastName = profileData.connection_params.im.lastName;
        }

        console.log('📝 Extracted names from LinkedIn profile:', {
          fullName: profileData.name,
          firstName,
          lastName
        });

        return { firstName, lastName };
      };

      const { firstName, lastName } = extractNamesFromProfile(profileData);

      // STEP 6A: Check if this is a reconnection or first-time connection
      console.log('🔍 STEP 6A: Checking for existing LinkedIn data...');
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('account_id, linkedin_email, linkedin_user_id, linkedin_connected_at, linkedin_reconnection_count')
        .eq('id', user.id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = no rows found
        console.error('❌ Error fetching existing profile:', fetchError);
        throw new Error('Failed to check existing LinkedIn data');
      }

      const isReconnection = existingProfile?.account_id && existingProfile?.linkedin_email;

      console.log('📊 Connection type analysis:', {
        isReconnection,
        existingAccountId: existingProfile?.account_id,
        existingEmail: existingProfile?.linkedin_email,
        newAccountId: accountId,
        newEmail: userEmail
      });

      if (isReconnection) {
        // STEP 6B: Validate reconnection data
        console.log('🔄 STEP 6B: Validating reconnection data...');

        const emailMatches = existingProfile.linkedin_email === userEmail;
        const accountIdMatches = existingProfile.account_id === accountId;

        if (!emailMatches || !accountIdMatches) {
          const errorMsg = `Reconnection validation failed. Please use your original LinkedIn account (${existingProfile.linkedin_email}) to reconnect.`;
          console.error('❌ Reconnection validation failed:', {
            emailMatches,
            accountIdMatches,
            existingEmail: existingProfile.linkedin_email,
            existingAccountId: existingProfile.account_id,
            newEmail: userEmail,
            newAccountId: accountId
          });
          throw new Error(errorMsg);
        }

        console.log('✅ Reconnection validation passed - updating non-unique fields only');

        // Extract LinkedIn user ID from profile data
        const linkedinUserId = profileData.connection_params?.im?.id ||
                              profileData.linkedinUserId ||
                              profileData.id;

        console.log('🔄 Reconnection - Extracting LinkedIn IDs:', {
          accountId: accountId, // Unipile account ID
          linkedinUserId: linkedinUserId, // LinkedIn internal user ID
        });

        // STEP 6C: Update only non-unique fields for reconnection
        const reconnectionData = {
          first_name: firstName || undefined, // Update name if available
          last_name: lastName || undefined,   // Update name if available
          linkedin_user_id: linkedinUserId, // ✅ Update LinkedIn internal user ID
          linkedin_url: profileData.publicIdentifier
            ? `https://linkedin.com/in/${profileData.publicIdentifier}`
            : null,
          linkedin_connection_status: 'connected',
          linkedin_last_sync: now,
          linkedin_last_reconnection: now,
          linkedin_reconnection_count: (existingProfile.linkedin_reconnection_count || 0) + 1,
          linkedin_profile_data: profileData,
          updated_at: now
          // Note: NOT updating account_id, linkedin_email to avoid unique constraint violations
        };

        const { error: reconnectError } = await supabase
          .from('profiles')
          .update(reconnectionData)
          .eq('id', user.id);

        if (reconnectError) {
          console.error('❌ Reconnection update error:', reconnectError);
          throw reconnectError;
        }

        console.log('✅ STEP 6C Complete: Reconnection data updated successfully');

      } else {
        // STEP 6D: First-time connection - save all data including unique fields
        console.log('🆕 STEP 6D: First-time connection - saving all LinkedIn data...');

        // Extract LinkedIn user ID from profile data
        const linkedinUserId = profileData.connection_params?.im?.id ||
                              profileData.linkedinUserId ||
                              profileData.id;

        console.log('🔍 Extracting LinkedIn IDs:', {
          accountId: accountId, // Unipile account ID
          linkedinUserId: linkedinUserId, // LinkedIn internal user ID
        });

        const firstTimeData = {
          first_name: firstName || undefined, // Save name from LinkedIn
          last_name: lastName || undefined,   // Save name from LinkedIn
          account_id: accountId, // ✅ Unipile account ID (existing field)
          linkedin_email: userEmail,
          linkedin_user_id: linkedinUserId, // ✅ LinkedIn internal user ID (correct field)
          linkedin_url: profileData.publicIdentifier
            ? `https://linkedin.com/in/${profileData.publicIdentifier}`
            : null,
          linkedin_connection_status: 'connected',
          linkedin_connected_at: now,
          linkedin_last_sync: now,
          linkedin_reconnection_count: 0,
          linkedin_profile_data: profileData,
          updated_at: now
        };

        const { error: firstTimeError } = await supabase
          .from('profiles')
          .update(firstTimeData)
          .eq('id', user.id);

        if (firstTimeError) {
          console.error('❌ First-time connection error:', firstTimeError);

          // Handle unique constraint violations for first-time connections
          if (firstTimeError.code === '23505') {
            let errorMessage = 'This LinkedIn account is already connected to another user.';

            if (firstTimeError.message.includes('account_id')) {
              errorMessage = 'This LinkedIn account is already connected to another user. Please use a different LinkedIn account.';
            } else if (firstTimeError.message.includes('linkedin_email')) {
              errorMessage = 'This LinkedIn email is already connected to another user. Please use a different LinkedIn account.';
            } else if (firstTimeError.message.includes('linkedin_user_id')) {
              errorMessage = 'This LinkedIn user ID is already connected to another user. Please use a different LinkedIn account.';
            }

            throw new Error(errorMessage);
          }

          throw firstTimeError;
        }

        console.log('✅ STEP 6D Complete: First-time connection data saved successfully');
      }

      console.log('✅ STEP 6A Complete: Profiles table updated');

      // STEP 6B: Update user metadata (THIS IS THE KEY FIX!)
      console.log('💾 STEP 6B: Updating user metadata...');

      // Extract LinkedIn user ID for metadata
      const linkedinUserId = profileData.connection_params?.im?.id ||
                            profileData.linkedinUserId ||
                            profileData.id;

      console.log('💾 Updating user metadata with both LinkedIn IDs:', {
        linkedinAccountId: accountId,
        linkedinUserId: linkedinUserId
      });

      const { error: metadataError } = await supabase.auth.updateUser({
        data: {
          linkedinAccountId: accountId, // Unipile account ID
          linkedin_account_id: accountId, // Unipile account ID (compatibility)
          linkedinUserId: linkedinUserId, // LinkedIn internal user ID
          linkedin_user_id: linkedinUserId, // LinkedIn internal user ID (compatibility)
          linkedinEmail: userEmail,
          linkedin_email: userEmail,
          linkedinUrl: profileData.publicIdentifier
            ? `https://linkedin.com/in/${profileData.publicIdentifier}`
            : null,
          linkedin_url: profileData.publicIdentifier
            ? `https://linkedin.com/in/${profileData.publicIdentifier}`
            : null,
          linkedin_connected: true,
          linkedin_connected_at: now,
          linkedin_profile_name: profileData.name
        }
      });

      if (metadataError) {
        console.error('⚠️ Warning: Failed to update user metadata:', metadataError);
        // Don't throw error here - profiles table update succeeded
      } else {
        console.log('✅ STEP 6B Complete: User metadata updated');
      }

      console.log('✅ STEP 6 Complete: LinkedIn data saved to both profiles table and user metadata');
      return true;

    } catch (error) {
      console.error('❌ Error saving to Supabase:', error);
      return false;
    }
  };

  /**
   * STEP 7: Complete the flow and redirect to dashboard
   */
  const completeFlow = async (userEmail: string, accountId: string) => {
    console.log('🎉 STEP 7: Completing LinkedIn connection flow');

    // Track successful verification
    await analyticsService.trackLinkedInVerificationSuccess(userEmail, accountId);

    setStatus('success');
    setMessage('LinkedIn account connected successfully!');
    toast.success('LinkedIn account connected and data saved!');

    // Auto-redirect to dashboard after 3 seconds
    setTimeout(() => {
      navigate('/dashboard');
    }, 3000);
  };

  /**
   * Main process: Handle the complete LinkedIn connection flow
   */
  const processLinkedInConnection = async (userEmail: string, providedAccountId?: string, isReconnection?: boolean) => {
    try {
      console.log('🚀 Starting LinkedIn connection process for:', userEmail);
      console.log('📊 Connection details:', { providedAccountId, isReconnection });

      let accountId: string;

      if (isReconnection && providedAccountId) {
        // STEP 4A: For reconnections, use the provided account_id directly
        console.log('🔄 Reconnection detected - using provided account_id:', providedAccountId);
        accountId = providedAccountId;
        setMessage('Processing LinkedIn reconnection...');
      } else {
        // STEP 4B: For first-time connections, wait for webhook completion
        setMessage('Waiting for LinkedIn account creation...');
        accountId = await waitForWebhookCompletion(userEmail);

        if (!accountId) {
          throw new Error('Webhook timeout - account creation may have failed');
        }
      }

      setAccountId(accountId);

      // STEP 5: Fetch LinkedIn profile data
      setMessage('Fetching your LinkedIn profile data...');
      const profileData = await fetchLinkedInProfile(accountId);

      // STEP 6: Save to Supabase
      setMessage('Saving your LinkedIn data...');
      const saved = await saveToSupabase(userEmail, accountId, profileData);

      if (!saved) {
        throw new Error('Failed to save LinkedIn data to database');
      }

      // STEP 7: Complete flow
      await completeFlow(userEmail, accountId);

    } catch (error: any) {
      console.error('❌ LinkedIn connection process failed:', error);
      setStatus('error');
      setMessage(`Connection failed: ${error.message}`);
      toast.error(`LinkedIn connection failed: ${error.message}`);
    }
  };

  // Handle callback URL parameters
  useEffect(() => {
    const success = searchParams.get('success');
    const error = searchParams.get('error');
    const user_id = searchParams.get('userId');
    const account_id = searchParams.get('accountId');
    const type = searchParams.get('type');

    console.log('🔍 Callback received:', { success, error, user_id, account_id, type });

    if (success === 'true' && user_id) {
      // Check if this is a reconnection
      const isReconnection = type === 'reconnect' && account_id;

      if (isReconnection) {
        console.log('🔄 Reconnection callback detected:', { user_id, account_id });
        // For reconnections, use the specific account_id from URL
        processLinkedInConnection(user_id, account_id, true);
      } else {
        console.log('🆕 First-time connection callback detected:', { user_id });
        // For first-time connections, use webhook polling
        processLinkedInConnection(user_id);
      }
    } else if (error) {
      setStatus('error');
      setMessage(`Connection failed: ${decodeURIComponent(error)}`);
      toast.error(`LinkedIn connection failed: ${decodeURIComponent(error)}`);
    } else {
      setStatus('error');
      setMessage('Invalid callback parameters');
    }
  }, [searchParams]);

  const handleRetry = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      <PageBackground />
      
      <div className="text-center p-8 relative z-10 max-w-md bg-white rounded-lg shadow-xl bg-opacity-95 backdrop-blur-sm border border-opacity-20">
        {status === 'loading' && (
          <div className="space-y-4">
            <div className="flex justify-center">
              <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900">Processing Connection</h2>
            <p className="text-gray-600">{message}</p>
          </div>
        )}
        
        {status === 'success' && (
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
            </div>
            <h2 className="text-2xl font-semibold text-green-900">Success!</h2>
            <p className="text-gray-600">{message}</p>
            
            {accountId && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-sm text-green-800">
                  <strong>Account ID:</strong> {accountId}
                </p>
              </div>
            )}
            
            <Button
              onClick={() => navigate('/dashboard')}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              Continue to Dashboard
            </Button>
          </div>
        )}
        
        {status === 'error' && (
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="p-3 bg-red-100 rounded-full">
                <XCircle className="h-12 w-12 text-red-600" />
              </div>
            </div>
            <h2 className="text-2xl font-semibold text-red-900">Connection Failed</h2>
            <p className="text-gray-600">{message}</p>
            
            <Button 
              onClick={handleRetry} 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              Back to Dashboard
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default UnipileAuthCallbackClean;
