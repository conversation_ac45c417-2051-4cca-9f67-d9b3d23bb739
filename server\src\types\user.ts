export interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  linkedin_url?: string;
  linkedin_email?: string;
  account_id?: string; // Unipile account ID (e.g., "X9KKvfn4T_Cot-xPdlZthg")
  linkedin_user_id?: string; // LinkedIn user identifier (e.g., "ACoAADNehFYBFevg3Qj8WjfB1A1gDX0afKOXMXs")
  linkedin_connection_status: 'connected' | 'disconnected' | 'pending' | 'verification_required' | 'not_connected';
  role: 'admin' | 'user';
  company_id?: string;
  created_at: Date;
  updated_at: Date;
}

export interface UserSignupRequest {
  // App credentials
  email: string;
  password: string;

  // LinkedIn credentials (email only - password not stored)
  linkedin_email: string;

  // User info
  first_name: string;
  last_name: string;
  linkedin_url?: string;

  // Options
  use_same_credentials: boolean;
}

export interface CreateUserRequest {
  id?: string; // Optional Supabase Auth user ID
  email: string;
  first_name?: string;
  last_name?: string;
  linkedin_url?: string;
  linkedin_email?: string;
  account_id?: string; // Unipile account ID
  linkedin_user_id?: string; // LinkedIn user identifier
  linkedin_connection_status?: 'connected' | 'disconnected' | 'pending' | 'verification_required' | 'not_connected';
  role?: 'admin' | 'user';
  company_id?: string;
}

export interface UserProfile {
  id: string;
  user_id: string;
  linkedin_profile_data?: any;
  analytics_data?: any;
  last_sync_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface LinkedInAnalytics {
  id: string;
  user_id: string;
  account_id: string;
  profile_views?: number;
  post_impressions?: number;
  connection_count?: number;
  follower_count?: number;
  engagement_rate?: number;
  last_activity_at?: Date;
  sync_status: 'pending' | 'syncing' | 'completed' | 'failed';
  created_at: Date;
  updated_at: Date;
}
