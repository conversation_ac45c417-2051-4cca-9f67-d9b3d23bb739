// Soft Refresh Service - Intelligent background data synchronization
// Implements smart refresh logic based on data age, user activity, and API limits

import { postSyncOrchestrator } from './syncOrchestrator';
import { getUserPosts } from './postStorageService';
import { getUserById } from './user.service';

export interface RefreshConfig {
    // Timing intervals (in minutes)
    minRefreshInterval: number;     // Minimum time between refreshes
    maxDataAge: number;             // Maximum age before forced refresh
    activeUserInterval: number;     // Refresh interval for active users
    inactiveUserInterval: number;   // Refresh interval for inactive users
    
    // API management
    maxConcurrentSyncs: number;     // Max simultaneous sync operations
    rateLimitBuffer: number;        // Buffer time after rate limit hit
    
    // Smart refresh triggers
    newPostThreshold: number;       // Hours to check for new posts
    engagementChangeThreshold: number; // % change to trigger refresh
}

export const DEFAULT_REFRESH_CONFIG: RefreshConfig = {
    minRefreshInterval: 30,         // 30 minutes minimum
    maxDataAge: 240,               // 4 hours maximum age
    activeUserInterval: 60,        // 1 hour for active users
    inactiveUserInterval: 480,     // 8 hours for inactive users
    maxConcurrentSyncs: 3,         // Max 3 concurrent syncs
    rateLimitBuffer: 60,           // 1 hour buffer after rate limit
    newPostThreshold: 2,           // Check for posts newer than 2 hours
    engagementChangeThreshold: 10  // 10% engagement change triggers refresh
};

export class SoftRefreshService {
    private config: RefreshConfig;
    private activeSyncs: Set<string> = new Set();
    private lastRateLimitHit: Date | null = null;
    private refreshQueue: Map<string, Date> = new Map();

    constructor(config: RefreshConfig = DEFAULT_REFRESH_CONFIG) {
        this.config = config;
    }

    /**
     * Determine if a user needs data refresh based on intelligent criteria
     */
    async shouldRefreshUser(userId: string): Promise<{
        shouldRefresh: boolean;
        reason: string;
        priority: 'high' | 'medium' | 'low';
        estimatedApiCalls: number;
    }> {
        try {
            // Check if already syncing
            if (this.activeSyncs.has(userId)) {
                return {
                    shouldRefresh: false,
                    reason: 'Sync already in progress',
                    priority: 'low',
                    estimatedApiCalls: 0
                };
            }

            // Check rate limit buffer
            if (this.isInRateLimitBuffer()) {
                return {
                    shouldRefresh: false,
                    reason: 'Rate limit buffer active',
                    priority: 'low',
                    estimatedApiCalls: 0
                };
            }

            // Get user's current data status
            const dataStatus = await this.getUserDataStatus(userId);
            
            if (!dataStatus.hasData) {
                return {
                    shouldRefresh: true,
                    reason: 'No data exists - initial sync needed',
                    priority: 'high',
                    estimatedApiCalls: 50 // Full sync
                };
            }

            // Check data age
            const dataAgeMinutes = dataStatus.dataAgeMinutes;
            
            if (dataAgeMinutes > this.config.maxDataAge) {
                return {
                    shouldRefresh: true,
                    reason: `Data too old (${dataAgeMinutes} minutes)`,
                    priority: 'high',
                    estimatedApiCalls: 20 // Incremental sync
                };
            }

            // Check user activity level
            const userActivity = await this.getUserActivityLevel(userId);
            const refreshInterval = userActivity === 'active' 
                ? this.config.activeUserInterval 
                : this.config.inactiveUserInterval;

            if (dataAgeMinutes > refreshInterval) {
                return {
                    shouldRefresh: true,
                    reason: `Regular refresh needed for ${userActivity} user`,
                    priority: userActivity === 'active' ? 'medium' : 'low',
                    estimatedApiCalls: 10 // Light refresh
                };
            }

            // Check for potential new posts (business hours, recent activity)
            if (this.isLikelyToHaveNewPosts(dataStatus)) {
                return {
                    shouldRefresh: true,
                    reason: 'Likely to have new posts',
                    priority: 'medium',
                    estimatedApiCalls: 5 // Quick check
                };
            }

            return {
                shouldRefresh: false,
                reason: 'Data is fresh and up-to-date',
                priority: 'low',
                estimatedApiCalls: 0
            };

        } catch (error: any) {
            console.error('❌ Error checking refresh status:', error);
            return {
                shouldRefresh: false,
                reason: `Error: ${error.message}`,
                priority: 'low',
                estimatedApiCalls: 0
            };
        }
    }

    /**
     * Perform intelligent soft refresh for a user
     */
    async performSoftRefresh(userId: string): Promise<{
        success: boolean;
        refreshType: 'full' | 'incremental' | 'light' | 'skipped';
        postsUpdated: number;
        apiCallsUsed: number;
        nextRefreshAt: Date;
        error?: string;
    }> {
        try {
            console.log(`🔄 Starting soft refresh for user: ${userId}`);

            // Check if refresh is needed
            const refreshCheck = await this.shouldRefreshUser(userId);
            
            if (!refreshCheck.shouldRefresh) {
                return {
                    success: true,
                    refreshType: 'skipped',
                    postsUpdated: 0,
                    apiCallsUsed: 0,
                    nextRefreshAt: this.calculateNextRefreshTime(userId, 'skipped')
                };
            }

            // Add to active syncs
            this.activeSyncs.add(userId);

            try {
                // Get user info
                const user = await getUserById(userId);
                if (!user || !user.account_id) {
                    throw new Error('User not found or LinkedIn not connected');
                }

                // Determine refresh type based on priority and data age
                const refreshType = this.determineRefreshType(refreshCheck);
                const limit = this.getRefreshLimit(refreshType);

                console.log(`📊 Performing ${refreshType} refresh for user ${userId} (${refreshCheck.reason})`);

                // Perform the sync
                const syncResult = await postSyncOrchestrator.syncUserPosts({
                    userId,
                    accountId: user.account_id,
                    userIdentifier: user.account_id, // Will be resolved in handler
                    limit,
                    fullSync: refreshType === 'full'
                });

                // Handle rate limit detection
                if (syncResult.apiCalls.rateLimitHit) {
                    this.lastRateLimitHit = new Date();
                    console.log('⚠️ Rate limit hit during soft refresh');
                }

                const nextRefreshAt = this.calculateNextRefreshTime(userId, refreshType);

                console.log(`✅ Soft refresh completed: ${refreshType} (${syncResult.posts.stored + syncResult.posts.updated} posts updated)`);

                return {
                    success: syncResult.success,
                    refreshType: refreshType as any,
                    postsUpdated: syncResult.posts.stored + syncResult.posts.updated,
                    apiCallsUsed: syncResult.apiCalls.made,
                    nextRefreshAt,
                    error: syncResult.errors.length > 0 ? syncResult.errors.join('; ') : undefined
                };

            } finally {
                // Remove from active syncs
                this.activeSyncs.delete(userId);
            }

        } catch (error: any) {
            console.error(`❌ Error in soft refresh for user ${userId}:`, error);
            return {
                success: false,
                refreshType: 'skipped',
                postsUpdated: 0,
                apiCallsUsed: 0,
                nextRefreshAt: this.calculateNextRefreshTime(userId, 'error'),
                error: error.message
            };
        }
    }

    /**
     * Get user's current data status
     */
    private async getUserDataStatus(userId: string): Promise<{
        hasData: boolean;
        dataAgeMinutes: number;
        totalPosts: number;
        lastSyncAt?: Date;
        newPosts: number;
        updatedPosts: number;
    }> {
        const postsResult = await getUserPosts(userId, 100); // Get recent posts for analysis
        
        if (!postsResult.success || !postsResult.posts || postsResult.posts.length === 0) {
            return {
                hasData: false,
                dataAgeMinutes: Infinity,
                totalPosts: 0,
                newPosts: 0,
                updatedPosts: 0
            };
        }

        const posts = postsResult.posts;
        const mostRecentSync = posts.reduce((latest, post) => {
            const syncDate = new Date(post.last_synced_at);
            return syncDate > latest ? syncDate : latest;
        }, new Date(0));

        const dataAgeMinutes = (Date.now() - mostRecentSync.getTime()) / (1000 * 60);
        const newPosts = posts.filter(p => p.is_newly_added).length;
        const updatedPosts = posts.filter(p => p.is_updated).length;

        return {
            hasData: true,
            dataAgeMinutes,
            totalPosts: posts.length,
            lastSyncAt: mostRecentSync,
            newPosts,
            updatedPosts
        };
    }

    /**
     * Determine user activity level based on recent data access patterns
     */
    private async getUserActivityLevel(userId: string): Promise<'active' | 'inactive'> {
        // This could be enhanced with actual user activity tracking
        // For now, use simple heuristics based on data freshness requests
        
        const lastQueueTime = this.refreshQueue.get(userId);
        if (!lastQueueTime) {
            return 'inactive';
        }

        const timeSinceLastRequest = Date.now() - lastQueueTime.getTime();
        const hoursAgo = timeSinceLastRequest / (1000 * 60 * 60);

        return hoursAgo < 2 ? 'active' : 'inactive';
    }

    /**
     * Check if user is likely to have new posts based on timing and patterns
     */
    private isLikelyToHaveNewPosts(dataStatus: any): boolean {
        const now = new Date();
        const isBusinessHours = now.getHours() >= 9 && now.getHours() <= 17;
        const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
        
        // More likely to have new posts during business hours on weekdays
        if (isBusinessHours && isWeekday && dataStatus.dataAgeMinutes > this.config.newPostThreshold * 60) {
            return true;
        }

        return false;
    }

    /**
     * Check if we're in rate limit buffer period
     */
    private isInRateLimitBuffer(): boolean {
        if (!this.lastRateLimitHit) return false;
        
        const bufferEndTime = new Date(this.lastRateLimitHit.getTime() + this.config.rateLimitBuffer * 60 * 1000);
        return new Date() < bufferEndTime;
    }

    /**
     * Determine refresh type based on priority and conditions
     */
    private determineRefreshType(refreshCheck: any): 'full' | 'incremental' | 'light' {
        if (refreshCheck.priority === 'high') return 'full';
        if (refreshCheck.priority === 'medium') return 'incremental';
        return 'light';
    }

    /**
     * Get API call limit based on refresh type
     */
    private getRefreshLimit(refreshType: string): number {
        switch (refreshType) {
            case 'full': return 50;
            case 'incremental': return 20;
            case 'light': return 5;
            default: return 10;
        }
    }

    /**
     * Calculate next refresh time based on refresh type and results
     */
    private calculateNextRefreshTime(userId: string, refreshType: string): Date {
        let intervalMinutes: number;
        
        switch (refreshType) {
            case 'full':
                intervalMinutes = this.config.activeUserInterval;
                break;
            case 'incremental':
                intervalMinutes = this.config.activeUserInterval * 0.75;
                break;
            case 'light':
                intervalMinutes = this.config.minRefreshInterval;
                break;
            case 'error':
                intervalMinutes = this.config.minRefreshInterval * 2; // Back off on errors
                break;
            default:
                intervalMinutes = this.config.inactiveUserInterval;
        }

        return new Date(Date.now() + intervalMinutes * 60 * 1000);
    }

    /**
     * Queue user for refresh (called when user requests data)
     */
    queueUserForRefresh(userId: string): void {
        this.refreshQueue.set(userId, new Date());
    }

    /**
     * Get refresh status for monitoring
     */
    getRefreshStatus(): {
        activeSyncs: number;
        queuedUsers: number;
        lastRateLimitHit?: Date;
        isInRateLimitBuffer: boolean;
    } {
        return {
            activeSyncs: this.activeSyncs.size,
            queuedUsers: this.refreshQueue.size,
            lastRateLimitHit: this.lastRateLimitHit || undefined,
            isInRateLimitBuffer: this.isInRateLimitBuffer()
        };
    }
}

// Export singleton instance
export const softRefreshService = new SoftRefreshService();
