
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, Linkedin, CalendarDays } from 'lucide-react';

interface TopPostProps {
  post: {
    title: string;
    date: string;
    preview: string;
    impressions: number;
    likes: number;
    engagementRate: number;
  };
  onViewDetails?: (postId: string) => void;
  topPostData?: any; // The actual post data with social_id
}

const TopPerformingPost: React.FC<TopPostProps> = ({ post, onViewDetails, topPostData }) => {
  return (
    <Card className="lg:col-span-3">
      <CardHeader className="pb-2">
        <CardTitle className="text-xl flex items-center gap-2">
          <Linkedin className="h-5 w-5 text-[#0A66C2]" />
          Top Performing Post
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="bg-gray-50 p-4 rounded-lg mb-4 border border-gray-100">
          <h3 className="font-bold mb-2">{post.title}</h3>
          <p className="text-sm text-enterprise-gray-600 mb-3">{post.preview}</p>
          <div className="text-xs text-enterprise-gray-500 flex items-center">
            <CalendarDays size={12} className="mr-1" /> {post.date}
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-lg font-bold">{post.impressions.toLocaleString()}</div>
            <div className="text-xs text-enterprise-gray-600">Impressions</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold">{post.likes}</div>
            <div className="text-xs text-enterprise-gray-600">Likes</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold">{post.engagementRate}%</div>
            <div className="text-xs text-enterprise-gray-600">Engagement</div>
          </div>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => {
            if (onViewDetails && topPostData?.id) {
              console.log(`🎯 Top post clicked - Post ID: ${topPostData.id}`);
              console.log(`📝 Note: Will use social_id (${topPostData.social_id}) for comments/reactions internally`);
              onViewDetails(topPostData.id);
            } else if (onViewDetails && topPostData?.social_id) {
              console.log(`🎯 Top post clicked - Social ID: ${topPostData.social_id} (fallback)`);
              onViewDetails(topPostData.social_id);
            } else {
              console.log('⚠️ No post data available for detailed view');
            }
          }}
          disabled={!onViewDetails || (!topPostData?.id && !topPostData?.social_id)}
        >
          <Eye size={14} className="mr-2" />
          {onViewDetails && (topPostData?.id || topPostData?.social_id) ? 'View Comprehensive Details' : 'View Full Post'}
        </Button>
      </CardContent>
    </Card>
  );
};

export default TopPerformingPost;
