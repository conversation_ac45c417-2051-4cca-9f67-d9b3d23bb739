
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Eye, Linkedin, CalendarDays, Paperclip, Image, Video, FileText, ExternalLink } from 'lucide-react';

interface TopPostProps {
  post: {
    title: string;
    date: string;
    preview: string;
    impressions: number;
    likes: number;
    engagementRate: number;
    attachments_links?: string[]; // Array of attachment URLs
  };
  onViewDetails?: (postId: string) => void;
  topPostData?: any; // The actual post data with social_id
}

const TopPerformingPost: React.FC<TopPostProps> = ({ post, onViewDetails, topPostData }) => {

  // Helper function to determine attachment type
  const getAttachmentType = (url: string): 'image' | 'video' | 'document' | 'unknown' => {
    const extension = url.split('.').pop()?.toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const videoExtensions = ['mp4', 'webm', 'ogg', 'avi', 'mov'];
    const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

    if (imageExtensions.includes(extension || '')) return 'image';
    if (videoExtensions.includes(extension || '')) return 'video';
    if (documentExtensions.includes(extension || '')) return 'document';

    // Check URL patterns for LinkedIn media
    if (url.includes('media.licdn.com') && url.includes('image')) return 'image';
    if (url.includes('media.licdn.com') && url.includes('video')) return 'video';

    return 'unknown';
  };

  // Helper function to get attachment icon
  const getAttachmentIcon = (type: string) => {
    switch (type) {
      case 'image': return <Image size={12} className="text-green-600" />;
      case 'video': return <Video size={12} className="text-blue-600" />;
      case 'document': return <FileText size={12} className="text-red-600" />;
      default: return <Paperclip size={12} className="text-gray-600" />;
    }
  };
  return (
    <Card className="lg:col-span-3">
      <CardHeader className="pb-2">
        <CardTitle className="text-xl flex items-center gap-2">
          <Linkedin className="h-5 w-5 text-[#0A66C2]" />
          Top Performing Post
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="bg-gray-50 p-4 rounded-lg mb-4 border border-gray-100">
          <h3 className="font-bold mb-2">{post.title}</h3>
          <p className="text-sm text-enterprise-gray-600 mb-3">{post.preview}</p>
          <div className="text-xs text-enterprise-gray-500 flex items-center">
            <CalendarDays size={12} className="mr-1" /> {post.date}
          </div>

          {/* Attachments Section */}
          {post.attachments_links && post.attachments_links.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="flex items-center gap-2 mb-2">
                <Paperclip size={12} className="text-gray-500" />
                <span className="text-xs text-gray-600 font-medium">
                  Attachments ({post.attachments_links.length})
                </span>
              </div>

              <div className="flex flex-wrap gap-2">
                {post.attachments_links.slice(0, 3).map((url: string, index: number) => {
                  const type = getAttachmentType(url);
                  const fileName = url.split('/').pop() || `attachment-${index + 1}`;

                  return (
                    <div
                      key={index}
                      className="flex items-center gap-1 bg-white px-2 py-1 rounded border text-xs hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => window.open(url, '_blank')}
                      title={`Open ${fileName}`}
                    >
                      {getAttachmentIcon(type)}
                      <span className="text-gray-700 truncate max-w-[80px]">
                        {fileName.length > 12 ? fileName.substring(0, 12) + '...' : fileName}
                      </span>
                      <ExternalLink size={10} className="text-gray-400" />
                    </div>
                  );
                })}

                {post.attachments_links.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{post.attachments_links.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>
        
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-lg font-bold">{post.impressions.toLocaleString()}</div>
            <div className="text-xs text-enterprise-gray-600">Impressions</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold">{post.likes}</div>
            <div className="text-xs text-enterprise-gray-600">Likes</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold">{post.engagementRate}%</div>
            <div className="text-xs text-enterprise-gray-600">Engagement</div>
          </div>
        </div>
        
        <div className='flex items-center justify-center w-full mt-8'>
          <Button
          variant="outline"
          size="sm"
          className="lg:w-1/2"
          onClick={() => {
            if (onViewDetails && topPostData?.id) {
              console.log(`🎯 Top post clicked - Post ID: ${topPostData.id}`);
              console.log(`📝 Note: Will use social_id (${topPostData.social_id}) for comments/reactions internally`);
              onViewDetails(topPostData.id);
            } else if (onViewDetails && topPostData?.social_id) {
              console.log(`🎯 Top post clicked - Social ID: ${topPostData.social_id} (fallback)`);
              onViewDetails(topPostData.social_id);
            } else {
              console.log('⚠️ No post data available for detailed view');
            }
          }}
          disabled={!onViewDetails || (!topPostData?.id && !topPostData?.social_id)}
        >
          <Eye size={14} className="mr-2" />
          {onViewDetails && (topPostData?.id || topPostData?.social_id) ? 'View Comprehensive Details' : 'View Full Post'}
        </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default TopPerformingPost;
