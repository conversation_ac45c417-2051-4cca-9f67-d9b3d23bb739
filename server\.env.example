# Server Environment Configuration Example
# Copy this file to .env and update the values

# Server Configuration
NODE_ENV=development
PORT=3000

# URLs
SERVER_URL=http://localhost:3000
CLIENT_URL=https://localhost:8080
WEBHOOK_BASE_URL=http://localhost:3000

# Unipile Configuration
UNIPILE_API_URL=https://api2.unipile.com:13290
UNIPILE_API_ACCESS_TOKEN=your_unipile_access_token_here

# Supabase Configuration (REQUIRED FOR PHASE 2)
SUPABASE_URL=https://qpvdmrvkrwhplskipfco.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Background Scheduler Configuration (OPTIONAL)
ENABLE_BACKGROUND_SCHEDULER=true
SCHEDULER_CHECK_INTERVAL=15
SCHEDULER_MAX_USERS_PER_CYCLE=5

# Database Configuration (if using direct database connection)
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Other API Keys (if needed)
# OPENAI_API_KEY=your_openai_key_here
# STRIPE_SECRET_KEY=your_stripe_key_here
