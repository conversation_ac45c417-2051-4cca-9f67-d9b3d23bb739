import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Heart, MessageCircle, Share2, Eye, TrendingUp, TrendingDown } from 'lucide-react';

interface AnimatedIconFillProps {
  icon: 'heart' | 'message' | 'share' | 'eye';
  currentValue: number;
  previousValue?: number;
  showAnimation?: boolean;
  animationDuration?: number;
  className?: string;
}

export const AnimatedIconFill: React.FC<AnimatedIconFillProps> = ({
  icon,
  currentValue,
  previousValue,
  showAnimation = false,
  animationDuration = 3000,
  className = ''
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [fillLevel, setFillLevel] = useState(0);

  useEffect(() => {
    if (showAnimation && previousValue !== undefined && previousValue !== currentValue) {
      setIsAnimating(true);
      
      // Start the filling animation
      let progress = 0;
      const interval = setInterval(() => {
        progress += 2; // Increase by 2% each step
        setFillLevel(progress);
        
        if (progress >= 100) {
          clearInterval(interval);
          // Start emptying after a brief pause
          setTimeout(() => {
            const emptyInterval = setInterval(() => {
              progress -= 4; // Empty faster than fill
              setFillLevel(Math.max(0, progress));
              
              if (progress <= 0) {
                clearInterval(emptyInterval);
                setIsAnimating(false);
                setFillLevel(0);
              }
            }, 30);
          }, 500); // Pause for 500ms when full
        }
      }, 30); // 30ms intervals for smooth animation

      // Cleanup after total duration
      const cleanup = setTimeout(() => {
        clearInterval(interval);
        setIsAnimating(false);
        setFillLevel(0);
      }, animationDuration);

      return () => {
        clearInterval(interval);
        clearTimeout(cleanup);
      };
    }
  }, [showAnimation, currentValue, previousValue, animationDuration]);

  const getIconConfig = () => {
    switch (icon) {
      case 'heart':
        return {
          Icon: Heart,
          color: 'text-red-500',
          fillColor: 'fill-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        };
      case 'message':
        return {
          Icon: MessageCircle,
          color: 'text-blue-500',
          fillColor: 'fill-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        };
      case 'share':
        return {
          Icon: Share2,
          color: 'text-green-500',
          fillColor: 'fill-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        };
      case 'eye':
        return {
          Icon: Eye,
          color: 'text-purple-500',
          fillColor: 'fill-purple-500',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200'
        };
      default:
        return {
          Icon: Heart,
          color: 'text-gray-500',
          fillColor: 'fill-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
    }
  };

  const config = getIconConfig();
  const Icon = config.Icon;
  const difference = previousValue !== undefined ? currentValue - previousValue : 0;
  const isIncrease = difference > 0;
  const isDecrease = difference < 0;

  const formatNumber = (num: number) => {
    if (!num || isNaN(num)) return '0';
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className={cn('relative inline-flex items-center gap-1', className)}>
      {/* Animated Icon Container */}
      <div className="relative">
        {/* Background Icon (always visible) */}
        <Icon 
          size={14} 
          className={cn(
            'transition-all duration-300',
            isAnimating ? config.color : config.color
          )} 
        />
        
        {/* Animated Fill Overlay */}
        {isAnimating && (
          <div 
            className="absolute inset-0 overflow-hidden"
            style={{
              clipPath: `inset(${100 - fillLevel}% 0 0 0)`
            }}
          >
            <Icon 
              size={14} 
              className={cn(config.fillColor, 'animate-pulse')}
            />
          </div>
        )}

        {/* Glow Effect */}
        {isAnimating && (
          <div className="absolute inset-0 -z-10">
            <Icon 
              size={18} 
              className={cn(
                config.color,
                'opacity-30 blur-sm animate-pulse'
              )}
            />
          </div>
        )}
      </div>

      {/* Animated Value */}
      <span
        className={cn(
          'font-medium transition-all duration-300',
          isAnimating && config.color,
          isAnimating && 'animate-pulse'
        )}
      >
        {formatNumber(currentValue)}
      </span>

      {/* Change Indicator */}
      {isAnimating && previousValue !== undefined && difference !== 0 && (
        <div className="absolute -top-6 left-0 right-0 flex items-center justify-center">
          <div
            className={cn(
              'flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium animate-bounce',
              isIncrease && 'bg-green-100 text-green-700',
              isDecrease && 'bg-red-100 text-red-700'
            )}
          >
            {isIncrease && <TrendingUp className="w-3 h-3" />}
            {isDecrease && <TrendingDown className="w-3 h-3" />}
            <span className="text-[10px] opacity-60">
              {previousValue} → {currentValue}
            </span>
            <span className="text-[10px] font-bold">
              {isIncrease ? '+' : ''}{difference}
            </span>
          </div>
        </div>
      )}

      {/* Subtle Background Pulse */}
      {isAnimating && (
        <div
          className={cn(
            'absolute inset-0 -z-20 rounded-lg animate-pulse',
            config.bgColor,
            'opacity-50'
          )}
          style={{
            transform: 'scale(1.2)',
            filter: 'blur(8px)'
          }}
        />
      )}
    </div>
  );
};

// Enhanced hook for metric updates with icon animations
export const useIconMetricUpdates = () => {
  const [previousMetrics, setPreviousMetrics] = useState<Record<string, any>>({});
  const [updatedMetrics, setUpdatedMetrics] = useState<Set<string>>(new Set());

  const updateMetrics = React.useCallback((newMetrics: Record<string, any>, postId: string) => {
    if (!newMetrics || !postId) return new Set<string>();

    setPreviousMetrics(prev => {
      const prevMetrics = prev[postId] || {};

      // Check which metrics have changed
      const changedMetrics = new Set<string>();
      Object.keys(newMetrics).forEach(key => {
        const newValue = newMetrics[key] || 0;
        const prevValue = prevMetrics[key] || 0;
        if (prevValue !== newValue && prevValue !== undefined) {
          changedMetrics.add(`${postId}-${key}`);
        }
      });

      // Mark metrics as updated only if there are actual changes
      if (changedMetrics.size > 0) {
        setUpdatedMetrics(changedMetrics);

        // Clear update indicators after animation
        setTimeout(() => {
          setUpdatedMetrics(new Set());
        }, 3000);
      }

      // Store current metrics as previous for next comparison
      return {
        ...prev,
        [postId]: newMetrics
      };
    });

    return new Set<string>();
  }, []);

  const isMetricUpdated = React.useCallback((postId: string, metricKey: string) => {
    return updatedMetrics.has(`${postId}-${metricKey}`);
  }, [updatedMetrics]);

  const getPreviousValue = React.useCallback((postId: string, metricKey: string) => {
    return previousMetrics[postId]?.[metricKey];
  }, [previousMetrics]);

  return {
    updateMetrics,
    isMetricUpdated,
    getPreviousValue
  };
};
