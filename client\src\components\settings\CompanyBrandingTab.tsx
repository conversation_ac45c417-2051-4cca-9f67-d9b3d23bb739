
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';

// Import hooks
import { useBrandingData } from '@/hooks/useBrandingData';
import { useAIEnhancement } from '@/hooks/useAIEnhancement';
import { useLinkedInExamples } from '@/hooks/useLinkedInExamples';

// Import components
import CoreValuesSection from './branding/CoreValuesSection';
import EnhanceableTextSection from './branding/EnhanceableTextSection';
import ContentGuidelinesSection from './branding/ContentGuidelinesSection';
import LinkedInExamplesList from './branding/LinkedInExamplesList';
import LinkedInExampleDialog from './branding/LinkedInExampleDialog';
import VisualBrandingSection from './branding/VisualBrandingSection';
import BrandColorEditor from './BrandColorEditor';

const CompanyBrandingTab = () => {
  const { profile, isAdmin } = useAuth();
  const [colorEditorOpen, setColorEditorOpen] = React.useState(false);

  // Use the branding data hook
  const brandingData = useBrandingData({
    profileId: profile?.id
  });

  // Use the AI enhancement hook
  const aiEnhancement = useAIEnhancement({
    onToneChange: brandingData.setToneOfVoice,
    onDescriptionChange: brandingData.setCompanyDescription,
    onAudienceChange: brandingData.setTargetAudience,
    onActionChange: brandingData.setAudienceAction
  });

  // Use the LinkedIn examples hook
  const linkedInExamples = useLinkedInExamples({
    initialExamples: brandingData.postExamples,
    onExamplesChange: brandingData.setPostExamples
  });

  // Debug panel for troubleshooting (visible in development)
  const DebugPanel = () => {
    if (import.meta.env.NODE_ENV !== 'development') return null;
    
    return (
      <Card className="mt-8 bg-slate-100 border-red-300">
        <CardHeader>
          <CardTitle className="text-sm">Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-xs font-mono whitespace-pre-wrap max-h-40 overflow-auto">
            Profile ID: {profile?.id || 'Not available'}<br/>
            Branding Record ID: {brandingData.brandingId || 'None (new record)'}<br/>
            Has Unsaved Changes: {brandingData.hasUnsavedChanges ? 'Yes' : 'No'}<br/>
            {brandingData.debugInfo}
          </div>
        </CardContent>
      </Card>
    );
  };

  // If not an admin, show restricted access message
  if (!isAdmin()) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Company Branding</CardTitle>
          <CardDescription>
            Company branding can only be managed by administrators.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>You need administrator privileges to access company branding settings.</p>
        </CardContent>
      </Card>
    );
  }

  if (brandingData.isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-enterprise-blue" />
        <span className="ml-2 text-lg">Loading branding settings...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Brand Voice & Tone</CardTitle>
          <CardDescription>
            Define the guidelines for your company's voice on social media
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Core Values Section */}
          <CoreValuesSection 
            coreValues={brandingData.coreValues}
            setCoreValues={brandingData.setCoreValues}
          />
          
          {/* Enhanceable Text Sections */}
          <EnhanceableTextSection 
            id="tone-voice"
            label="Describe Your Tone of Voice"
            value={brandingData.toneOfVoice}
            onChange={brandingData.setToneOfVoice}
            onEnhance={() => aiEnhancement.enhanceText(brandingData.toneOfVoice, 'tone')}
            isEnhancing={aiEnhancement.enhancingTone}
            description="Describe how your brand communicates - formal, casual, technical, friendly, etc."
            enhancementType="tone"
          />
          
          <EnhanceableTextSection 
            id="company-description"
            label="Describe what your company does"
            value={brandingData.companyDescription}
            onChange={brandingData.setCompanyDescription}
            onEnhance={() => aiEnhancement.enhanceText(brandingData.companyDescription, 'description')}
            isEnhancing={aiEnhancement.enhancingDesc}
            description="Provide a clear, concise description of your company's products or services."
            placeholder="We are a software company that provides enterprise solutions for..."
            rows={3}
            enhancementType="description"
          />
          
          <EnhanceableTextSection 
            id="target-audience"
            label="Describe your target audience"
            value={brandingData.targetAudience}
            onChange={brandingData.setTargetAudience}
            onEnhance={() => aiEnhancement.enhanceText(brandingData.targetAudience, 'audience')}
            isEnhancing={aiEnhancement.enhancingAudience}
            description="Define the demographics, roles, industries, and pain points of your ideal customers."
            placeholder="Our target audience includes mid-size business executives who..."
            rows={3}
            enhancementType="audience"
          />
          
          <EnhanceableTextSection 
            id="audience-action"
            label="What do you want your target audience to do?"
            value={brandingData.audienceAction}
            onChange={brandingData.setAudienceAction}
            onEnhance={() => aiEnhancement.enhanceText(brandingData.audienceAction, 'action')}
            isEnhancing={aiEnhancement.enhancingAction}
            description="Clearly define the desired actions you want your audience to take after engaging with your content."
            placeholder="We want our audience to schedule a demo of our product and..."
            rows={3}
            enhancementType="action"
          />
          
          {/* Content Guidelines Section */}
          <ContentGuidelinesSection 
            dosValue={brandingData.dosContent}
            dontsValue={brandingData.dontsContent}
            onDosChange={brandingData.setDosContent}
            onDontsChange={brandingData.setDontsContent}
          />
          
          {/* Hashtags Section */}
          <div className="space-y-2">
            <label htmlFor="approved-hashtags">Approved Hashtags</label>
            <p className="text-sm text-muted-foreground">Enter hashtags separated by commas</p>
            <Input 
              id="approved-hashtags" 
              value={brandingData.hashTags}
              onChange={(e) => brandingData.setHashTags(e.target.value)}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className={`text-sm ${brandingData.hasUnsavedChanges ? "text-amber-500" : "text-green-500"}`}>
            {brandingData.hasUnsavedChanges ? "Unsaved changes" : "All changes saved"}
          </div>
          <Button 
            onClick={() => brandingData.handleSaveAll(false)}
            className="bg-enterprise-blue hover:bg-enterprise-blue/90"
            disabled={brandingData.isSaving || !brandingData.hasUnsavedChanges}
          >
            {brandingData.isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Saving...
              </>
            ) : (
              'Save Brand Guidelines'
            )}
          </Button>
        </CardFooter>
      </Card>
      
      {/* LinkedIn Post Examples Section */}
      <Card>
        <CardHeader>
          <CardTitle>LinkedIn Post Examples</CardTitle>
          <CardDescription>
            Add examples of ideal LinkedIn posts to reference when generating new content
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <LinkedInExamplesList 
            examples={linkedInExamples.examples}
            onAddExample={linkedInExamples.handleAddExample}
            onEditExample={linkedInExamples.handleEditExample}
            onDeleteExample={linkedInExamples.handleDeleteExample}
          />
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className={`text-sm ${brandingData.hasUnsavedChanges ? "text-amber-500" : "text-green-500"}`}>
            {brandingData.hasUnsavedChanges ? "Unsaved changes" : "All changes saved"}
          </div>
          <Button 
            onClick={() => brandingData.handleSaveAll(false)}
            className="bg-enterprise-blue hover:bg-enterprise-blue/90"
            disabled={brandingData.isSaving || !brandingData.hasUnsavedChanges}
          >
            {brandingData.isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Saving...
              </>
            ) : (
              'Save LinkedIn Examples'
            )}
          </Button>
        </CardFooter>
      </Card>
      
      {/* Visual Branding Section */}
      <Card>
        <CardHeader>
          <CardTitle>Visual Branding</CardTitle>
          <CardDescription>
            Upload your company's visual assets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <VisualBrandingSection 
            brandColors={brandingData.brandColors}
            onEditColors={() => setColorEditorOpen(true)}
            onSave={() => brandingData.handleSaveAll(false)}
          />
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className={`text-sm ${brandingData.hasUnsavedChanges ? "text-amber-500" : "text-green-500"}`}>
            {brandingData.hasUnsavedChanges ? "Unsaved changes" : "All changes saved"}
          </div>
          <Button 
            onClick={() => brandingData.handleSaveAll(false)}
            className="bg-enterprise-blue hover:bg-enterprise-blue/90"
            disabled={brandingData.isSaving || !brandingData.hasUnsavedChanges}
          >
            {brandingData.isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Saving...
              </>
            ) : (
              'Save Visual Branding'
            )}
          </Button>
        </CardFooter>
      </Card>

      {/* Debug Panel */}
      <DebugPanel />

      {/* Dialogs */}
      <LinkedInExampleDialog 
        open={linkedInExamples.dialogOpen}
        onOpenChange={linkedInExamples.setDialogOpen}
        currentExample={linkedInExamples.currentExample}
        isEditing={linkedInExamples.isEditing}
        onSave={linkedInExamples.handleSaveExample}
        onSubjectChange={linkedInExamples.handleExampleSubjectChange}
        onContentChange={linkedInExamples.handleExampleContentChange}
      />

      <BrandColorEditor 
        open={colorEditorOpen}
        onOpenChange={setColorEditorOpen}
        colors={brandingData.brandColors}
        onSave={brandingData.setBrandColors}
      />
    </div>
  );
};

export default CompanyBrandingTab;
