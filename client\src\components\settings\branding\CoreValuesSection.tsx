
import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface CoreValuesSectionProps {
  coreValues: string[];
  setCoreValues: React.Dispatch<React.SetStateAction<string[]>>;
}

const CoreValuesSection = ({ coreValues, setCoreValues }: CoreValuesSectionProps) => {
  const [newCoreValue, setNewCoreValue] = useState('');
  const { toast } = useToast();

  // Function to add a new core value
  const handleAddCoreValue = () => {
    if (newCoreValue.trim() !== '') {
      if (coreValues.length >= 5) {
        toast({
          title: "Maximum values reached",
          description: "You can have a maximum of 5 core values.",
          variant: "destructive"
        });
        return;
      }
      
      if (!coreValues.includes(newCoreValue)) {
        setCoreValues([...coreValues, newCoreValue]);
        setNewCoreValue('');
        toast({
          title: "Core value added",
          description: `"${newCoreValue}" has been added to your core values.`
        });
      } else {
        toast({
          title: "Duplicate value",
          description: "This core value already exists.",
          variant: "destructive"
        });
      }
    }
  };

  // Function to remove a core value
  const handleRemoveCoreValue = (valueToRemove: string) => {
    setCoreValues(coreValues.filter(value => value !== valueToRemove));
    toast({
      title: "Core value removed",
      description: `"${valueToRemove}" has been removed from your core values.`
    });
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="brand-attributes" className="text-base">Core Values</Label>
      <p className="text-sm text-muted-foreground">Add up to 5 key values that define your brand</p>
      <div className="flex flex-wrap gap-2 my-2">
        {coreValues.map((value, index) => (
          <Badge key={index} className="bg-enterprise-blue flex items-center gap-1">
            {value}
            <X 
              className="h-3 w-3 cursor-pointer hover:text-red-200 ml-1" 
              onClick={() => handleRemoveCoreValue(value)}
            />
          </Badge>
        ))}
      </div>
      <div className="flex gap-2">
        <Input 
          id="brand-attributes" 
          placeholder="Add new core value..." 
          value={newCoreValue}
          onChange={(e) => setNewCoreValue(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleAddCoreValue()}
          disabled={coreValues.length >= 5}
        />
        <Button 
          onClick={handleAddCoreValue} 
          disabled={!newCoreValue.trim() || coreValues.length >= 5}
          className="bg-enterprise-blue hover:bg-enterprise-blue/90"
        >
          Add
        </Button>
      </div>
      {coreValues.length >= 5 && (
        <p className="text-xs text-amber-600">You've reached the maximum of 5 core values.</p>
      )}
    </div>
  );
};

export default CoreValuesSection;
