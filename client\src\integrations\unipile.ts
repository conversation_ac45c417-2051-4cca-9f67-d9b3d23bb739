
import { UnipileClient } from 'unipile-node-sdk';
import { UNIPILE_API_URL } from '@/config/env';

const UNIPILE_DSN = UNIPILE_API_URL;
const UNIPILE_ACCESS_TOKEN = import.meta.env.VITE_UNIPILE_ACCESS_TOKEN || "0+wWYama.5zEfWC/CX9YvKKp+0zcmQI8DDbi9sGs6Zyyp9Ujrzc0="

if (!UNIPILE_DSN || !UNIPILE_ACCESS_TOKEN) {
  throw new Error('UNIPILE_DSN and UNIPILE_ACCESS_TOKEN must be provided');
}

const client = new UnipileClient(UNIPILE_DSN, UNIPILE_ACCESS_TOKEN);

export { client };
