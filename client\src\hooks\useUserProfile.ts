
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { fetchUserRole } from '@/utils/auth';

export interface UserProfile {
  id: string;
  email: string;
  role: string;
  first_name?: string;
  last_name?: string;
  linkedin_url?: string;
  linkedin_connection_status?: string;
  phyllo_id?: string;
}

export const useUserProfile = () => {
  const [profile, setProfile] = useState<UserProfile | null>(null);

  const fetchUserProfile = async (userId: string) => {
    try {
      // First try to get the role directly from the edge function
      try {
        const { role } = await fetchUserRole();
        
        // Fetch additional profile details from the profiles table
        const { data, error } = await supabase
          .from('profiles')
          .select('first_name, last_name, linkedin_url')
          .eq('id', userId)
          .single();

        if (error) {
          console.error('Error fetching additional profile details:', error);
          return;
        }

        // Get user email from auth session
        const { data: sessionData } = await supabase.auth.getSession();
        const email = sessionData.session?.user?.email || '';

        // Create and set the user profile with safely accessed properties
        setProfile({
          id: userId,
          email: email,
          role,
          first_name: data?.first_name,
          last_name: data?.last_name,
          linkedin_url: data?.linkedin_url,
          linkedin_connection_status: undefined,
          phyllo_id: undefined
        });
        return;
      } catch (error) {
        console.log('Falling back to direct Supabase query for profile');
      }
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
    }
  };

  return { profile, setProfile, fetchUserProfile };
};
