
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

const SecurityTab = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Security Settings</CardTitle>
          <CardDescription>
            Configure security options for your account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Authentication</h3>
            
            <div className="flex items-center justify-between py-2">
              <div>
                <div className="flex items-center space-x-2">
                  <Switch id="sso-auth" defaultChecked />
                  <Label htmlFor="sso-auth">
                    Single Sign-On (SSO)
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground pl-7">
                  Allow users to sign in with your company's identity provider
                </p>
              </div>
              <Button variant="outline" size="sm">Configure</Button>
            </div>
            
            <div className="flex items-center justify-between py-2">
              <div>
                <div className="flex items-center space-x-2">
                  <Switch id="mfa" defaultChecked />
                  <Label htmlFor="mfa">
                    Multi-factor Authentication
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground pl-7">
                  Require two-factor authentication for all users
                </p>
              </div>
              <Button variant="outline" size="sm">Configure</Button>
            </div>
          </div>
          
          <div className="space-y-4 pt-4">
            <h3 className="text-sm font-medium">Content Security</h3>
            
            <div className="flex items-center justify-between py-2">
              <div>
                <div className="flex items-center space-x-2">
                  <Switch id="content-review" defaultChecked />
                  <Label htmlFor="content-review">
                    AI Content Review
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground pl-7">
                  Automatically scan content for potential compliance issues
                </p>
              </div>
            </div>
            
            <div className="flex items-center justify-between py-2">
              <div>
                <div className="flex items-center space-x-2">
                  <Switch id="keyword-alerts" defaultChecked />
                  <Label htmlFor="keyword-alerts">
                    Sensitive Keyword Alerts
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground pl-7">
                  Flag content with sensitive or restricted keywords
                </p>
              </div>
              <Button variant="outline" size="sm">Manage Keywords</Button>
            </div>
          </div>
          
          <div className="space-y-4 pt-4">
            <h3 className="text-sm font-medium">Data Protection</h3>
            
            <div className="flex items-center justify-between py-2">
              <div>
                <div className="flex items-center space-x-2">
                  <Switch id="data-encryption" defaultChecked />
                  <Label htmlFor="data-encryption">
                    Data Encryption
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground pl-7">
                  Encrypt all stored data and communications
                </p>
              </div>
            </div>
            
            <div className="flex items-center justify-between py-2">
              <div>
                <div className="flex items-center space-x-2">
                  <Switch id="audit-logs" defaultChecked />
                  <Label htmlFor="audit-logs">
                    Audit Logs
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground pl-7">
                  Keep detailed logs of all user activities
                </p>
              </div>
              <Button variant="outline" size="sm">View Logs</Button>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button className="bg-enterprise-blue hover:bg-enterprise-blue/90">
            Save Security Settings
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SecurityTab;
