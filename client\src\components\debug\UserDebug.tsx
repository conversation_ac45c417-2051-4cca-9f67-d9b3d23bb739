import React from 'react';
import { useAuth } from '@/context/AuthContext';

const UserDebug: React.FC = () => {
  const { user, profile, isLoading } = useAuth();

  if (!import.meta.env.DEV) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-md z-50">
      <h3 className="font-bold mb-2">🔍 User Debug Info</h3>
      <div className="space-y-1">
        <div><strong>Loading:</strong> {isLoading ? 'true' : 'false'}</div>
        <div><strong>User ID:</strong> {user?.id || 'undefined'}</div>
        <div><strong>User Email:</strong> {user?.email || 'undefined'}</div>
        <div><strong>User Object:</strong> {user ? 'exists' : 'null'}</div>
        <div><strong>Profile ID:</strong> {profile?.id || 'undefined'}</div>
        <div><strong>Profile Object:</strong> {profile ? 'exists' : 'null'}</div>
        <div><strong>LinkedIn Connected:</strong> {
          user?.user_metadata?.linkedinAccountId || 
          user?.user_metadata?.linkedin_account_id || 
          profile?.linkedin_url ? 'true' : 'false'
        }</div>
        <div><strong>LinkedIn Account ID:</strong> {
          user?.user_metadata?.linkedinAccountId || 
          user?.user_metadata?.linkedin_account_id || 
          'undefined'
        }</div>
      </div>
    </div>
  );
};

export default UserDebug;
