/**
 * Security Verification Utilities
 * 
 * This file contains utilities to verify that LinkedIn passwords
 * are not being stored in local storage, session storage, or
 * sent to Supabase.
 */

export interface SecurityCheckResult {
  passed: boolean;
  message: string;
  details?: string[];
}

/**
 * Check if LinkedIn password exists in local storage
 */
export const checkLocalStorageForLinkedInPassword = (): SecurityCheckResult => {
  const issues: string[] = [];
  
  try {
    // Check all localStorage keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const value = localStorage.getItem(key);
        if (value) {
          try {
            const parsed = JSON.parse(value);
            if (checkObjectForLinkedInPassword(parsed, key)) {
              issues.push(`LinkedIn password found in localStorage key: ${key}`);
            }
          } catch {
            // Not JSON, check string directly
            if (value.toLowerCase().includes('linkedin') && 
                (value.toLowerCase().includes('password') || value.toLowerCase().includes('pwd'))) {
              issues.push(`Potential LinkedIn password in localStorage key: ${key}`);
            }
          }
        }
      }
    }
    
    return {
      passed: issues.length === 0,
      message: issues.length === 0 
        ? "✅ No LinkedIn passwords found in localStorage" 
        : "❌ LinkedIn passwords detected in localStorage",
      details: issues
    };
  } catch (error) {
    return {
      passed: false,
      message: "❌ Error checking localStorage",
      details: [`Error: ${error}`]
    };
  }
};

/**
 * Check if LinkedIn password exists in session storage
 */
export const checkSessionStorageForLinkedInPassword = (): SecurityCheckResult => {
  const issues: string[] = [];
  
  try {
    // Check all sessionStorage keys
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key) {
        const value = sessionStorage.getItem(key);
        if (value) {
          try {
            const parsed = JSON.parse(value);
            if (checkObjectForLinkedInPassword(parsed, key)) {
              issues.push(`LinkedIn password found in sessionStorage key: ${key}`);
            }
          } catch {
            // Not JSON, check string directly
            if (value.toLowerCase().includes('linkedin') && 
                (value.toLowerCase().includes('password') || value.toLowerCase().includes('pwd'))) {
              issues.push(`Potential LinkedIn password in sessionStorage key: ${key}`);
            }
          }
        }
      }
    }
    
    return {
      passed: issues.length === 0,
      message: issues.length === 0 
        ? "✅ No LinkedIn passwords found in sessionStorage" 
        : "❌ LinkedIn passwords detected in sessionStorage",
      details: issues
    };
  } catch (error) {
    return {
      passed: false,
      message: "❌ Error checking sessionStorage",
      details: [`Error: ${error}`]
    };
  }
};

/**
 * Recursively check an object for LinkedIn password fields
 */
const checkObjectForLinkedInPassword = (obj: any, path: string): boolean => {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }
  
  const suspiciousKeys = [
    'linkedin_password',
    'linkedinPassword',
    'linkedin_pwd',
    'linkedinPwd'
  ];
  
  for (const key of Object.keys(obj)) {
    // Check if key itself is suspicious
    if (suspiciousKeys.includes(key)) {
      console.warn(`🚨 SECURITY ALERT: LinkedIn password field found at ${path}.${key}`);
      return true;
    }
    
    // Check if key contains linkedin and password
    if (key.toLowerCase().includes('linkedin') && 
        (key.toLowerCase().includes('password') || key.toLowerCase().includes('pwd'))) {
      console.warn(`🚨 SECURITY ALERT: Potential LinkedIn password field found at ${path}.${key}`);
      return true;
    }
    
    // Recursively check nested objects
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      if (checkObjectForLinkedInPassword(obj[key], `${path}.${key}`)) {
        return true;
      }
    }
  }
  
  return false;
};

/**
 * Check Supabase user metadata for LinkedIn passwords
 */
export const checkSupabaseUserMetadata = (user: any): SecurityCheckResult => {
  if (!user) {
    return {
      passed: true,
      message: "✅ No user data to check"
    };
  }
  
  const issues: string[] = [];
  
  try {
    // Check user metadata
    if (user.user_metadata && checkObjectForLinkedInPassword(user.user_metadata, 'user_metadata')) {
      issues.push("LinkedIn password found in user_metadata");
    }
    
    // Check app metadata
    if (user.app_metadata && checkObjectForLinkedInPassword(user.app_metadata, 'app_metadata')) {
      issues.push("LinkedIn password found in app_metadata");
    }
    
    // Check raw user object
    if (checkObjectForLinkedInPassword(user, 'user')) {
      issues.push("LinkedIn password found in user object");
    }
    
    return {
      passed: issues.length === 0,
      message: issues.length === 0 
        ? "✅ No LinkedIn passwords found in Supabase user data" 
        : "❌ LinkedIn passwords detected in Supabase user data",
      details: issues
    };
  } catch (error) {
    return {
      passed: false,
      message: "❌ Error checking Supabase user metadata",
      details: [`Error: ${error}`]
    };
  }
};

/**
 * Run comprehensive security check
 */
export const runComprehensiveSecurityCheck = (user?: any): SecurityCheckResult[] => {
  const results: SecurityCheckResult[] = [];
  
  results.push(checkLocalStorageForLinkedInPassword());
  results.push(checkSessionStorageForLinkedInPassword());
  
  if (user) {
    results.push(checkSupabaseUserMetadata(user));
  }
  
  return results;
};

/**
 * Display security check results in console
 */
export const displaySecurityCheckResults = (results: SecurityCheckResult[]): void => {
  console.group("🔒 LinkedIn Password Security Check Results");
  
  let allPassed = true;
  
  results.forEach((result, index) => {
    if (result.passed) {
      console.log(`${index + 1}. ${result.message}`);
    } else {
      console.error(`${index + 1}. ${result.message}`);
      if (result.details) {
        result.details.forEach(detail => console.error(`   - ${detail}`));
      }
      allPassed = false;
    }
  });
  
  if (allPassed) {
    console.log("\n🎉 All security checks passed! LinkedIn passwords are properly protected.");
  } else {
    console.error("\n🚨 SECURITY ISSUES DETECTED! Please review and fix the issues above.");
  }
  
  console.groupEnd();
};

/**
 * Monitor for LinkedIn password storage attempts
 */
export const monitorLinkedInPasswordStorage = (): void => {
  // Override localStorage.setItem to monitor for LinkedIn passwords
  const originalSetItem = localStorage.setItem;
  localStorage.setItem = function(key: string, value: string) {
    try {
      const parsed = JSON.parse(value);
      if (checkObjectForLinkedInPassword(parsed, `localStorage.${key}`)) {
        console.error("🚨 BLOCKED: Attempt to store LinkedIn password in localStorage");
        return;
      }
    } catch {
      // Not JSON, check string
      if (value.toLowerCase().includes('linkedin') && 
          (value.toLowerCase().includes('password') || value.toLowerCase().includes('pwd'))) {
        console.warn("⚠️ WARNING: Potential LinkedIn password storage attempt in localStorage");
      }
    }
    originalSetItem.call(this, key, value);
  };
  
  // Override sessionStorage.setItem to monitor for LinkedIn passwords
  const originalSessionSetItem = sessionStorage.setItem;
  sessionStorage.setItem = function(key: string, value: string) {
    try {
      const parsed = JSON.parse(value);
      if (checkObjectForLinkedInPassword(parsed, `sessionStorage.${key}`)) {
        console.error("🚨 BLOCKED: Attempt to store LinkedIn password in sessionStorage");
        return;
      }
    } catch {
      // Not JSON, check string
      if (value.toLowerCase().includes('linkedin') && 
          (value.toLowerCase().includes('password') || value.toLowerCase().includes('pwd'))) {
        console.warn("⚠️ WARNING: Potential LinkedIn password storage attempt in sessionStorage");
      }
    }
    originalSessionSetItem.call(this, key, value);
  };
  
  console.log("🔒 LinkedIn password storage monitoring activated");
};
