
import axios, { AxiosResponse } from "axios";

const PHYLLO_BASE_URL = "https://api.sandbox.getphyllo.com";
const URL_CREATE_USER = "/v1/users";
const URL_CREATE_USER_TOKEN = "/v1/sdk-tokens";
const URL_GET_ACCOUNT = "/v1/accounts";

const PHYLLO_CLIENT_ID = import.meta.env.VITE_PHYLLO_CLIENT_ID;
const PHYLLO_SECRET_ID = import.meta.env.VITE_PHYLLO_CLIENT_SECRET;

interface PhylloUser {
  id: string;
  name: string;
  external_id: string;
}

interface PhylloToken {
  sdk_token: string;
}

console.log({
  PHYLLO_BASE_URL,
  PHYLLO_CLIENT_ID,
  PHYLLO_SECRET_ID
})

interface PhylloError {
  body: unknown;
}

interface ConnectLinkedInOptions {
  userId: string;
  onSuccess?: (accountId: string, workplatformId: string, userId: string) => void;
  onError?: (error: Error) => void;
  onExit?: () => void;
}

const getAxiosInstance = () => {
  const credentials = `${PHYLLO_CLIENT_ID}:${PHYLLO_SECRET_ID}`;
  console.log({credentials})
  const api = axios.create({
    baseURL: PHYLLO_BASE_URL,
    headers: {
      'Authorization': `Basic ${credentials}`,
      'Content-Type': 'application/json'
    }
  });
  return api;
};

// Helper class for LinkedIn connection
class PhylloHelper {
  async connectLinkedIn(options: ConnectLinkedInOptions) {
    // Implementation would go here
    console.log("Connecting to LinkedIn with options:", options);
    // This is a placeholder - in a real implementation, this would use the Phyllo SDK
    if (options.onSuccess) {
      options.onSuccess("mock-account-id", "mock-workplatform-id", options.userId);
    }
  }
}

const createUser = async (username: string, externalId: string): Promise<string | PhylloError> => {
  try {
    const userId = localStorage.getItem("PHYLLO_USER_ID");
    if (userId) {
      localStorage.setItem("PHYLLO_USER_ID", null)
    }
    console.log({userId})
    const api = getAxiosInstance();
    const response: AxiosResponse<PhylloUser> = await api.post(URL_CREATE_USER, {
      name: username,
      external_id: externalId,
    });
    console.log({response})
    localStorage.setItem("PHYLLO_USER_ID", response.data.id);
    return response.data.id;
  } catch (err) {
    return { body: err };
  }
};

const createUserToken = async (userId: string): Promise<string | PhylloError> => {
  try {
    const token = localStorage.getItem("PHYLLO_SDK_TOKEN");
    if (token) {
      return token;
    }
    const api = getAxiosInstance();
    const response: AxiosResponse<PhylloToken> = await api.post(URL_CREATE_USER_TOKEN, {
      user_id: userId,
      products: ["IDENTITY", "ENGAGEMENT"],
    });
    localStorage.setItem("PHYLLO_SDK_TOKEN", response.data.sdk_token);
    return response.data.sdk_token;
  } catch (err) {
    return { body: err };
  }
};

const getAccounts = async (userId: string): Promise<AxiosResponse | PhylloError> => {
  try {
    const api = getAxiosInstance();
    const response = await api.get(`${URL_GET_ACCOUNT}?user_id=${userId}`);
    return response;
  } catch (err) {
    return { body: err };
  }
};

export { createUser, createUserToken, getAccounts, PhylloHelper };
