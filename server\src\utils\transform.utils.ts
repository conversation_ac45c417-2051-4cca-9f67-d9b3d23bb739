/**
 * Data Transformation Utility Functions
 * Pure functions for data transformation and formatting
 */

/**
 * Transform user data for API response
 */
export const transformUserForResponse = (user: any) => {
    if (!user) return null;

    return {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
        linkedin_url: user.linkedin_url,
        linkedin_email: user.linkedin_email,
        account_id: user.account_id,
        linkedin_connection_status: user.linkedin_connection_status || 'not_connected',
        role: user.role || 'user',
        company_id: user.company_id,
        created_at: user.created_at,
        updated_at: user.updated_at
    };
};

/**
 * Transform LinkedIn profile data for API response
 */
export const transformLinkedInProfile = (profile: any) => {
    if (!profile) return null;

    return {
        id: profile.id,
        name: profile.name,
        type: profile.type,
        status: profile.status,
        connection_params: profile.connection_params,
        user_info: profile.connection_params?.im ? {
            id: profile.connection_params.im.id,
            public_identifier: profile.connection_params.im.publicIdentifier,
            username: profile.connection_params.im.username,
            premium_id: profile.connection_params.im.premiumId,
            organizations: profile.connection_params.im.organizations || []
        } : null,
        created_at: profile.created_at,
        updated_at: profile.updated_at
    };
};

/**
 * Transform LinkedIn post data for API response
 */
export const transformLinkedInPost = (post: any) => {
    if (!post) return null;

    return {
        id: post.id,
        text: post.text,
        author: post.author ? {
            id: post.author.id,
            name: post.author.name,
            username: post.author.username,
            profile_picture: post.author.profile_picture
        } : null,
        created_at: post.parsed_datetime,
        updated_at: post.updated_at,
        media: post.media || [],
        reactions: post.reactions || [],
        comments: post.comments || [],
        shares: post.shares || 0,
        views: post.views || 0,
        engagement: {
            total_reactions: post.reactions?.length || 0,
            total_comments: post.comments?.length || 0,
            total_shares: post.shares || 0,
            total_views: post.views || 0,
            engagement_rate: calculateEngagementRate(post)
        }
    };
};

/**
 * Transform analytics data for API response
 */
export const transformAnalyticsData = (analytics: any) => {
    if (!analytics) return null;

    return {
        profile: {
            follower_count: analytics.profile?.follower_count || 0,
            connection_count: analytics.profile?.connection_count || 0,
            profile_views: analytics.profile?.profile_views || 0
        },
        posts: {
            total_posts: analytics.posts?.length || 0,
            total_impressions: analytics.totalImpressions || 0,
            total_reactions: analytics.totalReactions || 0,
            total_comments: analytics.totalComments || 0,
            total_shares: analytics.totalShares || 0,
            average_engagement_rate: analytics.engagementRate || 0,
            posts_per_week: analytics.postsPerWeek || 0,
            impressions_per_post: analytics.impressionsPerPost || 0,
            impressions_per_follower: analytics.impressionsPerFollower || 0,
            likes_per_post: analytics.likesPerPost || 0
        },
        recent_posts: analytics.posts?.slice(0, 10).map(transformLinkedInPost) || [],
        last_sync: analytics.last_sync || null,
        sync_status: analytics.sync_status || 'pending'
    };
};

/**
 * Calculate engagement rate for a post
 */
export const calculateEngagementRate = (post: any): number => {
    if (!post) return 0;

    const reactions = post.reactions?.length || 0;
    const comments = post.comments?.length || 0;
    const shares = post.shares || 0;
    const views = post.views || 0;

    if (views === 0) return 0;

    const totalEngagement = reactions + comments + shares;
    return Math.round((totalEngagement / views) * 100 * 100) / 100; // Round to 2 decimal places
};

/**
 * Transform webhook data for processing
 */
export const transformWebhookData = (webhookData: any) => {
    return {
        status: webhookData.status,
        account_id: webhookData.account_id,
        name: webhookData.name,
        type: webhookData.type || 'LINKEDIN',
        timestamp: new Date().toISOString(),
        raw_data: webhookData
    };
};

/**
 * Transform error for API response
 */
export const transformError = (error: any) => {
    return {
        message: error.message || 'An error occurred',
        type: error.name || 'Error',
        code: error.code || 'UNKNOWN_ERROR',
        timestamp: new Date().toISOString()
    };
};

/**
 * Format date for API response
 */
export const formatDate = (date: Date | string): string => {
    if (!date) return '';
    return new Date(date).toISOString();
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (date: Date | string): string => {
    if (!date) return '';

    const now = new Date();
    const past = new Date(date);
    const diffMs = now.getTime() - past.getTime();

    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSeconds < 60) return 'just now';
    if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    
    return formatDate(date);
};

/**
 * Extract LinkedIn user ID from various sources
 */
export const extractLinkedInUserId = (data: any): string | null => {
    // Try to get from connection params
    if (data.connection_params?.im?.id) {
        return data.connection_params.im.id;
    }

    // Try to get from profile data
    if (data.profile?.id) {
        return data.profile.id;
    }

    // Try to extract from LinkedIn URL
    if (data.linkedin_url) {
        const match = data.linkedin_url.match(/linkedin\.com\/in\/([^\/]+)/);
        if (match) return match[1];
    }

    return null;
};

/**
 * Normalize LinkedIn account data
 */
export const normalizeLinkedInAccount = (account: any) => {
    return {
        id: account.id,
        name: account.name,
        type: account.type || 'LINKEDIN',
        status: account.status,
        linkedin_user_id: extractLinkedInUserId(account),
        public_identifier: account.connection_params?.im?.publicIdentifier,
        username: account.connection_params?.im?.username,
        premium_features: account.connection_params?.im?.premiumFeatures || [],
        organizations: account.connection_params?.im?.organizations || [],
        connection_params: account.connection_params,
        created_at: formatDate(account.created_at),
        updated_at: formatDate(account.updated_at)
    };
};

/**
 * Calculate analytics summary
 */
export const calculateAnalyticsSummary = (posts: any[], profile: any) => {
    if (!posts || posts.length === 0) {
        return {
            totalPosts: 0,
            totalImpressions: 0,
            totalReactions: 0,
            totalComments: 0,
            totalShares: 0,
            averageEngagementRate: 0,
            postsPerWeek: 0,
            impressionsPerPost: 0,
            impressionsPerFollower: 0,
            likesPerPost: 0
        };
    }

    const totalPosts = posts.length;
    const totalImpressions = posts.reduce((sum, post) => sum + (post.views || 0), 0);
    const totalReactions = posts.reduce((sum, post) => sum + (post.reactions?.length || 0), 0);
    const totalComments = posts.reduce((sum, post) => sum + (post.comments?.length || 0), 0);
    const totalShares = posts.reduce((sum, post) => sum + (post.shares || 0), 0);

    const averageEngagementRate = posts.reduce((sum, post) => sum + calculateEngagementRate(post), 0) / totalPosts;
    const impressionsPerPost = totalPosts > 0 ? totalImpressions / totalPosts : 0;
    const impressionsPerFollower = profile?.follower_count > 0 ? totalImpressions / profile.follower_count : 0;
    const likesPerPost = totalPosts > 0 ? totalReactions / totalPosts : 0;

    // Calculate posts per week (assuming posts are from recent period)
    const postsPerWeek = totalPosts; // Simplified calculation

    return {
        totalPosts,
        totalImpressions,
        totalReactions,
        totalComments,
        totalShares,
        averageEngagementRate: Math.round(averageEngagementRate * 100) / 100,
        postsPerWeek,
        impressionsPerPost: Math.round(impressionsPerPost),
        impressionsPerFollower: Math.round(impressionsPerFollower * 100) / 100,
        likesPerPost: Math.round(likesPerPost * 100) / 100
    };
};
