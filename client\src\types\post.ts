
export interface Post {
  id: string;
  title: string;
  content: string;
  post_date?: string;
  created_at: string;
  updated_at?: string;
  impressions?: number;
  likes?: number;
  comments?: number;
  engagement_rate?: number;
  post_type: 'company' | 'personal';
  department: 'sales' | 'marketing' | 'product' | 'engineering' | 'other';
  status: 'draft' | 'pending_approval' | 'published' | 'approved' | 'rejected';
  user_id: string;
  user?: {
    first_name?: string | null;
    last_name?: string | null;
    email?: string;
  } | null;
}

// Simpler version for approvals UI
export interface ApprovalPost {
  id: string;
  author: string;
  department: string;
  platform: string;
  content: string;
  submitted: string;
  approved?: string;
  rejected?: string;
  title?: string;
}

// Interface for additional settings
export interface BrandSettings {
  adminApprovalsEnabled?: boolean;
  [key: string]: any;
}
