
import React from 'react';

interface AnimatedEmojiProps {
  position: string;
  delay?: string;
}

const AnimatedEmoji = ({ position, delay = '0s' }: AnimatedEmojiProps) => {
  // Parse the position string into Tailwind classes
  const positionClasses = position;

  return (
    <div className={`absolute ${positionClasses} w-12 h-12 z-40 pointer-events-none`}>
      <img 
        src="/lovable-uploads/053be5f9-0f5c-4ffc-b85e-fb3eb7d692f8.png" 
        alt="Thumbs up emoji"
        className="object-contain w-full h-full animate-float-bounce drop-shadow-md"
        style={{ 
          animationDelay: delay,
          animationDuration: '3s',
          animationIterationCount: 'infinite',
          animationDirection: 'alternate'
        }}
      />
    </div>
  );
};

export default AnimatedEmoji;
