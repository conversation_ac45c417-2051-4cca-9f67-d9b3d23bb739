
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';

const JiveHeader = () => {
  return (
    <header className="sticky top-0 z-50 bg-white bg-opacity-95 backdrop-blur-sm py-4 px-4 sm:px-6 md:px-8 shadow-sm">
      <div className="flex items-center justify-between max-w-7xl mx-auto w-full">
        <div className="flex items-center">
          <span className="text-2xl font-bold">CompanyVoice</span>
        </div>
        <div className="flex items-center gap-4">
          <Link to="/login" className="text-sm font-medium hover:underline">
            Login
          </Link>
          <Link to="/signup">
            <Button size="sm" className="rounded-full bg-black text-white hover:bg-black/90">
              Sign up
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
};

export default JiveHeader;
