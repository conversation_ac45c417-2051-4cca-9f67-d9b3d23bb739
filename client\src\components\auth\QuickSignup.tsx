import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, Lock, User, LogIn } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { useAuthState } from '@/hooks/useAuthState';
import PageBackground from '@/components/layout/PageBackground';

// User tracking service
import { trackUserAction } from '@/services/analytics';
import { ToastManager } from '@/utils/toastManager';
import { storeCompanyDomain } from '@/services/api-new';

const formSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  isCompany: z.enum(['true', 'false']).default('true'),
}).refine((data) => {
  if (data.password !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
}).superRefine((data, ctx) => {
  // Email validation based on company employee status
  if (data.isCompany === 'true') {
    // For company employees: must NOT be gmail, yahoo, hotmail, outlook, or other common personal email domains
    const personalEmailDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com', 'icloud.com', 'protonmail.com', 'live.com', 'msn.com'];
    const emailDomain = data.email.split('@')[1]?.toLowerCase();

    if (!emailDomain) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please enter a valid email address",
        path: ["email"]
      });
      return;
    }

    if (personalEmailDomains.includes(emailDomain)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Company employees must use their company email address, not personal email services",
        path: ["email"]
      });
      return;
    }

    // Additional validation: must have a proper company domain (at least 2 parts after @)
    const domainParts = emailDomain.split('.');
    if (domainParts.length < 2 || domainParts.some(part => part.length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please enter a valid company email address",
        path: ["email"]
      });
      return;
    }
  }
  // For non-company users: allow any valid email (no additional restrictions)
  // The basic email validation from z.string().email() will handle format validation
});

type FormValues = z.infer<typeof formSchema>;



const QuickSignup: React.FC = () => {
  const navigate = useNavigate();
  const { signUp } = useAuthState();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      isCompany: "true",
    },
  });

  // Watch the company employee selection to update email placeholder and validation
  const isCompanyEmployee = form.watch('isCompany') === 'true';

  // Function to extract company domain from email
  const extractCompanyDomain = (email: string): string | null => {
    const emailParts = email.split('@');
    if (emailParts.length === 2) {
      const domain = emailParts[1].toLowerCase();
      // Exclude personal email domains
      const personalDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com', 'icloud.com', 'protonmail.com', 'live.com', 'msn.com'];
      if (!personalDomains.includes(domain)) {
        return domain;
      }
    }
    return null;
  };

  // Get email placeholder and helper text based on company employee status
  const getEmailPlaceholder = () => {
    return isCompanyEmployee ? "<EMAIL>" : "<EMAIL>";
  };

  const getEmailHelperText = () => {
    return isCompanyEmployee
      ? "Please use your company email address (not Gmail, Yahoo, etc.)"
      : "You can use any email service (Gmail, Yahoo, Outlook, etc.)";
  };





  const onSubmit = async (values: FormValues) => {
    setIsLoading(true);

    try {
      // Track signup attempt
      await trackUserAction('signup_attempt', {
        email: values.email,
        firstName: values.firstName,
        lastName: values.lastName,
        timestamp: new Date().toISOString(),
        step: 'form_submission'
      });

      // Extract company domain if user is a company employee
      let companyDomain = null;
      if (values.isCompany === 'true') {
        companyDomain = extractCompanyDomain(values.email);
      }

      const result = await signUp({
        email: values.email,
        password: values.password,
        options: {
          data: {
            firstName: values.firstName,
            lastName: values.lastName,
            linkedin_connected: false,
            isCompanyEmployee: values.isCompany === 'true',
            companyDomain: companyDomain
          },
        },
      });

      if (result?.error) {
        // Track signup failure
        await trackUserAction('signup_failed', {
          email: values.email,
          error: result.error.message,
          timestamp: new Date().toISOString()
        });
        ToastManager.error(result.error.message);
      } else {
        // Track successful signup
        await trackUserAction('signup_success', {
          email: values.email,
          timestamp: new Date().toISOString(),
          next_step: 'dashboard'
        });

        // Store company domain if user is a company employee
        if (companyDomain && result?.data?.user?.id) {
          try {
            await storeCompanyDomain(companyDomain, result.data.user.id);
            console.log('✅ Company domain stored successfully:', companyDomain);
          } catch (domainError) {
            console.error('❌ Failed to store company domain:', domainError);
            // Don't block the signup flow if domain storage fails
          }
        }

        // Set flag for login page to show verification reminder
        localStorage.setItem('show_verification_reminder', 'true');
        localStorage.setItem('verification_email', values.email);

        // Show signup success sequence with proper toast management
        ToastManager.auth.signupSuccess(values.email);

        // Redirect to dashboard - LinkedIn connection will be shown as popup
        navigate('/dashboard');
      }
    } catch (err) {
      console.error(err);

      await trackUserAction('signup_error', {
        email: values.email,
        error: 'Unknown error',
        timestamp: new Date().toISOString()
      });
      ToastManager.error("Something went wrong during signup");
    } finally {
      setIsLoading(false);
    }
  };

  // Track form field interactions
  const handleFieldFocus = (fieldName: string) => {
    trackUserAction('signup_field_focus', {
      field: fieldName,
      timestamp: new Date().toISOString()
    });
  };



  return (
    <div className="flex flex-col min-h-screen relative overflow-hidden">
      <PageBackground />
      <div className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-xl bg-opacity-95 backdrop-blur-sm border border-opacity-20">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-2 text-black">
              Create Your Account
            </h1>
            <p className="text-gray-700">
              Join CompanyVoice in just 30 seconds
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Name Fields */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black">First Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input
                            placeholder="John"
                            className="pl-10"
                            {...field}
                            onFocus={() => handleFieldFocus('firstName')}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black">Last Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input
                            placeholder="Doe"
                            className="pl-10"
                            {...field}
                            onFocus={() => handleFieldFocus('lastName')}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Email Field */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">Email Address</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          placeholder={getEmailPlaceholder()}
                          className="pl-10"
                          {...field}
                          onFocus={() => handleFieldFocus('email')}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                    {/* Helper text for email validation */}
                    <p className="text-xs text-gray-600 mt-1">
                      {getEmailHelperText()}
                    </p>
                  </FormItem>
                )}
              />

              {/* Password Fields */}
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          type="password"
                          placeholder="At least 8 characters"
                          className="pl-10"
                          {...field}
                          onFocus={() => handleFieldFocus('password')}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">Confirm Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          type="password"
                          placeholder="Confirm your password"
                          className="pl-10"
                          {...field}
                          onFocus={() => handleFieldFocus('confirmPassword')}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Company Employee Radio Buttons */}
              <FormField
                control={form.control}
                name="isCompany"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel className="text-black">Are you a company employee?</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="true" id="yes" />
                          <Label htmlFor="yes">Yes</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="false" id="no" />
                          <Label htmlFor="no">No</Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />



              <Button
                type="submit"
                disabled={isLoading}
                className="w-full rounded-xl py-4 px-8 text-lg font-semibold bg-gradient-to-r from-gray-800 to-black text-white hover:from-black hover:to-gray-900 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 transform disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Creating Account...
                  </div>
                ) : (
                  <>
                    <LogIn className="mr-2 h-5 w-5" />
                    Create Account
                  </>
                )}
              </Button>

              <div className="text-center text-gray-700">
                <p>Already have an account?</p>
                <Link
                  to="/login"
                  className="flex items-center justify-center text-black hover:text-black/90 mt-2 font-semibold"
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  Sign In
                </Link>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default QuickSignup;