
import React, { useState } from 'react';
import * as PhylloIntegration from '../integrations/phyllo';

interface LinkedInConnectProps {
  userId: string;
  onConnectionSuccess?: (accountId: string, workplatformId: string, userId: string) => void;
}

const LinkedInConnect: React.FC<LinkedInConnectProps> = ({ userId, onConnectionSuccess }) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleConnect = async () => {
    setIsConnecting(true);
    setError(null);

    try {
      const phyllo = new PhylloIntegration.createUser;

      await phyllo.connectLinkedIn({
        userId,
        onSuccess: (accountId, workplatformId, userId) => {
          setIsConnecting(false);
          onConnectionSuccess?.(accountId, workplatformId, userId);
        },
        onError: (error) => {
          setIsConnecting(false);
          setError(error.message || 'Failed to connect to LinkedIn');
        },
        onExit: () => {
          setIsConnecting(false);
        },
      });
    } catch (error) {
      setIsConnecting(false);
      setError(error instanceof Error ? error.message : 'Failed to connect to LinkedIn');
    }
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <button
        onClick={handleConnect}
        disabled={isConnecting}
        className={`px-4 py-2 rounded-md text-white font-medium ${
          isConnecting
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700'
        }`}
      >
        {isConnecting ? 'Connecting...' : 'Connect LinkedIn'}
      </button>

      {error && (
        <div className="text-red-500 text-sm mt-2">
          {error}
        </div>
      )}
    </div>
  );
};

export default LinkedInConnect;
