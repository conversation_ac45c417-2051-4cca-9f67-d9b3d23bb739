
import React from 'react';

const PageBackground = () => {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      <div className="fixed top-0 left-0 w-72 h-72 bg-yellow-300 rounded-full filter blur-3xl opacity-10 -translate-x-1/2 -translate-y-1/2"></div>
      <div className="fixed top-1/4 right-0 w-80 h-80 bg-orange-400 rounded-full filter blur-3xl opacity-10 translate-x-1/3 -translate-y-1/4"></div>
      <div className="fixed bottom-0 left-1/4 w-96 h-96 bg-blue-400 rounded-full filter blur-3xl opacity-10 -translate-x-1/2 translate-y-1/3"></div>
      <div className="fixed bottom-1/4 right-1/4 w-64 h-64 bg-teal-400 rounded-full filter blur-3xl opacity-10 translate-x-1/2 translate-y-1/4"></div>
      <div className="fixed top-1/2 left-1/2 w-72 h-72 bg-purple-400 rounded-full filter blur-3xl opacity-10 -translate-x-1/2 -translate-y-1/2"></div>
    </div>
  );
};

export default PageBackground;
