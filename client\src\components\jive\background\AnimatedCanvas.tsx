
import React, { useEffect, useRef, useState } from 'react';
import {
  <PERSON>lob, Squiggle, Emoji,
  initializeAreas, initializeSquiggles, initializeBlobs,
  drawSquiggles, drawBlobs,
} from './drawingUtils';

export const AnimatedCanvas = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize canvas with areas for elements
    const areas = initializeAreas(canvas.width, canvas.height);
    
    // Initialize elements
    const squiggles: Squiggle[] = initializeSquiggles(areas);
    const blobs: Blob[] = initializeBlobs(areas);

    let animationId: number;
    let time = 0;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Increase time
      time += 0.16;
      
      // Draw elements
      drawBlobs(ctx, blobs, time);
      drawSquiggles(ctx, squiggles, time);
      
      animationId = requestAnimationFrame(animate);
    };

    animate();

    // Cleanup
    return () => {
      cancelAnimationFrame(animationId);
      window.removeEventListener('resize', resizeCanvas);
    };
  }, []); // Only run once on mount

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 z-0 pointer-events-none"
      style={{ opacity: 0.7 }}
    />
  );
};

export default AnimatedCanvas;
