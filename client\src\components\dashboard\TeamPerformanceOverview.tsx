import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Bar<PERSON><PERSON>, TrendingUp, Users, FileText, Calendar } from "lucide-react";

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  trend?: string;
}

const StatCard = ({ title, value, icon, trend }: StatCardProps) => (
  <Card className="border-t-[1px] border-t-enterprise-blue">
    <CardContent className="p-2.5 flex justify-between items-center">
      <div>
        <p className="text-[11px] font-medium text-enterprise-gray-600 mb-0.5">{title}</p>
        <h3 className="text-base font-bold leading-tight">{value}</h3>
        {trend && (
          <p className="text-[10px] text-green-600 leading-tight mt-0.5">
            {trend}
          </p>
        )}
      </div>
      <div className="p-1.5 rounded-full bg-gray-50">
        {React.cloneElement(icon as React.ReactElement, { 
          className: "h-5 w-5",
          strokeWidth: 1.5 
        })}
      </div>
    </CardContent>
  </Card>
);

export const TeamPerformanceOverview = () => {
  return (
    <div className="grid grid-cols-3 gap-3 mb-6">
      <StatCard
        title="Total Team Impressions"
        value="256.8K"
        icon={<TrendingUp className="h-8 w-8 text-enterprise-blue" />}
        trend="+12.5% vs last month"
      />
      <StatCard
        title="Avg Impressions/User"
        value="18.3K"
        icon={<Users className="h-8 w-8 text-enterprise-teal" />}
        trend="****% vs last month"
      />
      <StatCard
        title="Avg Engagement Rate"
        value="4.8%"
        icon={<BarChart className="h-8 w-8 text-amber-500" />}
        trend="****% vs last month"
      />
      <StatCard
        title="Total Posts"
        value="342"
        icon={<FileText className="h-8 w-8 text-purple-500" />}
        trend="Last 30 days"
      />
      <StatCard
        title="Avg Posts/User/Week"
        value="3.2"
        icon={<Calendar className="h-8 w-8 text-green-500" />}
        trend="Target: 4 posts"
      />
    </div>
  );
};
