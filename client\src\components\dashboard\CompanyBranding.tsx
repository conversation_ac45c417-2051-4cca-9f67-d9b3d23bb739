
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Check, Info, Loader2 } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Json } from '@/integrations/supabase/types';

interface LinkedInPostExample {
  id: string;
  subject: string;
  content: string;
}

interface ColorConfig {
  name: string;
  value: string;
  label: string;
}

interface BrandingData {
  core_values: string[];
  dos_content: string;
  donts_content: string;
  hashtags: string;
  brand_colors?: Json;
  linkedin_examples?: Json;
}

const CompanyBranding = () => {
  const { profile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [branding, setBranding] = useState<BrandingData | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBranding = async () => {
      try {
        setLoading(true);
        console.log("Fetching branding data in dashboard component");
        
        if (!profile?.id) {
          console.log("No profile ID available in dashboard component");
          setLoading(false);
          return;
        }
        
        // Try to fetch company branding data
        const { data, error } = await supabase
          .from('company_branding')
          .select('*')
          .eq('company_id', profile.id)
          .maybeSingle();
          
        if (error) {
          console.error('Error fetching branding:', error);
          setError(`Error fetching data: ${error.message}`);
          return;
        }
        
        console.log("Dashboard branding data retrieved:", data);
        
        if (data) {
          setBranding(data);
        } else {
          console.log("No branding data found for this company");
        }
      } catch (err) {
        console.error('Exception fetching branding:', err);
        setError(`Exception: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setLoading(false);
      }
    };
    
    fetchBranding();
  }, [profile?.id]);

  if (loading) {
    return (
      <Card className="w-full backdrop-blur-sm bg-white/70 border border-slate-200/50 shadow-lg">
        <CardContent className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-enterprise-blue" />
          <span className="ml-2">Loading company brand profile...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full backdrop-blur-sm bg-white/70 border border-slate-200/50 shadow-lg">
        <CardHeader>
          <CardTitle>Error Loading Company Voice Profile</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">{error}</p>
          <p className="mt-4">
            Please try refreshing the page or contact support if the problem persists.
          </p>
        </CardContent>
      </Card>
    );
  }

  // If no branding data exists yet
  if (!branding) {
    return (
      <Card className="w-full backdrop-blur-sm bg-white/70 border border-slate-200/50 shadow-lg">
        <CardHeader>
          <CardTitle>Company Voice Profile</CardTitle>
          <CardDescription>
            No branding profile has been set up yet
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center py-8 text-muted-foreground">
            Go to Settings → Company Branding to set up your company's brand voice profile.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Parse hashtags from string to array
  const hashtagsArray = branding?.hashtags
    ? branding.hashtags.split(',').map(tag => tag.trim())
    : ['#CompanyName', '#Innovation', '#CustomerSuccess', '#TeamWork', '#IndustryLeader', '#ProductName'];

  // Parse dos and donts from string to array
  const dosArray = branding?.dos_content
    ? branding.dos_content.split('.').filter(item => item.trim().length > 0)
    : ['Use data and statistics to support claims', 'Emphasize customer benefits over features', 'Keep content concise and focused on one main point', 'Use industry-specific terminology appropriately'];

  const dontsArray = branding?.donts_content
    ? branding.donts_content.split('.').filter(item => item.trim().length > 0)
    : ['Avoid criticizing competitors by name or making claims that cannot be verified'];

  return (
    <Card className="w-full backdrop-blur-sm bg-white/70 border border-slate-200/50 shadow-lg">
      <CardHeader>
        <CardTitle>Company Voice Profile</CardTitle>
        <CardDescription>
          These brand guidelines help ensure content matches your company's voice
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-sm font-medium flex items-center">
            <Info size={16} className="text-enterprise-blue mr-1" />
            Core Values
          </h3>
          <div className="flex flex-wrap gap-2">
            {branding.core_values && branding.core_values.length > 0 ? (
              branding.core_values.map((value, index) => (
                <Badge key={index} variant="secondary" className="bg-enterprise-lightBlue text-enterprise-blue">
                  {value}
                </Badge>
              ))
            ) : (
              <>
                <Badge variant="secondary" className="bg-enterprise-lightBlue text-enterprise-blue">
                  Innovative
                </Badge>
                <Badge variant="secondary" className="bg-enterprise-lightBlue text-enterprise-blue">
                  Trustworthy
                </Badge>
                <Badge variant="secondary" className="bg-enterprise-lightBlue text-enterprise-blue">
                  Professional
                </Badge>
                <Badge variant="secondary" className="bg-enterprise-lightBlue text-enterprise-blue">
                  Customer-focused
                </Badge>
                <Badge variant="secondary" className="bg-enterprise-lightBlue text-enterprise-blue">
                  Expert
                </Badge>
              </>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium flex items-center">
            <Info size={16} className="text-enterprise-blue mr-1" />
            Content guidelines
          </h3>
          <ul className="space-y-2">
            {dosArray.map((item, index) => (
              <li key={`do-${index}`} className="flex items-center">
                <Check size={16} className="text-green-500 mr-2 flex-shrink-0" />
                <span className="text-sm">{item}</span>
              </li>
            ))}
            
            {dontsArray.map((item, index) => (
              <li key={`dont-${index}`} className="flex items-start">
                <AlertCircle size={16} className="text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <span className="text-sm">{item}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium">Approved hashtags</h3>
          <div className="flex flex-wrap gap-2">
            {hashtagsArray.map((tag, index) => (
              <Badge key={index}>{tag}</Badge>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Brand voice adherence</h3>
            <span className="text-sm font-medium text-enterprise-blue">92%</span>
          </div>
          <Progress value={92} className="h-2" />
          <p className="text-xs text-muted-foreground">
            Based on AI analysis of your team's recent content
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default CompanyBranding;
