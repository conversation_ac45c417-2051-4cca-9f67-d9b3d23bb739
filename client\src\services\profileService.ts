import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface LinkedInProfileData {
  name?: string;
  id?: string;
  connection_params?: {
    im?: {
      id?: string;
      publicIdentifier?: string;
      username?: string;
    };
  };
}

export interface ProfileLinkedInData {
  linkedin_email: string;
  account_id: string;
  linkedin_user_id?: string;
  linkedin_url?: string;
  linkedin_connected_at: string;
  linkedin_last_sync: string;
  linkedin_connection_status: 'connected' | 'reconnecting' | 'failed';
  linkedin_profile_data: LinkedInProfileData;
  linkedin_reconnection_count?: number;
  linkedin_last_reconnection?: string;
}

class ProfileService {
  /**
   * Save LinkedIn data directly to profiles table
   * This replaces all temporary storage with direct database updates
   */
  async saveLinkedInDataToProfile(
    userEmail: string,
    accountId: string,
    linkedInProfile?: LinkedInProfileData,
    isReconnection: boolean = false
  ): Promise<boolean> {
    try {
      console.log('🎯 ===== SAVING LINKEDIN DATA TO PROFILES TABLE =====');
      console.log('📥 Input:', { userEmail, accountId, isReconnection });

      // Extract LinkedIn user data
      const linkedinUserId = linkedInProfile?.connection_params?.im?.id || 
                            linkedInProfile?.connection_params?.im?.publicIdentifier || 
                            linkedInProfile?.id;
      
      const publicIdentifier = linkedInProfile?.connection_params?.im?.publicIdentifier;
      
      // Find existing user by email
      console.log('🔍 Looking up user in profiles table...');
      const { data: existingUser, error: findError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', userEmail)
        .single();

      if (findError && findError.code !== 'PGRST116') {
        console.error('❌ Error finding user:', findError);
        throw findError;
      }

      if (!existingUser) {
        console.error('❌ User not found in profiles table:', userEmail);
        throw new Error('User profile not found');
      }

      console.log('✅ Found user profile:', existingUser.id);

      // Prepare LinkedIn data for update
      const now = new Date().toISOString();

      // Extract first and last name from LinkedIn profile
      const extractNamesFromProfile = (linkedInProfile: any) => {
        let firstName = '';
        let lastName = '';

        if (linkedInProfile?.name) {
          // Split full name into first and last name
          const nameParts = linkedInProfile.name.trim().split(' ');
          firstName = nameParts[0] || '';
          lastName = nameParts.slice(1).join(' ') || '';
        }

        // Also check if names are available in connection_params
        if (linkedInProfile?.connection_params?.im?.firstName) {
          firstName = linkedInProfile.connection_params.im.firstName;
        }
        if (linkedInProfile?.connection_params?.im?.lastName) {
          lastName = linkedInProfile.connection_params.im.lastName;
        }

        console.log('📝 Extracted names from LinkedIn profile:', {
          fullName: linkedInProfile?.name,
          firstName,
          lastName
        });

        return { firstName, lastName };
      };

      const { firstName, lastName } = extractNamesFromProfile(linkedInProfile);

      let updateData: any;
      let logMessage: string;

      if (isReconnection) {
        // For reconnection: only update non-unique fields to avoid constraint violations
        console.log('🔄 Processing reconnection - updating non-unique fields only');

        // Validate that the reconnection data matches existing data
        if (existingUser.linkedin_email !== userEmail || existingUser.account_id !== accountId) {
          const errorMsg = `Reconnection validation failed. Expected email: ${existingUser.linkedin_email}, account_id: ${existingUser.account_id}`;
          console.error('❌ Reconnection validation failed:', errorMsg);
          throw new Error(errorMsg);
        }

        updateData = {
          first_name: firstName || undefined, // Update name if available
          last_name: lastName || undefined,   // Update name if available
          linkedin_url: publicIdentifier
            ? `https://linkedin.com/in/${publicIdentifier}`
            : null,
          linkedin_last_sync: now,
          linkedin_connection_status: 'connected',
          linkedin_profile_data: linkedInProfile || {},
          linkedin_reconnection_count: (existingUser.linkedin_reconnection_count || 0) + 1,
          linkedin_last_reconnection: now,
          updated_at: now
          // Note: NOT updating account_id, linkedin_email, linkedin_user_id to avoid unique constraint violations
        };

        logMessage = `🔄 Processing reconnection #${updateData.linkedin_reconnection_count}`;
      } else {
        // For first-time connection: update all fields including unique ones
        console.log('🆕 Processing first-time connection - updating all fields');

        updateData = {
          first_name: firstName || undefined, // Save name from LinkedIn
          last_name: lastName || undefined,   // Save name from LinkedIn
          linkedin_email: userEmail,
          account_id: accountId,
          linkedin_user_id: linkedinUserId,
          linkedin_url: publicIdentifier
            ? `https://linkedin.com/in/${publicIdentifier}`
            : null,
          linkedin_last_sync: now,
          linkedin_connection_status: 'connected',
          linkedin_profile_data: linkedInProfile || {},
          linkedin_connected_at: now,
          linkedin_reconnection_count: 0,
          updated_at: now
        };

        logMessage = '🆕 Processing first-time connection';
      }

      console.log(logMessage);
      console.log('📦 Prepared LinkedIn data:', updateData);

      // Update the profiles table
      console.log('💾 Updating profiles table...');
      const { data: updatedUser, error: updateError } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', existingUser.id)
        .select()
        .single();

      if (updateError) {
        console.error('❌ Error updating profiles table:', updateError);

        // Handle unique constraint violations for first-time connections
        if (updateError.code === '23505' && !isReconnection) {
          let errorMessage = 'This LinkedIn account is already connected to another user.';

          if (updateError.message.includes('account_id')) {
            errorMessage = 'This LinkedIn account is already connected to another user. Please use a different LinkedIn account.';
          } else if (updateError.message.includes('linkedin_email')) {
            errorMessage = 'This LinkedIn email is already connected to another user. Please use a different LinkedIn account.';
          } else if (updateError.message.includes('linkedin_user_id')) {
            errorMessage = 'This LinkedIn user ID is already connected to another user. Please use a different LinkedIn account.';
          }

          throw new Error(errorMessage);
        }

        throw updateError;
      }

      console.log('✅ Profiles table updated successfully!');
      console.log('🎉 Updated profile data:', updatedUser);

      // Verify the update
      await this.verifyLinkedInDataSaved(userEmail, accountId);

      console.log('🎯 ===== LINKEDIN DATA SAVED TO PROFILES TABLE SUCCESSFULLY =====');
      return true;

    } catch (error: any) {
      console.error('💥 ===== FAILED TO SAVE LINKEDIN DATA TO PROFILES TABLE =====');
      console.error('❌ Error details:', {
        message: error.message,
        code: error.code,
        details: error.details
      });
      return false;
    }
  }

  /**
   * Check if user has LinkedIn connected
   */
  async isLinkedInConnected(userEmail: string): Promise<boolean> {
    try {
      const { data: user, error } = await supabase
        .from('profiles')
        .select('account_id, linkedin_connection_status')
        .eq('email', userEmail)
        .single();

      if (error || !user) {
        return false;
      }

      return !!(user.account_id && user.linkedin_connection_status === 'connected');

    } catch (error: any) {
      console.error('❌ Error checking LinkedIn connection:', error);
      return false;
    }
  }

  /**
   * Get user's LinkedIn data from profiles table
   */
  async getLinkedInData(userEmail: string): Promise<ProfileLinkedInData | null> {
    try {
      const { data: user, error } = await supabase
        .from('profiles')
        .select(`
          linkedin_email,
          account_id,
          linkedin_user_id,
          linkedin_url,
          linkedin_connected_at,
          linkedin_last_sync,
          linkedin_connection_status,
          linkedin_profile_data,
          linkedin_reconnection_count,
          linkedin_last_reconnection
        `)
        .eq('email', userEmail)
        .single();

      if (error || !user) {
        console.log('ℹ️ No LinkedIn data found for user:', userEmail);
        return null;
      }

      return user as ProfileLinkedInData;

    } catch (error: any) {
      console.error('❌ Error fetching LinkedIn data:', error);
      return null;
    }
  }

  /**
   * Update LinkedIn connection status
   */
  async updateLinkedInStatus(
    userEmail: string,
    status: 'connected' | 'disconnected' | 'reconnecting' | 'failed' | 'expired'
  ): Promise<boolean> {
    try {
      console.log('🔄 Updating LinkedIn status:', { userEmail, status });

      const { data, error } = await supabase
        .from('profiles')
        .update({
          linkedin_connection_status: status,
          linkedin_last_sync: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('email', userEmail)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating LinkedIn status:', error);
        return false;
      }

      console.log('✅ LinkedIn status updated:', data);
      return true;

    } catch (error: any) {
      console.error('❌ Error updating LinkedIn status:', error);
      return false;
    }
  }

  /**
   * Disconnect LinkedIn account
   */
  async disconnectLinkedIn(userEmail: string): Promise<boolean> {
    try {
      console.log('🔌 Disconnecting LinkedIn for user:', userEmail);

      const { data, error } = await supabase
        .from('profiles')
        .update({
          linkedin_connection_status: 'disconnected',
          linkedin_last_sync: new Date().toISOString(),
          updated_at: new Date().toISOString()
          // Keep historical data but mark as disconnected
        })
        .eq('email', userEmail)
        .select()
        .single();

      if (error) {
        console.error('❌ Error disconnecting LinkedIn:', error);
        return false;
      }

      console.log('✅ LinkedIn disconnected successfully');
      return true;

    } catch (error: any) {
      console.error('❌ Error disconnecting LinkedIn:', error);
      return false;
    }
  }

  /**
   * Verify LinkedIn data was saved correctly
   */
  private async verifyLinkedInDataSaved(userEmail: string, accountId: string): Promise<void> {
    try {
      console.log('🔍 Verifying LinkedIn data was saved...');

      const savedData = await this.getLinkedInData(userEmail);

      if (!savedData) {
        console.error('❌ Verification failed - no data found');
        return;
      }

      console.log('✅ Verification successful!');
      console.log('📊 Saved LinkedIn data:', savedData);

      // Verify account_id matches
      if (savedData.account_id === accountId) {
        console.log('✅ Account ID verification passed');
      } else {
        console.error('❌ Account ID mismatch');
      }

    } catch (error: any) {
      console.error('❌ Verification error:', error);
    }
  }
}

export const profileService = new ProfileService();
