import express from 'express';
import cors from 'cors';
import authRoutes from './routes/auth';
import analyticsRoutes from './routes/analytics';
// New functional routes
import usersRoutes from './routes/users';
import linkedinRoutes from './routes/linkedin';
import linkedinSearchRoutes from './routes/linkedinSearch.route';
import analyticsNewRoutes from './routes/analytics-new';
import authNewRoutes from './routes/auth-new';
import analyticsLegacyRoutes from './routes/analytics-legacy';
import postSyncRoutes from './routes/postSync.routes'; // ADD THIS LINE
import { errorHandler, notFoundHandler } from './middleware/validation';
import { backgroundScheduler } from './services/backgroundScheduler';

const app = express();
const PORT = process.env.PORT || 3000;

// Enable Strict CORS for frontend origin
// app.use(cors({
//     origin: [`${process.env.CLIENT_URL}`, 'https://localhost:8080'],
//     credentials: true,
//     methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
//     allowedHeaders: ['Content-Type', 'Authorization']
// }));

app.use(cors())

// Middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes 
app.use('/auth', authRoutes);
app.use('/analytics', analyticsRoutes);

// New functional API routes
app.use('/api/users', usersRoutes);
app.use('/api/linkedin', linkedinRoutes);
app.use('/api/linkedinsearch', linkedinSearchRoutes);
app.use('/api/analytics', analyticsNewRoutes);
app.use('/api/posts', postSyncRoutes);

// Alternative functional routes (for testing)
app.use('/api/auth', authNewRoutes);
app.use('/api/analytics-legacy', analyticsLegacyRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Server is running',
        timestamp: new Date().toISOString()
    });
});

// Supabase connection test endpoint
app.get('/test-supabase', async (req, res) => {
    try {
        const { getUserById } = await import('./services/user.service');
        const testUserId = 'e21336b8-4b20-4b05-8dc5-ece434000068';

        console.log('🧪 Testing Supabase connection via getUserById...');
        const user = await getUserById(testUserId);

        res.status(200).json({
            success: true,
            message: 'Supabase connection test completed',
            user: user ? {
                id: user.id,
                email: user.email,
                account_id: user.account_id,
                linkedin_connection_status: user.linkedin_connection_status
            } : null,
            timestamp: new Date().toISOString()
        });
    } catch (error: any) {
        console.error('❌ Supabase test failed:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

app.listen(PORT, () => {
    console.log(`🚀 Server is running on port ${PORT}`);
    console.log(`\n📊 Legacy APIs (now using functional handlers):`);
    console.log(`🔐 Auth API: http://localhost:${PORT}/auth`);
    console.log(`� Analytics API: http://localhost:${PORT}/analytics`);
    console.log(`\n🎯 New Functional APIs:`);
    console.log(`👤 Users API: http://localhost:${PORT}/api/users`);
    console.log(`🔗 LinkedIn API: http://localhost:${PORT}/api/linkedin`);
    console.log(`📈 Analytics API: http://localhost:${PORT}/api/analytics`);
    console.log(`\n🧪 Alternative Functional APIs:`);
    console.log(`🔐 Auth API (New): http://localhost:${PORT}/api/auth`);
    console.log(`📊 Analytics API (Legacy): http://localhost:${PORT}/api/analytics-legacy`);
    console.log(`📝 Posts Sync API: http://localhost:${PORT}/api/posts`);

    // Start background scheduler for soft refresh
    if (process.env.NODE_ENV !== 'test') {
        console.log('\n🔄 Starting background scheduler for LinkedIn post sync...');
        backgroundScheduler.start();
    }
});
