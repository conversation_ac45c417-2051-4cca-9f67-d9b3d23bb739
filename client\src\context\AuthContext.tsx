// Update coded
import React, { createContext, useContext, useEffect, ReactNode } from "react";
import { supabase } from "@/integrations/supabase/client";
import { AuthContextType, Profile } from "@/types/auth";
import { useAuthState } from "@/hooks/useAuthState";
import { useUserProfile } from "@/hooks/useUserProfile";
import { SessionManager } from "@/utils/sessionManager";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const {
    session,
    setSession,
    user,
    setUser,
    isLoading,
    setIsLoading,
    signUp,
    signIn,
    signOut,
  } = useAuthState();

  const { profile, setProfile, fetchUserProfile } = useUserProfile();

  useEffect(() => {
    const path = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);

    // Don't clear localStorage during LinkedIn OAuth flows
    const isLinkedInOAuth = path.includes('/auth/') ||
                           path.includes('/callback') ||
                           searchParams.has('accountId') ||
                           searchParams.has('userId') ||
                           window.location.href.includes('unipile');

    if ((path === "/login" || path === "/signup") && !isLinkedInOAuth) {
      if (!user && !isLoading) {
        localStorage.removeItem("supabase.auth.token");
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith("supabase.")) {
            localStorage.removeItem(key);
          }
        }
      }
    }

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, newSession) => {
      console.log('🔐 Auth state change:', event, newSession?.user?.id);

      setSession(newSession);
      setUser(newSession?.user ?? null);

      if (newSession?.user) {
        // Track login session for soft call control
        if (event === 'SIGNED_IN') {
          console.log('✅ User signed in, marking session');
          SessionManager.markUserLoggedIn(newSession.user.id);
        }

        setTimeout(() => {
          fetchUserProfile(newSession.user.id);
        }, 0);
      } else {
        // Clear session on logout
        if (event === 'SIGNED_OUT') {
          console.log('👋 User signed out, clearing session');
          SessionManager.clearSession();
        }
        setProfile(null);
      }
    });

    supabase.auth.getSession().then(({ data: { session: currentSession } }) => {
      setSession(currentSession);
      setUser(currentSession?.user ?? null);

      if (currentSession?.user) {
        fetchUserProfile(currentSession.user.id);
      }
      setIsLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const isAdmin = () => {
    return (profile as Profile)?.role === "admin";
  };

  const isUser = () => {
    return (profile as Profile)?.role === "user";
  };

  const value: AuthContextType = {
    session,
    user,
    profile: profile as Profile | null,
    isLoading,
    signUp,
    signIn,
    signOut,
    isAdmin,
    isUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
