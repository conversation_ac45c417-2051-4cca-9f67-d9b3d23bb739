import React from 'react';
import { Send, BarChart3 } from 'lucide-react';
const JiveAppPreview = () => {
  return <section className="relative max-w-5xl mx-auto px-4 my-12">
      <div className="flex justify-center">
        <div className="relative py-[20px]">
          {/* Content Creator Phone */}
          <div className="transform -rotate-6 shadow-2xl rounded-3xl border-8 border-black overflow-hidden w-64 md:w-72 z-10 relative">
            <div className="bg-blue-100 aspect-[9/16] flex items-center justify-center">
              <div className="w-4/5 mx-auto text-center">
                <div className="bg-white p-4 rounded-xl shadow-md mb-4">
                  <div className="w-full mx-auto mb-3">
                    <div className="h-8 bg-gray-100 rounded-md mb-2"></div>
                    <div className="h-24 bg-gray-50 rounded-md mb-2 p-2 text-xs text-left text-gray-500">
                      Create content that aligns with our company voice and values...
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs">Post template</span>
                      <Send size={16} className="text-blue-500" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Analytics Phone */}
          <div className="absolute top-8 -right-20 md:-right-24 transform rotate-6 shadow-2xl rounded-3xl border-8 border-black overflow-hidden w-64 md:w-72 z-20">
            <div className="bg-white aspect-[9/16] flex flex-col">
              <div className="bg-gray-100 p-4">
                <h3 className="font-bold text-lg">Post Analytics</h3>
              </div>
              <div className="p-4 flex-grow">
                <div className="h-8 w-full bg-gray-50 rounded-md mb-4"></div>
                <div className="flex items-center justify-between mb-3">
                  <div className="font-medium">Engagement Rate</div>
                  <div className="text-green-500 font-bold">+24%</div>
                </div>
                <div className="h-32 bg-gray-100 rounded-md mb-4 flex items-end justify-around p-2">
                  <div className="h-1/2 w-8 bg-blue-400 rounded-t"></div>
                  <div className="h-3/4 w-8 bg-blue-500 rounded-t"></div>
                  <div className="h-1/4 w-8 bg-blue-300 rounded-t"></div>
                  <div className="h-2/3 w-8 bg-blue-600 rounded-t"></div>
                  <div className="h-1/3 w-8 bg-blue-400 rounded-t"></div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">View detailed metrics</span>
                  <BarChart3 size={18} className="text-blue-500" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>;
};
export default JiveAppPreview;