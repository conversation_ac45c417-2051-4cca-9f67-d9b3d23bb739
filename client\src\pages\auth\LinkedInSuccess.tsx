import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CheckCircle, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

const LinkedInSuccess: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Show success message
    toast.success('LinkedIn verification completed successfully!');
    
    // Auto-redirect after 3 seconds
    const timer = setTimeout(() => {
      navigate('/signup');
    }, 3000);

    return () => clearTimeout(timer);
  }, [navigate]);

  const handleReturnToSignup = () => {
    navigate('/signup');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-enterprise-blue/5 to-enterprise-lightBlue/10">
      <div className="max-w-md w-full mx-4">
        <div className="content-card text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          
          <h1 className="text-2xl font-bold text-foreground mb-4">
            LinkedIn Verification Successful!
          </h1>
          
          <p className="text-muted-foreground mb-6">
            Your LinkedIn account has been successfully verified. You can now continue with your signup process.
          </p>
          
          <div className="space-y-3">
            <Button
              onClick={handleReturnToSignup}
              className="w-full button-primary"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Return to Signup
            </Button>
            
            <p className="text-xs text-muted-foreground">
              You will be automatically redirected in a few seconds...
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinkedInSuccess;
