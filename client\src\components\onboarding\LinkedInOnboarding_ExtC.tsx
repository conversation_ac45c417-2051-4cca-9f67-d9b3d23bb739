// Extra Component

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { LinkIcon, CheckCircle, ArrowRight, BarChart3, Users, TrendingUp, SkipForwardIcon, Mail, Check, Component } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import PageBackground from '@/components/layout/PageBackground';
import { UnipileHostedAuth } from '@/components/auth/UnipileHostedAuth';
import { useAuthState } from '@/hooks/useAuthState';
import { analyticsService } from '@/services/analytics';


const LinkedInOnboarding: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthState();
  const [showLinkedInAuth, setShowLinkedInAuth] = useState(false);
  const [isLinkedInConnected, setIsLinkedInConnected] = useState(false);
  const [linkedInAccountId, setLinkedInAccountId] = useState<string | null>(null);
  const [onboardingStep, setOnboardingStep] = useState(1);

  // Use user email directly (no temporary storage)
  const persistedEmail = user?.email || '';

  // New state for enhanced email handling
  const [useRegistrationEmail, setUseRegistrationEmail] = useState(true);
  const [linkedinEmail, setLinkedinEmail] = useState(persistedEmail);
  const [isEmailValid, setIsEmailValid] = useState(!!persistedEmail);
  const [isCheckingAccount, setIsCheckingAccount] = useState(false);
  const [registrationEmailDisplay, setRegistrationEmailDisplay] = useState(persistedEmail);

  useEffect(() => {
    // Track onboarding started
    analyticsService.trackLinkedInOnboardingStarted();

    // Set user ID for analytics
    if (user?.id) {
      analyticsService.setUserId(user.id);
    }

    // Initialize with user email (no temporary storage)
    console.log('📦 Using user email directly:', persistedEmail);

    if (persistedEmail) {
      setLinkedinEmail(persistedEmail);
      setRegistrationEmailDisplay(persistedEmail);
      validateEmail(persistedEmail);
      console.log('✅ Using user email:', persistedEmail);
    }
  }, [user, persistedEmail]);

  // Email validation
  const validateEmail = (email: string) => {
    const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    setIsEmailValid(isValid);
    return isValid;
  };

  // Handle email input change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const email = e.target.value;
    setLinkedinEmail(email);
    validateEmail(email);
  };

  // Handle checkbox change for using registration email
  const handleUseRegistrationEmailChange = (checked: boolean) => {
    setUseRegistrationEmail(checked);

    if (checked) {
      // Use persisted registration email
      const emailToUse = persistedEmail || user?.email || '';
      setLinkedinEmail(emailToUse);
      validateEmail(emailToUse);

      // No temporary storage needed - data saved directly to profiles table
      console.log('✅ Using registration email:', emailToUse);
    } else {
      // Clear for manual input
      setLinkedinEmail('');
      setIsEmailValid(false);
      console.log('📝 Switched to manual email input');
    }
  };

  const handleConnectLinkedIn = async () => {
    const emailToUse = useRegistrationEmail ? (persistedEmail || user?.email) : linkedinEmail;

    if (!emailToUse || !emailToUse.trim()) {
      toast.error('Please enter your LinkedIn email address first');
      return;
    }

    if (!isEmailValid) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsCheckingAccount(true);

    try {
      // Track LinkedIn verification attempt
      await analyticsService.trackLinkedInVerificationAttempt(emailToUse);

      console.log('🔍 Checking for existing account before verification...');

      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/auth/get-account-by-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: emailToUse }),
      });

      const data = await response.json();

      if (response.ok && data.success && data.account_id) {
        // Account already exists and is verified!
        console.log('✅ Found existing verified account:', data.account_id);

        // Show success message for auto-verification
        const message = data.shouldReconnect
          ? 'You already have an account! Account automatically verified for reconnection'
          : 'Account found and automatically verified!';

        toast.success(message);

        // Automatically set as verified without showing auth flow
        handleLinkedInSuccess(data.account_id, data.shouldReconnect || true);
        return;
      }
    } catch (error) {
      console.log('⚠️ Error checking existing account:', error);
      // Continue with normal verification flow
    }

    setIsCheckingAccount(false);

    // No existing account found, proceed with normal verification
    // Skip the intermediate step and directly open verification popup
    setShowLinkedInAuth(true);
  };

  const handleLinkedInSuccess = async (accountId: string, isReconnection?: boolean) => {
    console.log('✅ LinkedIn connected successfully:', accountId);

    const emailUsed = useRegistrationEmail ? (persistedEmail || user?.email) : linkedinEmail;

    setLinkedInAccountId(accountId);
    setIsLinkedInConnected(true);
    setShowLinkedInAuth(false);
    setOnboardingStep(2);

    // LinkedIn data will be saved directly to profiles table via backend
    console.log('✅ LinkedIn verification completed - data saved to profiles table');

    // Track successful LinkedIn connection
    if (emailUsed) {
      await analyticsService.trackLinkedInVerificationSuccess(emailUsed, accountId);
    }

    const message = isReconnection
      ? 'LinkedIn account reconnected successfully! 🎉'
      : 'LinkedIn account connected successfully! 🎉';

    toast.success(message);

    // Auto-advance to completion after a short delay
    setTimeout(() => {
      handleCompleteOnboarding();
    }, 2000);
  };

  const handleLinkedInError = (error: string) => {
    console.error('❌ LinkedIn connection failed:', error);
    toast.error(`LinkedIn connection failed: ${error}`);
    setShowLinkedInAuth(false);
  };

  const handleSkipLinkedIn = async () => {
    // Track LinkedIn skipped
    await analyticsService.trackLinkedInSkipped();
    
    toast.info('LinkedIn connection skipped. You can connect later from your dashboard.');
    navigate('/dashboard');
  };

  const handleCompleteOnboarding = async () => {
    // Track onboarding completion
    await analyticsService.trackOnboardingCompleted();
    
    toast.success('Welcome to CompanyVoice! 🚀');
    navigate('/dashboard');
  };

  const features = [
    {
      icon: <BarChart3 className="h-6 w-6 text-blue-600" />,
      title: "LinkedIn Analytics",
      description: "Track your post performance, engagement rates, and audience growth"
    },
    {
      icon: <Users className="h-6 w-6 text-green-600" />,
      title: "Audience Insights",
      description: "Understand your followers and optimize content for better reach"
    },
    {
      icon: <TrendingUp className="h-6 w-6 text-purple-600" />,
      title: "Growth Tracking",
      description: "Monitor your LinkedIn presence and identify growth opportunities"
    }
  ];

  if (showLinkedInAuth) {
    return (
      <div className="flex flex-col min-h-screen relative overflow-hidden">
        <PageBackground />
        <div className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="w-full max-w-lg p-8 space-y-8 bg-white rounded-lg shadow-xl bg-opacity-95 backdrop-blur-sm border border-opacity-20">
            <div className="text-center">
              <h1 className="text-3xl font-bold mb-2 text-black">
                Connect Your LinkedIn
              </h1>
              <p className="text-gray-700">
                Securely connect your LinkedIn account to unlock analytics
              </p>
            </div>

            <UnipileHostedAuth
              userId={useRegistrationEmail ? (user?.email || '') : linkedinEmail}
              onSuccess={(account) => {
                const isReconnection = account.status === 'RECONNECTED';
                handleLinkedInSuccess(account.account_id, isReconnection);
              }}
              onError={handleLinkedInError}
              providers={['LINKEDIN']}
            />

            <div className="text-center">
              <Button
                onClick={() => setShowLinkedInAuth(false)}
                variant="outline"
                className="w-full rounded-xl py-3 px-6 font-semibold border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300"
              >
                ← Back to Onboarding
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen relative overflow-hidden">
      <PageBackground />
      <div className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="w-full max-w-2xl space-y-8">
          {/* Progress Header */}
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4 text-black">
              Welcome to CompanyVoice! 🎉
            </h1>
            <p className="text-xl text-gray-700 mb-6">
              Let's connect your LinkedIn to unlock powerful analytics
            </p>
            <div className="w-full max-w-md mx-auto">
              <Progress value={onboardingStep * 50} className="h-3" />
              <p className="text-sm text-gray-600 mt-2">
                Step {onboardingStep} of 2
              </p>
            </div>
          </div>

          {/* Main Content */}
          <div className="bg-white rounded-xl shadow-xl bg-opacity-95 backdrop-blur-sm border border-opacity-20 overflow-hidden">
            {onboardingStep === 1 && (
              <div className="p-8">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <LinkIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-black mb-2">
                    Connect Your LinkedIn Account
                  </h2>
                  <p className="text-gray-600">
                    Unlock powerful analytics and insights for your LinkedIn presence
                  </p>
                </div>

                {/* Email Configuration Section */}
                <div className="mb-8 p-6 bg-gray-50 rounded-xl border border-gray-200">
                  <div className="space-y-4">
                    {/* Checkbox for using registration email */}
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="use-registration-email"
                        checked={useRegistrationEmail}
                        onCheckedChange={handleUseRegistrationEmailChange}
                        className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                      />
                      <Label
                        htmlFor="use-registration-email"
                        className="text-sm font-medium text-gray-700 cursor-pointer"
                      >
                        My registration email is my LinkedIn email ({registrationEmailDisplay || 'No email found'})
                      </Label>
                    </div>

                    {/* LinkedIn Email Input - Only show if not using registration email */}
                    {!useRegistrationEmail && (
                      <div className="space-y-2">
                        <Label htmlFor="linkedin-email" className="text-sm font-medium text-gray-700">
                          LinkedIn Email Address
                        </Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input
                            id="linkedin-email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={linkedinEmail}
                            onChange={handleEmailChange}
                            className={`pl-10 ${!isEmailValid && linkedinEmail ? 'border-red-500 focus:border-red-500' : ''}`}
                          />
                        </div>
                        {!isEmailValid && linkedinEmail && (
                          <p className="text-sm text-red-500">Please enter a valid email address</p>
                        )}
                      </div>
                    )}

                    {/* Email confirmation display when using registration email */}
                    {useRegistrationEmail && (
                      <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <Check className="w-5 h-5 text-white" />
                          </div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-blue-800">Using Registration Email</p>
                          <p className="text-sm text-blue-600">{registrationEmailDisplay}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  {features.map((feature, index) => (
                    <Card key={index} className="border-2 border-gray-100 hover:border-blue-200 transition-colors">
                      <CardHeader className="text-center pb-2">
                        <div className="flex justify-center mb-2">
                          {feature.icon}
                        </div>
                        <CardTitle className="text-lg">{feature.title}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="text-center">
                          {feature.description}
                        </CardDescription>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="space-y-4">
                  {/* Main Connect Button - Only show when email is valid */}
                  {(useRegistrationEmail || (!useRegistrationEmail && isEmailValid && linkedinEmail)) && (
                    <Button
                      onClick={handleConnectLinkedIn}
                      disabled={isCheckingAccount}
                      className={`w-full rounded-xl py-4 px-8 text-lg font-semibold transition-all duration-300 transform ${
                        useRegistrationEmail || (isEmailValid && linkedinEmail)
                          ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl hover:scale-105 ring-2 ring-blue-300 ring-opacity-50'
                          : 'bg-gray-400 cursor-not-allowed'
                      } disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none`}
                    >
                      {isCheckingAccount ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          Checking Account...
                        </div>
                      ) : (
                        <>
                          <LinkIcon className="mr-2 h-5 w-5" />
                          Connect LinkedIn Account
                        </>
                      )}
                    </Button>
                  )}

                  {/* Helper text for email validation */}
                  <div className="text-center">
                    {!useRegistrationEmail && !linkedinEmail ? (
                      <p className="text-sm text-gray-500">
                        Please enter your LinkedIn email above or check the box to use your registration email
                      </p>
                    ) : !useRegistrationEmail && !isEmailValid ? (
                      <p className="text-sm text-red-500">
                        Please enter a valid email address
                      </p>
                    ) : (useRegistrationEmail || (isEmailValid && linkedinEmail)) ? (
                      <p className="text-sm text-blue-600 font-medium animate-pulse">
                        ✨ Ready to connect! Click the button above
                      </p>
                    ) : null}
                  </div>

                  {/* Skip Button - Low opacity and positioned to the side */}
                  <div className="flex justify-end">
                    <Button
                      onClick={handleSkipLinkedIn}
                      variant="ghost"
                      className="opacity-40 hover:opacity-60 text-xs px-4 py-2 text-gray-500 hover:text-gray-700 transition-all duration-300"
                    >
                      <SkipForwardIcon className="mr-1 h-3 w-3" />
                      Skip for now
                    </Button>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm text-blue-800 text-center">
                    🔒 Your LinkedIn data is secure and encrypted. We only access analytics data to provide insights.
                  </p>
                </div>
              </div>
            )}

            {onboardingStep === 2 && isLinkedInConnected && (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <h2 className="text-2xl font-bold text-black mb-2">
                  LinkedIn Connected Successfully! 🎉
                </h2>
                <p className="text-gray-600 mb-6">
                  Your account is now ready. Let's explore your dashboard!
                </p>

                <Button
                  onClick={handleCompleteOnboarding}
                  className="w-full rounded-xl py-4 px-8 text-lg font-semibold bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
                >
                  <ArrowRight className="mr-2 h-5 w-5" />
                  Go to Dashboard
                </Button>
              </div>
            )}
          </div>

          {/* Help Text */}
          <div className="text-center text-gray-600">
            <p className="text-sm">
              Need help? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinkedInOnboarding;
