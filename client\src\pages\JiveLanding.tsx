
import React from 'react';
import JiveHeader from '@/components/jive/JiveHeader';
import JiveHero from '@/components/jive/JiveHero';
import JiveAppPreview from '@/components/jive/JiveAppPreview';
import JiveHowItWorks from '@/components/jive/JiveHowItWorks';
import Ji<PERSON><PERSON>ooter from '@/components/jive/JiveFooter';
import JiveBackground from '@/components/jive/JiveBackground';
import JiveBenefits from '@/components/jive/JiveBenefits';

const JiveLanding = () => {
  return (
    <div className="relative overflow-x-hidden min-h-screen">
      <JiveBackground />

      <div className="relative isolate min-h-screen flex flex-col">
        <JiveHeader />
        <JiveHero />
        <JiveAppPreview />
        <JiveBenefits />
        <JiveHowItWorks />
        <JiveFooter />
      </div>
    </div>
  );
};

export default JiveLanding;
