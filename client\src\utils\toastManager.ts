import { toast } from 'sonner';

/**
 * Toast Manager Utility
 * Provides centralized toast management with automatic clearing
 */

export class ToastManager {
  /**
   * Clear all existing toasts and show a new success toast
   */
  static success(message: string, options?: any) {
    toast.dismiss();
    return toast.success(message, options);
  }

  /**
   * Clear all existing toasts and show a new error toast
   */
  static error(message: string, options?: any) {
    toast.dismiss();
    return toast.error(message, options);
  }

  /**
   * Clear all existing toasts and show a new info toast
   */
  static info(message: string, options?: any) {
    toast.dismiss();
    return toast.info(message, options);
  }

  /**
   * Clear all existing toasts and show a new warning toast
   */
  static warning(message: string, options?: any) {
    toast.dismiss();
    return toast.warning(message, options);
  }

  /**
   * Clear all existing toasts
   */
  static clear() {
    toast.dismiss();
  }

  /**
   * Show a toast without clearing existing ones (use sparingly)
   */
  static append = {
    success: (message: string, options?: any) => toast.success(message, options),
    error: (message: string, options?: any) => toast.error(message, options),
    info: (message: string, options?: any) => toast.info(message, options),
    warning: (message: string, options?: any) => toast.warning(message, options),
  };

  /**
   * Sequential toast display - shows toasts one after another
   */
  static sequence = {
    /**
     * Show multiple toasts in sequence with delays
     */
    show: (toasts: Array<{
      type: 'error' |'success'| 'info' | 'warning';
      message: string;
      options?: any;
      delay?: number;
    }>) => {
      // Clear all existing toasts first
      toast.dismiss();

      toasts.forEach((toastConfig, index) => {
        const delay = toastConfig.delay || (index * 3500); // Default 3.5s between toasts

        setTimeout(() => {
          // Clear before showing each new toast in sequence
          if (index > 0) {
            toast.dismiss();
          }

          switch (toastConfig.type) {
            case 'error':
              toast.error(toastConfig.message, toastConfig.options);
              break;
            case 'success':
              toast.success(toastConfig.message, toastConfig.options);
              break;
            case 'info':
              toast.info(toastConfig.message, toastConfig.options);
              break;
            case 'warning':
              toast.warning(toastConfig.message, toastConfig.options);
              break;
          }
        }, delay);
      });
    }
  };

  /**
   * Authentication-specific toast methods
   */
  static auth = {
    /**
     * Show signup success sequence
     */
    signupSuccess: (email: string) => {
      ToastManager.sequence.show([
        {
          type: 'success',
          message: 'Account created successfully! 🎉',
          options: { duration: 3000 },
          delay: 0
        },
        {
          type: 'info',
          message: '📧 Please verify your email to complete setup',
          options: {
            duration: 0, // Persistent
            description: `We've sent a verification link to ${email}`,
            action: {
              label: 'Check Email',
              onClick: () => window.open(`mailto:${email}`, '_blank')
            }
          },
          delay: 3500
        }
      ]);
    },

    /**
     * Show login error for unverified email
     */
    emailNotVerified: (email: string) => {
      ToastManager.error('📧 Please verify your email first', {
        duration: 0, // Persistent
        description: 'Check your email and click the verification link before logging in',
        action: {
          label: 'Check Email',
          onClick: () => window.open(`mailto:${email}`, '_blank')
        }
      });
    },

    /**
     * Show verification reminder (from signup flow)
     */
    verificationReminder: (email: string) => {
      ToastManager.warning('⚠️ Please verify your account for the app', {
        duration: 0, // Persistent
        description: `Check your email at ${email} and click the verification link`,
        action: {
          label: 'Check Email',
          onClick: () => window.open(`mailto:${email}`, '_blank')
        }
      });
    },

    /**
     * Show login success
     */
    loginSuccess: () => {
      ToastManager.success('Login successful! 🎉');
    },

    /**
     * Show login error
     */
    loginError: (message?: string) => {
      ToastManager.error(message || 'Login failed. Please check your credentials and try again.');
    }
  };

  /**
   * LinkedIn-specific toast methods
   */
  static linkedin = {
    /**
     * Show LinkedIn connection success
     */
    connectionSuccess: (isReconnection: boolean = false) => {
      const message = isReconnection
        ? 'LinkedIn account reconnected successfully! 🎉'
        : 'LinkedIn account connected successfully! 🎉';
      ToastManager.success(message);
    },

    /**
     * Show LinkedIn connection error
     */
    connectionError: (error: string) => {
      ToastManager.error(`LinkedIn connection failed: ${error}`);
    },

    /**
     * Show LinkedIn connection skipped
     */
    connectionSkipped: () => {
      ToastManager.info('LinkedIn connection skipped. You can connect later from your profile settings.');
    },

    /**
     * Show LinkedIn already connected
     */
    alreadyConnected: () => {
      ToastManager.success('LinkedIn account already connected! 🎉');
    }
  };
}

// Export convenience functions for backward compatibility
export const clearAllToasts = () => ToastManager.clear();
export const showToast = ToastManager;

export default ToastManager;
