import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Linkedin, Mail, Lock, User, CheckCircle, AlertCircle } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";
import { showSignupCompletionToasts } from "@/utils/toastHelpers";
import PageBackground from "@/components/layout/PageBackground";
import CheckpointModal from "@/components/auth/CheckpointModal";
import { useLinkedInVerification } from "@/hooks/useLinkedInVerification";
import { SignupFormData } from "@/types/auth";

// Enhanced Zod schema
const formSchema = z
  .object({
    useSameCredentials: z.boolean(),

    linkedinEmail: z
      .string()
      .email({ message: "Enter a valid LinkedIn email" }),
    linkedinPassword: z
      .string()
      .min(6, { message: "LinkedIn password must be at least 6 characters" }),

    email: z.string().email({ message: "Enter a valid email" }).optional(),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" })
      .optional(),
    confirmPassword: z.string().optional(),

    firstName: z.string().min(1, { message: "First name is required" }),
    lastName: z.string().min(1, { message: "Last name is required" }),
    linkedinUrl: z.string().optional(),
  })
  .refine(
    (data) => {
      if (!data.useSameCredentials) {
        return data.email && data.password && data.confirmPassword;
      }
      return true;
    },
    {
      message: "App credentials are required when not using LinkedIn credentials",
      path: ["email"],
    }
  )
  .refine(
    (data) => {
      if (!data.useSameCredentials && data.password && data.confirmPassword) {
        return data.password === data.confirmPassword;
      }
      return true;
    },
    {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    }
  );

type FormValues = z.infer<typeof formSchema>;

const EnhancedSignUpForm = () => {
  const navigate = useNavigate();
  const { signUp, user, isLoading } = useAuth();
  const { verificationState, verifyLinkedInCredentials, solveCheckpoint, resetVerification } = useLinkedInVerification();
  
  const [showCheckpointModal, setShowCheckpointModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      useSameCredentials: false,
      linkedinEmail: "",
      linkedinPassword: "",
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
      linkedinUrl: "",
    },
  });

  const useSameCredentials = form.watch("useSameCredentials");

  useEffect(() => {
    if (user && !isLoading) {
      navigate("/dashboard");
    }
  }, [user, isLoading, navigate]);

  useEffect(() => {
    if (verificationState.requiresCheckpoint && verificationState.checkpoint) {
      setShowCheckpointModal(true);
    }
  }, [verificationState.requiresCheckpoint, verificationState.checkpoint]);

  const handleLinkedInVerification = async (values: FormValues) => {
    const result = await verifyLinkedInCredentials(values.linkedinEmail, values.linkedinPassword);
    return result;
  };

  const handleCheckpointVerified = async (linkedinAccountId?: string) => {
    setShowCheckpointModal(false);

    if (verificationState.isVerified && (verificationState.accountId || linkedinAccountId)) {
      // Complete the signup process with the LinkedIn account ID
      await completeSignup(linkedinAccountId || verificationState.accountId);
    }
  };

  const completeSignup = async (linkedinAccountId?: string) => {
    const values = form.getValues();

    try {
      setIsSubmitting(true);

      const finalEmail = values.useSameCredentials ? values.linkedinEmail : values.email!;
      const finalPassword = values.useSameCredentials ? values.linkedinPassword : values.password!;

      // Note: LinkedIn password is NOT included in the data for security reasons
      // It's only used for verification and then discarded
      const result = await signUp({
        email: finalEmail,
        password: finalPassword,
        options: {
          data: {
            firstName: values.firstName,
            lastName: values.lastName,
            linkedinUrl: values.linkedinUrl,
            linkedinEmail: values.linkedinEmail,
            linkedinAccountId: linkedinAccountId || verificationState.accountId,
            // LinkedIn password is intentionally NOT included here for security
          },
        },
      });

      if (result?.error) {
        toast.error(result.error.message);
      } else {
        // Use enhanced toast sequence for signup completion
        showSignupCompletionToasts(navigate);
      }
    } catch (err) {
      console.error("Signup completion error:", err);
      toast.error("Failed to complete signup");
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      // Step 1: Verify LinkedIn credentials
      if (!verificationState.isVerified) {
        const linkedinResult = await handleLinkedInVerification(values);
        
        if (!linkedinResult.success) {
          if (linkedinResult.requiresCheckpoint) {
            // Checkpoint modal will be shown via useEffect
            return;
          } else {
            toast.error(linkedinResult.error || "LinkedIn verification failed");
            return;
          }
        }
      }

      // Step 2: Complete signup if LinkedIn is verified
      if (verificationState.isVerified) {
        await completeSignup();
      }
    } catch (err) {
      console.error("Sign up error:", err);
      toast.error("An unexpected error occurred during sign up");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getLinkedInStatusIcon = () => {
    if (verificationState.isVerified) {
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    } else if (verificationState.error) {
      return <AlertCircle className="h-5 w-5 text-red-600" />;
    }
    return null;
  };

  return (
    <PageBackground>
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-white">
              Create your account
            </h2>
            <p className="mt-2 text-sm text-gray-300">
              Connect your LinkedIn and start amplifying your voice
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="mt-8 space-y-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 space-y-4">
                {/* LinkedIn Credentials Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Linkedin className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold text-white">LinkedIn Account</h3>
                    {getLinkedInStatusIcon()}
                  </div>

                  <FormField
                    control={form.control}
                    name="linkedinEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">LinkedIn Email</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              {...field}
                              type="email"
                              placeholder="<EMAIL>"
                              className="pl-10 bg-white/20 border-white/30 text-white placeholder-gray-300"
                              disabled={verificationState.isVerified}
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="linkedinPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">LinkedIn Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              {...field}
                              type="password"
                              placeholder="••••••••"
                              className="pl-10 bg-white/20 border-white/30 text-white placeholder-gray-300"
                              disabled={verificationState.isVerified}
                            />
                          </div>
                        </FormControl>
                        <div className="text-xs text-gray-300 mt-1">
                          🔒 Password is only used for verification and is not stored
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {verificationState.isVerified && (
                    <div className="text-green-400 text-sm flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      LinkedIn account verified successfully
                    </div>
                  )}
                </div>

                {/* Same Credentials Checkbox */}
                <FormField
                  control={form.control}
                  name="useSameCredentials"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="border-white/30"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-white text-sm">
                          Use LinkedIn credentials for app login
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                {/* App Credentials Section (if not using same credentials) */}
                {!useSameCredentials && (
                  <div className="space-y-4 pt-4 border-t border-white/20">
                    <h3 className="text-lg font-semibold text-white">App Credentials</h3>
                    
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white">App Email</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                {...field}
                                type="email"
                                placeholder="<EMAIL>"
                                className="pl-10 bg-white/20 border-white/30 text-white placeholder-gray-300"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white">App Password</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                {...field}
                                type="password"
                                placeholder="••••••••"
                                className="pl-10 bg-white/20 border-white/30 text-white placeholder-gray-300"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white">Confirm Password</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                {...field}
                                type="password"
                                placeholder="••••••••"
                                className="pl-10 bg-white/20 border-white/30 text-white placeholder-gray-300"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}
              </div>

              {/* Personal Information */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 space-y-4">
                <h3 className="text-lg font-semibold text-white">Personal Information</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">First Name</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              {...field}
                              placeholder="John"
                              className="pl-10 bg-white/20 border-white/30 text-white placeholder-gray-300"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Last Name</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              {...field}
                              placeholder="Doe"
                              className="pl-10 bg-white/20 border-white/30 text-white placeholder-gray-300"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="linkedinUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">LinkedIn Profile URL (Optional)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Linkedin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            {...field}
                            placeholder="https://linkedin.com/in/yourprofile"
                            className="pl-10 bg-white/20 border-white/30 text-white placeholder-gray-300"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-black text-white hover:bg-black/90 py-6 px-8 text-lg rounded-full"
                disabled={isSubmitting || verificationState.isVerifying}
              >
                {isSubmitting || verificationState.isVerifying ? (
                  <span className="flex items-center justify-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    {verificationState.isVerifying ? "Verifying LinkedIn..." : "Creating Account..."}
                  </span>
                ) : (
                  "Create Account"
                )}
              </Button>

              <div className="text-center">
                <p className="text-sm text-gray-300">
                  Already have an account?{" "}
                  <Link to="/login" className="font-medium text-white hover:text-gray-200">
                    Sign in
                  </Link>
                </p>
              </div>
            </form>
          </Form>
        </div>
      </div>

      {/* Checkpoint Modal */}
      <CheckpointModal
        open={showCheckpointModal}
        onClose={() => setShowCheckpointModal(false)}
        checkpoint={verificationState.checkpoint || null}
        onVerified={handleCheckpointVerified}
      />
    </PageBackground>
  );
};

export default EnhancedSignUpForm;
