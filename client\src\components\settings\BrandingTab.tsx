
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Check, AlertCircle, Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import BrandColorEditor from './BrandColorEditor';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';

interface ColorConfig {
  name: string;
  value: string;
  label: string;
}

interface LinkedInPostExample {
  id: string;
  subject: string;
  content: string;
}

const BrandingTab = () => {
  const { toast } = useToast();
  const [brandColors, setBrandColors] = useState([
    { name: "Primary Blue", value: "#2563eb", label: "Primary Blue" },
    { name: "Teal", value: "#0d9488", label: "Teal" },
    { name: "Dark Gray", value: "#1e293b", label: "Dark Gray" }
  ]);
  
  const [colorEditorOpen, setColorEditorOpen] = useState(false);
  const [postExamples, setPostExamples] = useState<LinkedInPostExample[]>([
    {
      id: "1",
      subject: "Product Launch",
      content: "Excited to announce the launch of our new enterprise software solution! 🚀 After months of hard work, we're proud to unveil a product that will transform how businesses manage their operations. \n\nOur team has focused on creating an intuitive user experience while delivering powerful capabilities that address the most challenging problems our customers face.\n\n#ProductLaunch #Innovation"
    },
    {
      id: "2",
      subject: "Industry Insight",
      content: "The landscape of enterprise software is evolving faster than ever. As businesses adapt to remote and hybrid work environments, we're seeing a fundamental shift in what tools are needed to succeed.\n\nAt Acme, we believe the future belongs to companies that embrace flexibility, automation, and data-driven decision making.\n\nWhat changes are you seeing in your industry? Let's discuss below.\n\n#FutureOfWork #EnterpriseTools"
    }
  ]);

  // State for new example dialog
  const [newExampleOpen, setNewExampleOpen] = useState(false);
  const [currentExample, setCurrentExample] = useState<LinkedInPostExample | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Handle adding or editing an example
  const handleSaveExample = () => {
    if (!currentExample?.subject || !currentExample?.content) {
      toast({
        title: "Error",
        description: "Both subject and content are required.",
        variant: "destructive"
      });
      return;
    }

    if (isEditing) {
      setPostExamples(postExamples.map(example => 
        example.id === currentExample.id ? currentExample : example
      ));
      toast({
        title: "Example updated",
        description: "Your LinkedIn post example has been updated."
      });
    } else {
      setPostExamples([
        ...postExamples, 
        { ...currentExample, id: Date.now().toString() }
      ]);
      toast({
        title: "Example added",
        description: "Your LinkedIn post example has been added."
      });
    }
    
    setNewExampleOpen(false);
    setCurrentExample(null);
    setIsEditing(false);
  };

  // Handle editing an example
  const handleEditExample = (example: LinkedInPostExample) => {
    setCurrentExample(example);
    setIsEditing(true);
    setNewExampleOpen(true);
  };

  // Handle deleting an example
  const handleDeleteExample = (id: string) => {
    setPostExamples(postExamples.filter(example => example.id !== id));
    toast({
      title: "Example deleted",
      description: "Your LinkedIn post example has been deleted."
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Brand Voice & Tone</CardTitle>
          <CardDescription>
            Define the guidelines for your company's voice on social media
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="brand-attributes">Brand Attributes</Label>
            <p className="text-sm text-muted-foreground">Add up to 5 key attributes that define your brand</p>
            <div className="flex flex-wrap gap-2 my-2">
              <Badge className="bg-enterprise-blue">Innovative</Badge>
              <Badge className="bg-enterprise-blue">Trustworthy</Badge>
              <Badge className="bg-enterprise-blue">Professional</Badge>
              <Badge className="bg-enterprise-blue">Customer-focused</Badge>
              <Badge className="bg-enterprise-blue">Expert</Badge>
            </div>
            <Input id="brand-attributes" placeholder="Add new attribute..." />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="tone-voice">Tone of Voice</Label>
            <Textarea 
              id="tone-voice" 
              defaultValue="Our communication style is professional yet approachable. We use clear, concise language and avoid jargon when possible. We're authoritative but not condescending, and we focus on how our products and services benefit our customers."
              rows={4}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="approved-hashtags">Approved Hashtags</Label>
            <p className="text-sm text-muted-foreground">Enter hashtags separated by commas</p>
            <Input 
              id="approved-hashtags" 
              defaultValue="#AcmeCorp, #Innovation, #EnterpriseSuccess, #TeamAcme, #TechLeader, #ProductName"
            />
          </div>
          
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Content Guidelines</h3>
            <div className="space-y-4">
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-3">
                  <Check className="text-green-500 mt-0.5" size={16} />
                  <Label htmlFor="guideline-1">Do</Label>
                </div>
                <Textarea 
                  id="guideline-1" 
                  defaultValue="Use data and statistics to support claims. Emphasize customer benefits over features. Keep content concise and focused."
                  rows={2}
                  className="w-full"
                />
              </div>
              
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-3">
                  <AlertCircle className="text-red-500 mt-0.5" size={16} />
                  <Label htmlFor="guideline-2">Don't</Label>
                </div>
                <Textarea 
                  id="guideline-2" 
                  defaultValue="Criticize competitors by name. Make claims that cannot be verified. Use slang or unprofessional language."
                  rows={2}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button className="bg-enterprise-blue hover:bg-enterprise-blue/90">
            Save Brand Guidelines
          </Button>
        </CardFooter>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Visual Branding</CardTitle>
          <CardDescription>
            Upload your company's visual assets
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>Company Logo</Label>
              <div className="border rounded-md p-4 flex flex-col items-center justify-center">
                <div className="h-20 w-20 rounded-md bg-enterprise-blue flex items-center justify-center mb-4">
                  <span className="font-bold text-white text-xl">A</span>
                </div>
                <Button variant="outline" size="sm">
                  Change Logo
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Brand Colors</Label>
              <div className="border rounded-md p-4 space-y-3">
                <div className="grid grid-cols-3 gap-2">
                  {brandColors.map((color, index) => (
                    <div key={index}>
                      <div 
                        className="h-8 rounded-md mb-1"
                        style={{ backgroundColor: color.value }}
                      ></div>
                      <p className="text-xs text-center">{color.value}</p>
                    </div>
                  ))}
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => setColorEditorOpen(true)}
                >
                  Edit Brand Colors
                </Button>
              </div>
            </div>
          </div>
          
          {/* LinkedIn Post Examples Section */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <Label className="text-base">Ideal LinkedIn Post Examples</Label>
              <Button 
                size="sm" 
                onClick={() => {
                  setCurrentExample({ id: '', subject: '', content: '' });
                  setIsEditing(false);
                  setNewExampleOpen(true);
                }}
                className="bg-enterprise-blue hover:bg-enterprise-blue/90"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Example
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Add examples of ideal LinkedIn posts to reference when generating new content.
            </p>
            
            <div className="space-y-4 mt-2">
              {postExamples.length === 0 ? (
                <div className="text-center p-6 border rounded-md border-dashed">
                  <p className="text-muted-foreground">No examples added yet</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2"
                    onClick={() => {
                      setCurrentExample({ id: '', subject: '', content: '' });
                      setNewExampleOpen(true);
                    }}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Your First Example
                  </Button>
                </div>
              ) : (
                postExamples.map((example) => (
                  <div key={example.id} className="border rounded-md p-4 space-y-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{example.subject}</h4>
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleEditExample(example)}
                        >
                          Edit
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDeleteExample(example.id)}
                          className="text-red-500 hover:text-red-600 hover:bg-red-50"
                        >
                          Delete
                        </Button>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground whitespace-pre-line">
                      {example.content.length > 150 
                        ? `${example.content.substring(0, 150)}...` 
                        : example.content}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button className="bg-enterprise-blue hover:bg-enterprise-blue/90">
            Save Visual Assets
          </Button>
        </CardFooter>
      </Card>

      {/* LinkedIn Post Example Dialog */}
      <Dialog open={newExampleOpen} onOpenChange={setNewExampleOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{isEditing ? "Edit LinkedIn Post Example" : "Add LinkedIn Post Example"}</DialogTitle>
            <DialogDescription>
              Create examples of ideal LinkedIn posts to reference when generating new content.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="post-subject">Subject Line</Label>
              <Input 
                id="post-subject" 
                placeholder="e.g., Product Launch, Industry Insight"
                value={currentExample?.subject || ''}
                onChange={(e) => setCurrentExample(prev => prev ? {...prev, subject: e.target.value} : null)}
              />
              <p className="text-xs text-muted-foreground">
                A brief description of what the post is about
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="post-content">Post Content</Label>
              <Textarea 
                id="post-content" 
                placeholder="Enter the LinkedIn post content here..."
                rows={6}
                value={currentExample?.content || ''}
                onChange={(e) => setCurrentExample(prev => prev ? {...prev, content: e.target.value} : null)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setNewExampleOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSaveExample}
              className="bg-enterprise-blue hover:bg-enterprise-blue/90"
            >
              {isEditing ? "Update Example" : "Add Example"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <BrandColorEditor 
        open={colorEditorOpen}
        onOpenChange={setColorEditorOpen}
        colors={brandColors}
        onSave={setBrandColors}
      />
    </div>
  );
};

export default BrandingTab;
