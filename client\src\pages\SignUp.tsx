import React, { useEffect, useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
// still used for 2FA
import { getApiUrl, CLIENT_URL } from "@/config/env";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Mail, Lock, LogIn, User, Link as LinkIcon } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";
import { showSignupCompletionToasts } from "@/utils/toastHelpers";
import PageBackground from "@/components/layout/PageBackground";
import { Checkbox } from "@/components/ui/checkbox";

// Zod schema remains unchanged
const formSchema = z
  .object({
    useSameCredentials: z.boolean(),

    linkedinEmail: z
      .string()
      .email({ message: "Enter a valid LinkedIn email" }),
    linkedinPassword: z
      .string()
      .min(6, { message: "LinkedIn password must be at least 6 characters" }),

    email: z.string().email({ message: "Enter a valid email" }).optional(),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" })
      .optional(),

    confirmPassword: z.string().optional(),

    firstName: z.string().min(1, { message: "First name is required" }),
    lastName: z.string().min(1, { message: "Last name is required" }),
    linkedinUrl: z
      .string()
      .url({ message: "Enter a valid LinkedIn URL" })
      .includes("linkedin.com", { message: "Must be a LinkedIn URL" }),
  })
  .superRefine((data, ctx) => {
    if (!data.useSameCredentials) {
      if (!data.email) {
        ctx.addIssue({
          path: ["email"],
          message: "Email is required",
          code: z.ZodIssueCode.custom,
        });
      }
      if (!data.password) {
        ctx.addIssue({
          path: ["password"],
          message: "Password is required",
          code: z.ZodIssueCode.custom,
        });
      }
      if (data.password !== data.confirmPassword) {
        ctx.addIssue({
          path: ["confirmPassword"],
          message: "Passwords do not match",
          code: z.ZodIssueCode.custom,
        });
      }
    }
  });

type FormValues = z.infer<typeof formSchema>;

const SignUp = () => {
  const navigate = useNavigate();
  const { signUp, user, isLoading } = useAuth();




  const [useSameCredentials, setUseSameCredentials] = useState(false);
  const [linkedinAccountId, setLinkedinAccountId] = useState<string | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      useSameCredentials: false,
      linkedinEmail: "",
      linkedinPassword: "",
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
      linkedinUrl: "",
    },
  });

  useEffect(() => {
    if (user && !isLoading) {
      navigate("/dashboard");
    }
  }, [user, isLoading, navigate]);

  // Listen for verification completion messages from popup windows
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Verify origin for security
      if (event.origin !== CLIENT_URL && event.origin !== window.location.origin) {
        return;
      }

      if (event.data.type === 'VERIFICATION_COMPLETE') {
        console.log('📨 Received verification completion message:', event.data);

        if (event.data.status === 'success') {
          toast.success("LinkedIn verification completed successfully!");

          // If we have a stored account ID, complete the signup
          if (linkedinAccountId) {
            completeSignupAfterOtp(linkedinAccountId);
          } else {
            toast.info("Please try signing up again to complete the process.");
          }
        } else if (event.data.status === 'error') {
          toast.error("LinkedIn verification failed. Please try again.");
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [linkedinAccountId]);



  const onSubmit = async (values: FormValues) => {
    const {
      linkedinEmail,
      linkedinPassword,
      useSameCredentials,
      email,
      password,
      firstName,
      lastName,
      linkedinUrl,
    } = values;

    const finalEmail = useSameCredentials ? linkedinEmail : email!;
    const finalPassword = useSameCredentials ? linkedinPassword : password!;

    try {
      // ❌ OLD METHOD - Using custom auth with password (DEPRECATED)
      // This was causing fake verification without real LinkedIn connection
      console.log('⚠️ SignUp.tsx is using DEPRECATED custom auth method');
      console.log('🔄 This should be updated to use hosted auth flow');

      // For now, show error and redirect to hosted auth
      toast.error('Custom LinkedIn authentication is deprecated. Please use the new hosted authentication method.');

      // TODO: Replace this entire section with hosted auth component
      return;
    } catch (err) {
      console.error(err);
      toast.error("Something went wrong");
    }
  };

  const completeSignupAfterOtp = async (accountId?: string) => {
    const values = form.getValues();

    const finalEmail = values.useSameCredentials
      ? values.linkedinEmail
      : values.email!;
    const finalPassword = values.useSameCredentials
      ? values.linkedinPassword
      : values.password!;

    const {
      firstName,
      lastName,
      linkedinUrl,
      linkedinEmail,
      // linkedinPassword: Not extracted - never stored for security
    } = values;

    try {
      const result = await signUp({
        email: finalEmail,
        password: finalPassword,
        options: {
          data: {
            firstName,
            lastName,
            linkedinUrl,
            linkedinEmail,
            linkedinAccountId: accountId || linkedinAccountId, // Use the account ID from checkpoint
            // linkedinPassword: REMOVED for security - never store LinkedIn passwords
          },
        },
      });

      if (result?.error) {
        toast.error(result.error.message);
      } else {
        // Use enhanced toast sequence for signup completion
        showSignupCompletionToasts(navigate);
      }
    } catch (err) {
      console.error(err);
      toast.error("Something went wrong");
    }
  };

  return (
    <div className="flex flex-col min-h-screen relative overflow-hidden">
      <PageBackground />



      <div className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="w-full max-w-md p-8 space-y-8 bg-red-500 rounded-lg shadow-xl bg-opacity-95 backdrop-blur-sm border border-opacity-20">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-2 text-black">
              Create Your Account
            </h1>
            <p className="text-gray-700">
              Join CompanyVoice to amplify your brand
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* LinkedIn Credentials */}
              <FormField
                control={form.control}
                name="linkedinEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">LinkedIn Email</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          placeholder="<EMAIL>"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="linkedinPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">
                      LinkedIn Password
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          type="password"
                          placeholder="LinkedIn password"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <div className="text-xs text-gray-500 mt-1">
                      🔒 Password is only used for verification and is not stored
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormItem>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={useSameCredentials}
                    onCheckedChange={(checked) => {
                      setUseSameCredentials(!!checked);
                      form.setValue("useSameCredentials", !!checked);
                      if (checked) {
                        form.setValue("email", form.getValues("linkedinEmail"));
                        form.setValue(
                          "password",
                          form.getValues("linkedinPassword")
                        );
                      }
                    }}
                  />
                  <FormLabel className="text-sm text-black">
                    Use LinkedIn email and password for CompanyVoice login
                  </FormLabel>
                </div>
              </FormItem>

              {/* App Email & Password Fields (conditionally disabled) */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">Email</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          placeholder="<EMAIL>"
                          className="pl-10"
                          disabled={useSameCredentials}
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          type="password"
                          placeholder="At least 8 characters"
                          className="pl-10"
                          disabled={useSameCredentials}
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {!useSameCredentials && (
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black">
                        Confirm Password
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input
                            type="password"
                            placeholder="Confirm your password"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Name and LinkedIn URL */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black">First Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input
                            placeholder="John"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black">Last Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                          <Input
                            placeholder="Doe"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="linkedinUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black">
                      LinkedIn Profile
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <LinkIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-500" />
                        <Input
                          placeholder="https://linkedin.com/in/your-profile"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full rounded-full bg-black text-white hover:bg-black/90 py-6 px-8 text-lg"
              >
                Create Account
              </Button>

              <div className="text-center text-gray-700">
                <p>Already have an account?</p>
                <Link
                  to="/login"
                  className="flex items-center justify-center text-black hover:text-black/90 mt-2"
                >
                  <LogIn className="mr-2 h-4 w-4" /> Sign In
                </Link>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default SignUp;

