import { UnipileConfig, createUnipileHeaders, handleUnipileError, logUnipileSuccess, logUnipileRequest } from './config';

export interface UserAccountMapping {
    account_id: string;
    userEmail: string;
    timestamp: Date;
}

/**
 * Account mappings are now stored directly in Supabase profiles table
 * No in-memory storage needed
 */

/**
 * Store user account mapping - now handled directly in profiles table
 */
export async function storeUserAccountMapping(
    internalUserId: string,
    unipileAccountId: string,
    userEmail: string
): Promise<void> {
    try {
        console.log('📝 Account mapping stored directly in profiles table:', {
            internalUserId,
            unipileAccountId,
            userEmail
        });
        // Data is stored directly in profiles table via webhook processing
    } catch (error: any) {
        console.error('Error with account mapping:', error);
        throw error;
    }
}

/**
 * Get user account mapping by internal user ID
 */
export async function getUserAccountMapping(internalUserId: string): Promise<UserAccountMapping | null> {
    try {
        console.log('🔍 Looking up user account mapping for:', internalUserId);
        // Data is retrieved from profiles table via direct database queries
        return null; // Placeholder - actual implementation uses profiles table
    } catch (error: any) {
        console.error('Error getting user account mapping:', error);
        throw error;
    }
}

/**
 * Get LinkedIn profile data using Unipile accounts endpoint
 */
export async function getLinkedInProfile(config: UnipileConfig, accountId: string): Promise<any> {
    try {
        console.log('🔍 Fetching LinkedIn profile:', {
            accountId: accountId,
            endpoint: 'GET /api/v1/accounts/{accountId}'
        });

        const url = `${config.baseUrl}/api/v1/accounts/${accountId}`;
        logUnipileRequest('Profile', url);

        const response = await fetch(url, {
            method: 'GET',
            headers: createUnipileHeaders(config.apiKey)
        });

        if (!response.ok) {
            await handleUnipileError(response, 'Profile API');
        }

        const data = await response.json();
        logUnipileSuccess('LinkedIn profile', {
            id: data.id,
            name: data.name,
            type: data.type,
            connection_params: data.connection_params
        });

        return data;
    } catch (error: any) {
        console.error('💥 Error fetching LinkedIn profile:', error);
        throw new Error(`Failed to fetch LinkedIn profile: ${error.message}`);
    }
}

/**
 * List all accounts using Unipile API
 */
export async function listAccounts(config: UnipileConfig): Promise<any> {
    try {
        console.log('Fetching all accounts');

        const url = `${config.baseUrl}/api/v1/accounts`;
        const response = await fetch(url, {
            method: 'GET',
            headers: createUnipileHeaders(config.apiKey)
        });

        if (!response.ok) {
            await handleUnipileError(response, 'Accounts list');
        }

        const data = await response.json();
        console.log('Accounts response:', data);
        return data;
    } catch (error: any) {
        console.error('Error fetching accounts:', error);
        throw new Error(`Failed to fetch accounts: ${error.message}`);
    }
}

/**
 * UNIVERSAL ACCOUNT CHECK - Always checks for existing accounts before any connection
 * This ensures NO duplicate accounts are ever created in Unipile
 */
export async function checkAccountByEmail(config: UnipileConfig, email: string): Promise<{
    exists: boolean;
    account_id: string | null;
    status: string;
    profile?: any;
    shouldReconnect: boolean;
    error?: string;
    message?: string;
}> {
    console.log('🔍 UNIVERSAL ACCOUNT CHECK for email:', email);

    try {
        // Step 1: Check our internal mapping first
        const mapping = await getUserAccountMapping(email);

        if (mapping && mapping.account_id) {
            console.log('✅ Found internal account mapping:', {
                account_id: mapping.account_id,
                userEmail: email,
                timestamp: mapping.timestamp
            });

            // Step 2: Verify the account still exists and is valid in Unipile
            try {
                const profile = await getLinkedInProfile(config, mapping.account_id);
                if (profile && profile.id) {
                    console.log('✅ Account verified and ACTIVE in Unipile:', {
                        accountId: mapping.account_id,
                        profileName: profile.name,
                        status: 'ACTIVE'
                    });

                    return {
                        exists: true,
                        account_id: mapping.account_id,
                        status: 'OK',
                        profile: profile,
                        shouldReconnect: true
                    };
                }
            } catch (profileError) {
                console.log('⚠️ Account mapping exists but Unipile account may be invalid:', profileError);

                // Account mapping exists but account might be invalid in Unipile , So We should still try to reconnect in case it's a temporary issue
                return {
                    exists: true,
                    account_id: mapping.account_id,
                    status: 'INVALID_BUT_RECONNECT',
                    shouldReconnect: true,
                    error: 'Account exists but may need reconnection'
                };
            }
        }

        // Step 3: Search all accounts in Unipile
        console.log('🔍 Step 3: No internal mapping found, searching all Unipile accounts for:', email);

        try {
            const accountsResponse = await listAccounts(config);
            const allAccounts = accountsResponse.items || [];

            if (allAccounts && allAccounts.length > 0) {
                console.log(`🔍 Found ${allAccounts.length} total accounts in Unipile, checking LinkedIn accounts...`);

                const linkedInAccounts = allAccounts.filter((account: any) => account.type === 'LINKEDIN');
                console.log(`🔍 Found ${linkedInAccounts.length} LinkedIn accounts to check`);

                // Sort by creation date (most recent first) to prefer newer accounts
                linkedInAccounts.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

                for (const account of linkedInAccounts) {
                    try {
                        console.log('🔍 Checking LinkedIn account:', {
                            accountId: account.id,
                            name: account.name,
                            created_at: account.created_at
                        });

                        const profile = await getLinkedInProfile(config, account.id);

                        if (profile && profile.connection_params && profile.connection_params.im) {
                            console.log('📊 Account profile details:', {
                                accountId: account.id,
                                profileName: profile.name,
                                publicId: profile.connection_params.im.publicIdentifier,
                                linkedinId: profile.connection_params.im.id
                            });

                            // Since Unipile doesn't provide email in profile, we'll use name matching
                            // and return the most recent account for the user to choose
                            const emailName = email.split('@')[0].toLowerCase().replace(/[._]/g, '');
                            const profileName = (profile.name || '').toLowerCase().replace(/[^a-z]/g, '');

                            console.log('🔍 Name matching check:', {
                                emailName,
                                profileName,
                                accountName: account.name
                            });

                            // Return the most recent LinkedIn account as a potential match
                            // The user can confirm if this is their account
                            console.log('✅ Found potential LinkedIn account (most recent):', {
                                accountId: account.id,
                                name: account.name,
                                email: email,
                                status: 'POTENTIAL_MATCH'
                            });

                            return {
                                exists: true,
                                account_id: account.id,
                                status: 'POTENTIAL_MATCH',
                                profile: profile,
                                shouldReconnect: true,
                                message: `Found LinkedIn account "${account.name}" - please confirm this is your account`
                            };
                        }
                    } catch (profileError) {
                        console.log('⚠️ Error getting profile for account:', account.id, profileError);
                        continue;
                    }
                }
            }
        } catch (searchError) {
            console.log('⚠️ Error searching Unipile accounts:', searchError);
        }

        console.log('❌ No existing account found anywhere for email:', email);
        console.log('📊 Summary of search attempts:');
        console.log('  - Internal mapping: Not found');
        console.log('  - Unipile account search: No matching accounts found');

        return {
            exists: false,
            account_id: null,
            status: 'NOT_FOUND',
            shouldReconnect: false,
            message: 'No existing LinkedIn accounts found for this email'
        };

    } catch (error) {
        console.error('❌ Error in universal account check:', error);
        return {
            exists: false,
            account_id: null,
            status: 'ERROR',
            shouldReconnect: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
