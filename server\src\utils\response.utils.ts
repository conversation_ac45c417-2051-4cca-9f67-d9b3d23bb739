import { Response } from 'express';

/**
 * Response Utility Functions
 * Pure functions for standardized API responses
 */

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    details?: string;
    timestamp?: string;
}

/**
 * Send success response
 */
export const sendSuccess = <T>(
    res: Response,
    data?: T,
    message?: string,
    statusCode: number = 200
): void => {
    const response: ApiResponse<T> = {
        success: true,
        timestamp: new Date().toISOString()
    };

    if (data !== undefined) response.data = data;
    if (message) response.message = message;

    res.status(statusCode).json(response);
};

/**
 * Send error response
 */
export const sendError = (
    res: Response,
    error: string,
    details?: string,
    statusCode: number = 500
): void => {
    const response: ApiResponse = {
        success: false,
        error,
        timestamp: new Date().toISOString()
    };

    if (details) response.details = details;

    res.status(statusCode).json(response);
};

/**
 * Send validation error response
 */
export const sendValidationError = (
    res: Response,
    error: string,
    details?: string
): void => {
    sendError(res, error, details, 400);
};

/**
 * Send not found error response
 */
export const sendNotFound = (
    res: Response,
    resource: string = 'Resource'
): void => {
    sendError(res, `${resource} not found`, undefined, 404);
};

/**
 * Send unauthorized error response
 */
export const sendUnauthorized = (
    res: Response,
    message: string = 'Unauthorized access'
): void => {
    sendError(res, message, undefined, 401);
};

/**
 * Send forbidden error response
 */
export const sendForbidden = (
    res: Response,
    message: string = 'Access forbidden'
): void => {
    sendError(res, message, undefined, 403);
};

/**
 * Send created response
 */
export const sendCreated = <T>(
    res: Response,
    data: T,
    message?: string
): void => {
    sendSuccess(res, data, message || 'Resource created successfully', 201);
};

/**
 * Send no content response
 */
export const sendNoContent = (res: Response): void => {
    res.status(204).send();
};

/**
 * Handle async route errors
 */
export const handleAsyncError = (
    res: Response,
    error: any,
    operation: string = 'operation'
): void => {
    console.error(`❌ Error in ${operation}:`, error);
    
    const errorMessage = error.message || `Failed to ${operation}`;
    const details = error.stack || error.toString();
    
    sendError(res, errorMessage, details);
};

/**
 * Validate required fields
 */
export const validateRequiredFields = (
    res: Response,
    data: Record<string, any>,
    requiredFields: string[]
): boolean => {
    const missingFields = requiredFields.filter(field => !data[field]);
    
    if (missingFields.length > 0) {
        sendValidationError(
            res,
            'Missing required fields',
            `Required fields: ${missingFields.join(', ')}`
        );
        return false;
    }
    
    return true;
};

/**
 * Log request details
 */
export const logRequest = (
    method: string,
    endpoint: string,
    userId?: string,
    additionalData?: Record<string, any>
): void => {
    console.log(`🌐 ${method} ${endpoint}${userId ? ` - User: ${userId}` : ''}`);
    if (additionalData) {
        console.log('📋 Request data:', additionalData);
    }
};

/**
 * Log response details
 */
export const logResponse = (
    method: string,
    endpoint: string,
    success: boolean,
    duration?: number
): void => {
    const status = success ? '✅' : '❌';
    const durationText = duration ? ` (${duration}ms)` : '';
    console.log(`${status} ${method} ${endpoint}${durationText}`);
};
