import { UnipileConfig, createUnipileHeaders, handleUnipileError } from './config';

export interface AccountStatusResult {
    status: string;
    message: string;
    data?: any;
}

/**
 * Check if account exists in connected accounts list
 */
export async function checkAccountInList(config: UnipileConfig, accountId: string): Promise<AccountStatusResult> {
    try {
        console.log('🔍 Checking if account exists in accounts list:', accountId);

        const response = await fetch(`${config.baseUrl}/api/v1/accounts`, {
            method: 'GET',
            headers: createUnipileHeaders(config.apiKey)
        });

        if (!response.ok) {
            await handleUnipileError(response, 'Accounts list check');
        }

        const data = await response.json();
        console.log('📋 Accounts list response:', {
            totalAccounts: data.items?.length || 0,
            accountIds: data.items?.map((acc: any) => acc.id) || []
        });

        // Check if our account ID exists in the list
        const accountExists = data.items?.some((acc: any) => acc.id === accountId);

        if (accountExists) {
            const account = data.items.find((acc: any) => acc.id === accountId);
            console.log('✅ Account found in list:', account);
            return {
                status: 'CONNECTED',
                message: 'Account found in connected accounts list',
                data: account
            };
        } else {
            return {
                status: 'NOT_FOUND',
                message: 'Account not found in connected accounts list',
                data: data
            };
        }
    } catch (error: any) {
        console.error('💥 Error checking accounts list:', error);
        return {
            status: 'ERROR',
            message: `Failed to check accounts list: ${error.message}`
        };
    }
}

/**
 * Get account status using direct account endpoint
 */
export async function getDirectAccountStatus(config: UnipileConfig, accountId: string): Promise<AccountStatusResult | null> {
    try {
        const response = await fetch(`${config.baseUrl}/api/v1/accounts/${accountId}`, {
            method: 'GET',
            headers: createUnipileHeaders(config.apiKey)
        });

        if (response.ok) {
            const data = await response.json();
            console.log('✅ Direct account response:', JSON.stringify(data, null, 2));

            // Check multiple possible status indicators
            let accountStatus = 'UNKNOWN';

            // Check various possible status fields
            if (data.status) {
                accountStatus = data.status;
            } else if (data.connection_status) {
                accountStatus = data.connection_status;
            } else if (data.state) {
                accountStatus = data.state;
            } else if (data.account_status) {
                accountStatus = data.account_status;
            } else if (data.provider && data.id) {
                // If we have provider and id, account likely exists and is connected
                accountStatus = 'CONNECTED';
            }

            console.log('📊 Direct method - detected status:', accountStatus);

            // Consider these statuses as "connected/successful"
            const successStatuses = ['OK', 'CONNECTED', 'ACTIVE', 'VALID', 'SUCCESS'];
            const isConnected = successStatuses.includes(accountStatus.toUpperCase());

            if (isConnected) {
                return {
                    status: accountStatus,
                    message: `Account status: ${accountStatus} (Connected)`,
                    data: data
                };
            }
        }
        return null;
    } catch (directError) {
        console.log('⚠️ Direct account check failed');
        return null;
    }
}

/**
 * Get comprehensive account status for polling during verification
 */
export async function getAccountStatus(config: UnipileConfig, accountId: string): Promise<AccountStatusResult> {
    try {
        console.log('🔍 Checking account status for:', accountId);

        // Method 1: Try direct account endpoint
        const directResult = await getDirectAccountStatus(config, accountId);
        if (directResult) {
            return directResult;
        }

        // Method 2: Check accounts list (fallback)
        console.log('🔄 Trying accounts list method...');
        const listResult = await checkAccountInList(config, accountId);

        if (listResult.status === 'CONNECTED') {
            console.log('✅ Account found via accounts list method!');
            return listResult;
        }

        // If both methods fail, return the list result
        return listResult;

    } catch (error: any) {
        console.error('💥 Error checking account status:', error);
        return {
            status: 'ERROR',
            message: `Failed to check account status: ${error.message}`
        };
    }
}
