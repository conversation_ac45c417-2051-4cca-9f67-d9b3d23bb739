-- Phase 1: Create sync tracking table for monitoring LinkedIn post synchronization
-- This table helps us track sync operations, performance, and errors

-- Step 1: Create sync_operations table
CREATE TABLE public.sync_operations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  
  -- Operation Details
  operation_type text NOT NULL CHECK (operation_type IN ('full_sync', 'incremental_sync', 'manual_sync', 'retry_sync')),
  sync_source text DEFAULT 'unipile' CHECK (sync_source IN ('unipile', 'manual', 'scheduled')),
  
  -- Timing
  started_at timestamp with time zone DEFAULT now(),
  completed_at timestamp with time zone,
  duration_seconds integer, -- Auto-calculated duration
  
  -- Results
  status text DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
  posts_fetched integer DEFAULT 0,
  posts_created integer DEFAULT 0,
  posts_updated integer DEFAULT 0,
  posts_skipped integer DEFAULT 0,
  posts_failed integer DEFAULT 0,
  
  -- Error Tracking
  error_message text,
  error_details jsonb,
  
  -- API Usage
  api_calls_made integer DEFAULT 0,
  api_rate_limit_hit boolean DEFAULT false,
  
  -- Metadata
  sync_config jsonb, -- Store sync configuration used
  created_at timestamp with time zone DEFAULT now(),
  
  -- Constraints
  CONSTRAINT sync_operations_pkey PRIMARY KEY (id),
  CONSTRAINT sync_operations_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Step 2: Create indexes for sync operations
CREATE INDEX idx_sync_operations_user_id ON public.sync_operations(user_id);
CREATE INDEX idx_sync_operations_status ON public.sync_operations(status);
CREATE INDEX idx_sync_operations_started_at ON public.sync_operations(started_at DESC);
CREATE INDEX idx_sync_operations_operation_type ON public.sync_operations(operation_type);

-- Partial indexes for active operations
CREATE INDEX idx_sync_operations_running ON public.sync_operations(user_id, started_at) 
WHERE status = 'running';

CREATE INDEX idx_sync_operations_failed ON public.sync_operations(user_id, started_at) 
WHERE status = 'failed';

-- Step 3: Create function to auto-calculate duration
CREATE OR REPLACE FUNCTION calculate_sync_duration()
RETURNS TRIGGER AS $$
BEGIN
    -- Only calculate duration when operation is completed
    IF NEW.completed_at IS NOT NULL AND OLD.completed_at IS NULL THEN
        NEW.duration_seconds = EXTRACT(EPOCH FROM (NEW.completed_at - NEW.started_at))::integer;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_sync_duration_trigger
    BEFORE UPDATE ON public.sync_operations
    FOR EACH ROW
    EXECUTE FUNCTION calculate_sync_duration();

-- Step 4: Create sync statistics view for easy monitoring
CREATE OR REPLACE VIEW public.sync_statistics AS
SELECT 
    user_id,
    COUNT(*) as total_syncs,
    COUNT(*) FILTER (WHERE status = 'completed') as successful_syncs,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_syncs,
    COUNT(*) FILTER (WHERE status = 'running') as running_syncs,
    SUM(posts_fetched) as total_posts_fetched,
    SUM(posts_created) as total_posts_created,
    SUM(posts_updated) as total_posts_updated,
    AVG(duration_seconds) as avg_duration_seconds,
    MAX(started_at) as last_sync_at,
    SUM(api_calls_made) as total_api_calls
FROM public.sync_operations
GROUP BY user_id;

-- Step 5: Create user sync status view
CREATE OR REPLACE VIEW public.user_sync_status AS
SELECT 
    u.id as user_id,
    u.email,
    COALESCE(ss.total_syncs, 0) as total_syncs,
    COALESCE(ss.successful_syncs, 0) as successful_syncs,
    COALESCE(ss.failed_syncs, 0) as failed_syncs,
    ss.last_sync_at,
    CASE 
        WHEN ss.running_syncs > 0 THEN 'syncing'
        WHEN ss.last_sync_at IS NULL THEN 'never_synced'
        WHEN ss.last_sync_at < now() - interval '24 hours' THEN 'needs_sync'
        ELSE 'up_to_date'
    END as sync_status,
    COUNT(pup.id) as total_posts,
    COUNT(pup.id) FILTER (WHERE pup.is_newly_added = true) as new_posts,
    COUNT(pup.id) FILTER (WHERE pup.is_updated = true) as updated_posts
FROM auth.users u
LEFT JOIN public.sync_statistics ss ON u.id = ss.user_id
LEFT JOIN public.public_users_posts pup ON u.id = pup.user_id
GROUP BY u.id, u.email, ss.total_syncs, ss.successful_syncs, ss.failed_syncs, ss.last_sync_at, ss.running_syncs;

-- Step 6: Create function to start a sync operation
CREATE OR REPLACE FUNCTION start_sync_operation(
    p_user_id uuid,
    p_operation_type text DEFAULT 'incremental_sync',
    p_sync_source text DEFAULT 'scheduled',
    p_sync_config jsonb DEFAULT '{}'::jsonb
) RETURNS uuid AS $$
DECLARE
    operation_id uuid;
BEGIN
    -- Check if user already has a running sync
    IF EXISTS (
        SELECT 1 FROM public.sync_operations 
        WHERE user_id = p_user_id AND status = 'running'
    ) THEN
        RAISE EXCEPTION 'User already has a running sync operation';
    END IF;
    
    -- Create new sync operation
    INSERT INTO public.sync_operations (
        user_id,
        operation_type,
        sync_source,
        sync_config
    ) VALUES (
        p_user_id,
        p_operation_type,
        p_sync_source,
        p_sync_config
    ) RETURNING id INTO operation_id;
    
    RETURN operation_id;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Create function to complete a sync operation
CREATE OR REPLACE FUNCTION complete_sync_operation(
    p_operation_id uuid,
    p_status text,
    p_posts_fetched integer DEFAULT 0,
    p_posts_created integer DEFAULT 0,
    p_posts_updated integer DEFAULT 0,
    p_posts_skipped integer DEFAULT 0,
    p_posts_failed integer DEFAULT 0,
    p_api_calls_made integer DEFAULT 0,
    p_error_message text DEFAULT NULL,
    p_error_details jsonb DEFAULT NULL
) RETURNS boolean AS $$
BEGIN
    UPDATE public.sync_operations SET
        completed_at = now(),
        status = p_status,
        posts_fetched = p_posts_fetched,
        posts_created = p_posts_created,
        posts_updated = p_posts_updated,
        posts_skipped = p_posts_skipped,
        posts_failed = p_posts_failed,
        api_calls_made = p_api_calls_made,
        error_message = p_error_message,
        error_details = p_error_details
    WHERE id = p_operation_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Step 8: Add helpful comments
COMMENT ON TABLE public.sync_operations IS 'Tracks LinkedIn post synchronization operations for monitoring and debugging';
COMMENT ON VIEW public.sync_statistics IS 'Aggregated sync statistics per user';
COMMENT ON VIEW public.user_sync_status IS 'Current sync status and post counts for all users';
COMMENT ON FUNCTION start_sync_operation IS 'Safely start a new sync operation for a user';
COMMENT ON FUNCTION complete_sync_operation IS 'Mark a sync operation as completed with results';
