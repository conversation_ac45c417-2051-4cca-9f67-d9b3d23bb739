import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle, Linkedin } from 'lucide-react';
import { LinkedInConnectionState, LinkedIn2FADetails, LinkedInCredentials } from '@/types/linkedin';
import { toast } from 'sonner';

interface LinkedInConnectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConnect: (credentials: LinkedInCredentials) => Promise<void>;
  onSubmit2FA: (code: string) => Promise<void>;
  connectionState: LinkedInConnectionState;
}

const credentialsSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

const twoFactorSchema = z.object({
  code: z.string().min(1, 'Please enter the verification code'),
});

const LinkedInConnectionDialog: React.FC<LinkedInConnectionDialogProps> = ({
  isOpen,
  onClose,
  onConnect,
  onSubmit2FA,
  connectionState,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const credentialsForm = useForm<z.infer<typeof credentialsSchema>>({
    resolver: zodResolver(credentialsSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const twoFactorForm = useForm<z.infer<typeof twoFactorSchema>>({
    resolver: zodResolver(twoFactorSchema),
    defaultValues: {
      code: '',
    },
  });

  const handleCredentialsSubmit = async (values: z.infer<typeof credentialsSchema>) => {
    try {
      setIsSubmitting(true);
      await onConnect(values);
    } catch (error) {
      console.error('Error connecting to LinkedIn:', error);
      toast.error('Failed to connect to LinkedIn');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTwoFactorSubmit = async (values: z.infer<typeof twoFactorSchema>) => {
    try {
      setIsSubmitting(true);
      await onSubmit2FA(values.code);
    } catch (error) {
      console.error('Error submitting 2FA code:', error);
      toast.error('Failed to verify 2FA code');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Linkedin className="h-5 w-5 text-[#0A66C2]" />
            Connect LinkedIn Account
          </DialogTitle>
          <DialogDescription>
            Enter your LinkedIn credentials to connect your account
          </DialogDescription>
        </DialogHeader>

        {connectionState.status === 'error' && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{connectionState.error}</AlertDescription>
          </Alert>
        )}

        {connectionState.status !== '2fa_required' ? (
          <Form {...credentialsForm}>
            <form onSubmit={credentialsForm.handleSubmit(handleCredentialsSubmit)} className="space-y-4">
              <FormField
                control={credentialsForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>LinkedIn Email</FormLabel>
                    <FormControl>
                      <Input {...field} type="email" placeholder="Enter your LinkedIn email" autoComplete="email" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={credentialsForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>LinkedIn Password</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" placeholder="Enter your password" autoComplete="current-password" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter className="mt-6">
                <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Connecting...
                    </>
                  ) : (
                    'Connect'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        ) : (
          <Form {...twoFactorForm}>
            <form onSubmit={twoFactorForm.handleSubmit(handleTwoFactorSubmit)} className="space-y-4">
              <div className="bg-amber-50 border border-amber-200 p-4 rounded-md mb-4">
                <h4 className="font-medium text-amber-800">Two-Factor Authentication Required</h4>
                <p className="text-sm text-amber-700 mt-1">
                  {connectionState.twoFactorDetails?.message || 'Please enter the verification code sent to you'}
                </p>
              </div>

              <FormField
                control={twoFactorForm.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Verification Code</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder="Enter verification code"
                        maxLength={connectionState.twoFactorDetails?.codeLength || 6}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter className="mt-6">
                <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    'Submit Code'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default LinkedInConnectionDialog;
