import { Router } from 'express';
import {
    handleUnipileWebhook,
    handleCreateHostedAuth,
    handleCreateReconnectAuth,
    handleGetAccountByEmail,
    handleVerifyHostedAuthAccount,
    handleCheckAccountStatus,
    handleGetLatestLinkedInAccount,
    handleSaveLinkedInDataManually,
    handleGetLinkedInProfileForFrontend,
    handleReconnectLinkedInAccount,
    handleCompleteSignup
} from '../handlers/auth.handlers';

/**
 * Functional Auth Routes
 * Clean, specific endpoints for authentication operations
 */

const router = Router();

// Webhook endpoints
router.post('/webhook/unipile-auth', handleUnipileWebhook);           // POST /auth/webhook/unipile-auth

// Hosted auth endpoints
router.post('/hosted-auth/create', handleCreateHostedAuth);           // POST /auth/hosted-auth/create
router.post('/hosted-auth/reconnect', handleCreateReconnectAuth);     // POST /auth/hosted-auth/reconnect

// Account management endpoints
router.post('/account/email', handleGetAccountByEmail);              // POST /auth/account/email
router.post('/verify-account', handleVerifyHostedAuthAccount);        // POST /auth/verify-account
router.get('/account/status/:accountId', handleCheckAccountStatus);  // GET /auth/account/status/:accountId

// LinkedIn  endpoints
router.get('/linkedin/latest', handleGetLatestLinkedInAccount);       // GET /auth/linkedin/latest
router.post('/linkedin/save', handleSaveLinkedInDataManually);        // POST /auth/linkedin/save
router.get('/linkedin/profile/:accountId', handleGetLinkedInProfileForFrontend); // GET /auth/linkedin/profile/:accountId
router.post('/linkedin/reconnect', handleReconnectLinkedInAccount);   // POST /auth/linkedin/reconnect

// Signup endpoints
router.post('/signup/complete', handleCompleteSignup);               // POST /auth/signup/complete

export default router;
