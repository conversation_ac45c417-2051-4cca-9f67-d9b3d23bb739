import { useEffect, useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';

interface MetricChange {
  postId: string;
  metric: string;
  oldValue: number;
  newValue: number;
  timestamp: number;
}

interface AnimationState {
  [key: string]: boolean; // postId-metric -> isAnimating
}

export const useRealtimeAnimations = (userId?: string) => {
  const [animatingMetrics, setAnimatingMetrics] = useState<AnimationState>({});
  const [metricChanges, setMetricChanges] = useState<Record<string, MetricChange>>({});

  // Track which metrics are currently animating
  const isMetricAnimating = useCallback((postId: string, metric: string) => {
    return animatingMetrics[`${postId}-${metric}`] || false;
  }, [animatingMetrics]);

  // Get previous value for a metric
  const getPreviousValue = useCallback((postId: string, metric: string) => {
    const change = metricChanges[`${postId}-${metric}`];
    return change?.oldValue;
  }, [metricChanges]);

  // Start animation for a metric
  const startAnimation = useCallback((postId: string, metric: string, oldValue: number, newValue: number) => {
    const key = `${postId}-${metric}`;
    
    console.log(`🎬 Starting animation for ${key}: ${oldValue} → ${newValue}`);
    
    // Store the change
    setMetricChanges(prev => ({
      ...prev,
      [key]: {
        postId,
        metric,
        oldValue,
        newValue,
        timestamp: Date.now()
      }
    }));

    // Mark as animating
    setAnimatingMetrics(prev => ({
      ...prev,
      [key]: true
    }));

    // Stop animation after 3 seconds
    setTimeout(() => {
      setAnimatingMetrics(prev => ({
        ...prev,
        [key]: false
      }));
      
      // Clean up old changes after animation
      setTimeout(() => {
        setMetricChanges(prev => {
          const newChanges = { ...prev };
          delete newChanges[key];
          return newChanges;
        });
      }, 1000);
    }, 3000);
  }, []);

  // Set up Supabase real-time subscription
  useEffect(() => {
    if (!userId) return;

    console.log('🔄 Setting up real-time subscription for user:', userId);

    const channel = supabase
      .channel('public_users_posts_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'public_users_posts',
          filter: `user_id=eq.${userId}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          console.log('📡 Real-time update received:', payload);
          
          const oldRecord = payload.old;
          const newRecord = payload.new;
          
          if (!oldRecord || !newRecord) return;

          const postId = newRecord.id;
          const metricsToCheck = ['impressions', 'likes', 'comments', 'shares', 'engagement_rate'];

          // Check each metric for changes
          metricsToCheck.forEach(metric => {
            const oldValue = oldRecord[metric] || 0;
            const newValue = newRecord[metric] || 0;

            if (oldValue !== newValue) {
              console.log(`📊 Metric changed: ${metric} ${oldValue} → ${newValue} for post ${postId}`);
              startAnimation(postId, metric, oldValue, newValue);
            }
          });
        }
      )
      .subscribe((status) => {
        console.log('📡 Subscription status:', status);
      });

    return () => {
      console.log('🔄 Cleaning up real-time subscription');
      supabase.removeChannel(channel);
    };
  }, [userId, startAnimation]);

  // Manual trigger for testing
  const triggerTestAnimation = useCallback((postId: string) => {
    console.log('🧪 Triggering test animations for post:', postId);
    
    // Simulate metric changes
    const testChanges = [
      { metric: 'impressions', oldValue: 1000, newValue: 1150 },
      { metric: 'likes', oldValue: 50, newValue: 65 },
      { metric: 'comments', oldValue: 10, newValue: 15 },
      { metric: 'shares', oldValue: 5, newValue: 8 }
    ];

    testChanges.forEach(({ metric, oldValue, newValue }, index) => {
      setTimeout(() => {
        startAnimation(postId, metric, oldValue, newValue);
      }, index * 500); // Stagger animations
    });
  }, [startAnimation]);

  return {
    isMetricAnimating,
    getPreviousValue,
    triggerTestAnimation,
    animatingMetrics,
    metricChanges
  };
};

// Hook specifically for LinkedIn posts table
export const useLinkedInPostAnimations = (userId?: string, posts?: any[]) => {
  const {
    isMetricAnimating,
    getPreviousValue,
    triggerTestAnimation,
    animatingMetrics,
    metricChanges
  } = useRealtimeAnimations(userId);

  // Test function that uses the first available post
  const testAnimations = useCallback(() => {
    if (posts && posts.length > 0) {
      const firstPost = posts[0];
      triggerTestAnimation(firstPost.id);
    } else {
      console.log('❌ No posts available for testing');
    }
  }, [posts, triggerTestAnimation]);

  // Check if any metric in a post is animating (for row highlighting)
  const isPostAnimating = useCallback((postId: string) => {
    const metrics = ['impressions', 'likes', 'comments', 'shares', 'engagement_rate'];
    return metrics.some(metric => isMetricAnimating(postId, metric));
  }, [isMetricAnimating]);

  return {
    isMetricAnimating,
    getPreviousValue,
    testAnimations,
    isPostAnimating,
    animatingMetrics,
    metricChanges
  };
};
