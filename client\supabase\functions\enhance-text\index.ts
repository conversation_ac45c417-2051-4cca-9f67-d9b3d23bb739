
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Get the OpenAI API key from Supabase secrets
const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { text, enhancementType } = await req.json();
    
    if (!text || text.trim() === '') {
      return new Response(
        JSON.stringify({ error: 'Text content is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check if API key is available
    if (!openAIApiKey) {
      console.error('OpenAI API key is not defined');
      return new Response(
        JSON.stringify({ error: 'API key is not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Enhancing ${enhancementType}: "${text.substring(0, 50)}..."`);
    console.log(`API Key available: ${openAIApiKey ? 'Yes' : 'No'}`);

    // Create a system prompt based on the enhancement type
    let systemPrompt = 'You are a professional copywriter that enhances marketing text.';
    let userPrompt = '';
    
    switch (enhancementType) {
      case 'tone':
        systemPrompt += ' You improve brand tone of voice descriptions to be more professional and distinctive.';
        userPrompt = `Enhance this brand tone of voice description to be more professional and distinctive while maintaining its core meaning: "${text}"`;
        break;
      case 'description':
        systemPrompt += ' You improve company descriptions to be concise, compelling and professional.';
        userPrompt = `Improve this company description to be more concise, compelling and professional while maintaining its core meaning: "${text}"`;
        break;
      case 'audience':
        systemPrompt += ' You improve target audience descriptions to be more specific and actionable.';
        userPrompt = `Enhance this target audience description to be more specific, clear and actionable while maintaining its core meaning: "${text}"`;
        break;
      case 'action':
        systemPrompt += ' You improve action statements to be more compelling and clear.';
        userPrompt = `Enhance this audience action statement to be more compelling and clear while maintaining its core meaning: "${text}"`;
        break;
      case 'linkedin':
        systemPrompt += ' You create engaging LinkedIn posts for professionals.';
        userPrompt = text; // The text is already a well-formed prompt
        break;
      default:
        systemPrompt += ' You improve marketing text to be more professional and compelling.';
        userPrompt = `Enhance this text to be more professional and compelling while maintaining its core meaning: "${text}"`;
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API error:', errorData);
      return new Response(
        JSON.stringify({ error: 'Failed to enhance text', details: errorData }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const data = await response.json();
    const enhancedText = data.choices[0].message.content;
    
    console.log(`Enhanced result: "${enhancedText.substring(0, 50)}..."`);

    return new Response(
      JSON.stringify({ enhancedText }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error in enhance-text function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
