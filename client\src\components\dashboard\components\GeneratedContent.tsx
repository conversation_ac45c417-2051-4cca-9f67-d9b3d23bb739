
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Check, Copy, Edit, ThumbsUp, ThumbsDown, Send, Save } from 'lucide-react';
import { useContent } from '@/context/ContentContext';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

const GeneratedContent = () => {
  const { generatedContent, saveContent, saveContentAsDraft, activeBox } = useContent();
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const handleCopy = () => {
    navigator.clipboard.writeText(generatedContent);
    setCopied(true);
    toast({
      title: "Copied!",
      description: "LinkedIn post copied to clipboard"
    });
    setTimeout(() => setCopied(false), 2000);
  };

  // Determine button text based on post type
  const getButtonText = () => {
    return activeBox === 'personal' 
      ? 'Save Post' 
      : 'Save & Submit for Approval';
  };

  return (
    <Card className="shadow-lg border border-white/20 bg-white/40 backdrop-blur-sm animate-fade-in">
      <CardContent className="space-y-4 p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium flex items-center">
            <Check size={16} className="text-green-500 mr-1" />
            Generated LinkedIn post
          </h3>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleCopy} className="bg-white/70">
              {copied ? (
                <>
                  <Check size={14} className="mr-1" />
                  Copied
                </>
              ) : (
                <>
                  <Copy size={14} className="mr-1" />
                  Copy
                </>
              )}
            </Button>
            <Button variant="outline" size="sm" className="bg-white/70">
              <Edit size={14} className="mr-1" />
              Edit
            </Button>
          </div>
        </div>
        
        <div className="p-4 bg-white/70 rounded-md border border-gray-100 shadow-sm">
          <p className="text-sm whitespace-pre-line">{generatedContent}</p>
        </div>

        <div className="flex justify-between items-center pt-2">
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" className="flex items-center bg-white/70">
              <ThumbsUp size={14} className="mr-1" />
              Good
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => window.dispatchEvent(new CustomEvent('regenerate-content'))}
            >
              Regenerate
            </Button>
          </div>
          <div className="flex space-x-2">
            <Button 
              onClick={saveContentAsDraft} 
              variant="outline"
              className="bg-white shadow-sm hover:bg-gray-100"
            >
              <Save size={14} className="mr-1" />
              Save for Later
            </Button>
            <Button 
              onClick={saveContent} 
              className="bg-gradient-to-r from-enterprise-teal to-teal-400 hover:from-enterprise-teal/90 hover:to-teal-500 text-white shadow-md hover:shadow-lg transition-all"
            >
              <Send size={14} className="mr-1" />
              {getButtonText()}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GeneratedContent;
