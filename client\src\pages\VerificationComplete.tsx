import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { CheckCircle, XCircle, Loader2, Clock, Shield } from 'lucide-react';
import { getApiUrl } from '@/config/env';

const VerificationComplete: React.FC = () => {
  const [searchParams] = useSearchParams();
  const status = searchParams.get('status');
  const type = searchParams.get('type');
  const userId = searchParams.get('user');

  const [verificationStage, setVerificationStage] = useState<'checking' | 'completed' | 'failed'>('checking');
  const [accountId, setAccountId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('🔄 VerificationComplete page loaded with:', { status, type, userId });

    if (status === 'success' && userId) {
      // Hosted auth completed - now verify the account was actually created
      console.log('✅ Hosted auth completed, verifying account creation...');
      setVerificationStage('checking');

      // Start verification process
      verifyAccountCreation();
    } else if (status === 'error') {
      setVerificationStage('failed');
      setError('LinkedIn authentication failed');
      // For errors, auto-close after showing message
      setTimeout(() => {
        handleClose('error');
      }, 3000);
    } else {
      setVerificationStage('failed');
      setError('Missing required parameters');
    }
  }, [status, type, userId]);

  // Verify that the account was actually created via webhook
  const verifyAccountCreation = async () => {
    console.log('🔍 Starting account verification for user:', userId);

    // First, try immediate check by email
    try {
      console.log('🚀 Trying immediate account check by email...');
      const immediateResponse = await fetch(getApiUrl('/auth/get-account-by-email'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: userId }),
      });

      const immediateData = await immediateResponse.json();
      console.log('🔍 Immediate account check response:', immediateData);

      if (immediateResponse.ok && immediateData.success && immediateData.account_id) {
        // Account found immediately!
        console.log('✅ Account found immediately:', immediateData.account_id);
        setAccountId(immediateData.account_id);
        setVerificationStage('completed');

        console.log('📤 Sending account ID to parent window:', immediateData.account_id);
        handleClose('success', immediateData.account_id);
        return;
      }
    } catch (immediateError) {
      console.log('⚠️ Immediate check failed, falling back to polling:', immediateError);
    }

    // If immediate check fails, fall back to polling
    console.log('🔄 Starting polling for account creation...');
    const maxAttempts = 20; // 1 minute (3 second intervals)
    let attempts = 0;

    const pollForAccount = async (): Promise<void> => {
      try {
        attempts++;
        console.log(`📊 Account verification attempt ${attempts}/${maxAttempts}`);

        const response = await fetch(getApiUrl('/auth/verify-hosted-auth-account'), {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            internalUserId: userId,
            userEmail: userId
          }),
        });

        const data = await response.json();
        console.log('🔍 Account verification response:', data);

        if (response.ok && data.success && data.account_id) {
          // Account successfully created and verified
          console.log('✅ LinkedIn account verified successfully:', data.account_id);
          setAccountId(data.account_id);
          setVerificationStage('completed');

          // Immediately notify parent with account ID
          console.log('📤 Sending account ID to parent window:', data.account_id);
          handleClose('success', data.account_id);
          return;
        }

        if (attempts >= maxAttempts) {
          // Timeout reached
          console.log('⏰ Account verification timeout reached');
          setVerificationStage('failed');
          setError('Account verification timeout. Please try again.');
          setTimeout(() => {
            handleClose('timeout');
          }, 3000);
          return;
        }

        // Wait 3 seconds before next attempt
        console.log(`⏳ Waiting 3 seconds before next verification attempt...`);
        setTimeout(pollForAccount, 3000);

      } catch (error: any) {
        console.error('💥 Error during account verification:', error);

        if (attempts >= maxAttempts) {
          setVerificationStage('failed');
          setError('Account verification failed due to network error.');
          setTimeout(() => {
            handleClose('error');
          }, 3000);
          return;
        }

        // Retry on error
        setTimeout(pollForAccount, 3000);
      }
    };

    // Start polling
    pollForAccount();
  };

  const handleClose = (finalStatus: string, accountId?: string) => {
    try {
      // If this is a popup window, close it and notify parent
      if (window.opener && !window.opener.closed) {
        console.log('📤 Sending verification completion message to parent:', {
          type: 'VERIFICATION_COMPLETE',
          status: finalStatus,
          verificationType: type,
          accountId: accountId,
          userAction: 'auto_verified'
        });

        // Send message to parent window with error handling
        try {
          window.opener.postMessage({
            type: 'VERIFICATION_COMPLETE',
            status: finalStatus,
            verificationType: type,
            accountId: accountId,
            userAction: 'auto_verified',
            timestamp: new Date().toISOString()
          }, window.location.origin);
        } catch (postMessageError) {
          console.warn('⚠️ Failed to send message to parent window:', postMessageError);
          // This is expected if parent window is closed/navigated away
        }

        // Wait a bit for the message to be sent, then close
        setTimeout(() => {
          console.log('🔒 Closing popup window');
          window.close();
        }, 500);
      } else {
        console.log('⚠️ No opener window found, redirecting to login');
        // If not a popup, redirect to login page
        setTimeout(() => {
          window.location.href = '/login';
        }, 2000);
      }
    } catch (error) {
      console.error('💥 Error handling verification redirect:', error);
      // Fallback: redirect to login
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);
    }
  };

  const getStatusIcon = () => {
    if (verificationStage === 'checking') {
      return <Loader2 className="h-16 w-16 text-blue-500 animate-spin" />;
    } else if (verificationStage === 'completed') {
      return <CheckCircle className="h-16 w-16 text-green-500" />;
    } else if (verificationStage === 'failed') {
      return <XCircle className="h-16 w-16 text-red-500" />;
    } else {
      return <Shield className="h-16 w-16 text-gray-500" />;
    }
  };

  const getStatusMessage = () => {
    if (verificationStage === 'checking') {
      return {
        title: 'Verifying Account Creation',
        message: 'LinkedIn verification completed! Now checking that your account was successfully created and is accessible...',
        color: 'text-blue-700'
      };
    } else if (verificationStage === 'completed') {
      return {
        title: 'Account Verified Successfully!',
        message: `Your LinkedIn account has been created and verified. Account ID: ${accountId}`,
        color: 'text-green-700'
      };
    } else if (verificationStage === 'failed') {
      return {
        title: 'Verification Failed',
        message: error || 'There was an issue with the LinkedIn verification. Please try again.',
        color: 'text-red-700'
      };
    } else {
      return {
        title: 'Processing...',
        message: 'Processing your verification request...',
        color: 'text-gray-700'
      };
    }
  };

  const statusInfo = getStatusMessage();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-lg w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="flex justify-center mb-6">
          {getStatusIcon()}
        </div>

        <h1 className={`text-2xl font-bold mb-4 ${statusInfo.color}`}>
          {statusInfo.title}
        </h1>

        <p className="text-gray-600 mb-6">
          {statusInfo.message}
        </p>

        {/* Verification Status */}
        {verificationStage === 'checking' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center gap-3">
              <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
              <span className="text-blue-800 font-medium">
                Checking webhook callback and account creation...
              </span>
            </div>
            <p className="text-sm text-blue-700 mt-2 text-center">
              This may take up to 1 minute. Please wait...
            </p>
          </div>
        )}

        {verificationStage === 'completed' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-green-800 font-medium">
                Account verified successfully!
              </span>
            </div>
            <p className="text-sm text-green-700 mt-2 text-center">
              Closing window and returning to the application...
            </p>
          </div>
        )}

        {verificationStage === 'failed' && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center gap-3">
              <XCircle className="h-5 w-5 text-red-600" />
              <span className="text-red-800 font-medium">
                Verification failed
              </span>
            </div>
            <p className="text-sm text-red-700 mt-2 text-center">
              {error}
            </p>
          </div>
        )}

        {/* Status Messages */}
        <div className="text-sm text-gray-500 text-center">
          {verificationStage === 'checking' ? (
            <p>Automatically verifying your LinkedIn account creation...</p>
          ) : verificationStage === 'completed' ? (
            <p>Verification complete! Returning to the application...</p>
          ) : verificationStage === 'failed' ? (
            <p>Verification failed. Please try again.</p>
          ) : (
            <p>Please wait...</p>
          )}
        </div>

        {/* Manual redirect button as fallback */}
        <div className="mt-6">
          <button
            onClick={() => window.location.href = '/login'}
            className="text-blue-600 hover:text-blue-800 underline text-sm"
          >
            Click here if you're not redirected automatically
          </button>
        </div>
      </div>
    </div>
  );
};

export default VerificationComplete;
