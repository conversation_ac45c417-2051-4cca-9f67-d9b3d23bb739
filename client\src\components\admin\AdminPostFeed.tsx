
import React, { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { MoreVertical, Edit, Trash } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationNext, PaginationPrevious, PaginationLink } from "@/components/ui/pagination"
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

const AdminPostFeed = () => {
  const [posts, setPosts] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [postsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    fetchPosts();
  }, [currentPage, searchQuery]);

  const fetchPosts = async () => {
    try {
      let query = supabase
        .from('employee_posts')
        .select(`
          *,
          user:user_id (
            first_name,
            last_name,
            email,
            department
          )
        `)
        .order('created_at', { ascending: false });

      if (searchQuery) {
        query = query.ilike('title', `%${searchQuery}%`);
      }

      const { data, error } = await query
        .range((currentPage - 1) * postsPerPage, currentPage * postsPerPage - 1);

      if (error) {
        console.error('Error fetching posts:', error);
      } else {
        setPosts(data || []);
      }
    } catch (error) {
      console.error('Error fetching posts:', error);
    }
  };

  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = posts.slice(indexOfFirstPost, indexOfLastPost);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Helper function to safely get user display name
  const getUserDisplayName = (post: any) => {
    if (!post.user) return 'Unknown User';
    
    const firstName = post.user.first_name || '';
    const lastName = post.user.last_name || '';
    
    if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim();
    }
    
    return post.user.email || 'Unknown User';
  };

  // Helper function to safely get user department
  const getUserDepartment = (post: any) => {
    return post.user?.department || 'N/A';
  };

  // Helper function to safely get user avatar text
  const getUserAvatarText = (post: any) => {
    if (!post.user) return 'U';
    
    const firstName = post.user.first_name || '';
    const lastName = post.user.last_name || '';
    
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`;
    } else if (firstName) {
      return firstName.charAt(0);
    } else if (lastName) {
      return lastName.charAt(0);
    } else if (post.user.email) {
      return post.user.email.charAt(0).toUpperCase();
    }
    
    return 'U';
  };

  return (
    <div>
      <div className="mb-4">
        <Input
          type="text"
          placeholder="Search posts..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      <Table>
        <TableCaption>A list of your recent posts.</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">Author</TableHead>
            <TableHead>Title</TableHead>
            <TableHead>Department</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentPosts.map((post: any) => (
            <TableRow key={post.id}>
              <TableCell className="font-medium">
                <div className="flex items-center space-x-2">
                  <Avatar>
                    <AvatarImage src={`https://avatar.vercel.sh/${getUserAvatarText(post)}.png`} />
                    <AvatarFallback>{getUserAvatarText(post)}</AvatarFallback>
                  </Avatar>
                  <span>{getUserDisplayName(post)}</span>
                </div>
              </TableCell>
              <TableCell>{post.title}</TableCell>
              <TableCell>{getUserDepartment(post)}</TableCell>
              <TableCell>
                <Badge variant="secondary">{post.status}</Badge>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem>
                      <Edit className="mr-2 h-4 w-4" /> Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Trash className="mr-2 h-4 w-4" /> Delete
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>View Post</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={5}>
              <Pagination>
                <PaginationContent>
                  <PaginationPrevious href="#" />
                  <PaginationItem>
                    <PaginationLink href="#" onClick={() => paginate(1)} isActive={currentPage === 1}>1</PaginationLink>
                  </PaginationItem>
                  {currentPage > 2 && <PaginationEllipsis />}
                  {currentPage > 1 && (
                    <PaginationItem>
                      <PaginationLink href="#" onClick={() => paginate(currentPage - 1)}>{currentPage - 1}</PaginationLink>
                    </PaginationItem>
                  )}
                  <PaginationItem>
                    <PaginationLink href="#" onClick={() => paginate(currentPage)} isActive>{currentPage}</PaginationLink>
                  </PaginationItem>
                  {currentPage < Math.ceil(posts.length / postsPerPage) && (
                    <PaginationItem>
                      <PaginationLink href="#" onClick={() => paginate(currentPage + 1)}>{currentPage + 1}</PaginationLink>
                    </PaginationItem>
                  )}
                  {currentPage < Math.ceil(posts.length / postsPerPage) - 1 && <PaginationEllipsis />}
                  <PaginationItem>
                    <PaginationLink
                      href="#"
                      onClick={() => paginate(Math.ceil(posts.length / postsPerPage))}
                      isActive={currentPage === Math.ceil(posts.length / postsPerPage)}
                    >
                      {Math.ceil(posts.length / postsPerPage)}
                    </PaginationLink>
                  </PaginationItem>
                  <PaginationNext href="#" />
                </PaginationContent>
              </Pagination>
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
};

export default AdminPostFeed;
