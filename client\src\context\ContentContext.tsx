
import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { BrandSettings } from '@/types/settings';

type ContentType = 'text' | 'article' | 'youtube';
type ContentBoxType = 'company' | 'personal' | null;

interface ContentContextType {
  // State
  activeBox: ContentBoxType;
  contentIdea: string;
  contentTone: string;
  contentType: ContentType;
  contentUrl: string;
  generatedContent: string;
  isGenerating: boolean;
  
  // Actions
  setActiveBox: (box: ContentBoxType) => void;
  setContentIdea: (idea: string) => void;
  setContentTone: (tone: string) => void;
  setContentType: (type: ContentType) => void;
  setContentUrl: (url: string) => void;
  generateContent: () => void;
  saveContent: () => void;
  saveContentAsDraft: () => void;
}

const ContentContext = createContext<ContentContextType | undefined>(undefined);

export const ContentProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [activeBox, setActiveBox] = useState<ContentBoxType>(null);
  const [contentIdea, setContentIdea] = useState('');
  const [generatedContent, setGeneratedContent] = useState('');
  const [contentTone, setContentTone] = useState('professional');
  const [contentType, setContentType] = useState<ContentType>('text'); // default to 'text'
  const [contentUrl, setContentUrl] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();
  const { user, profile } = useAuth();

  // For regenerate content events
  React.useEffect(() => {
    const handleRegenerate = () => {
      generateContent();
    };
    window.addEventListener('regenerate-content', handleRegenerate);
    return () => {
      window.removeEventListener('regenerate-content', handleRegenerate);
    };
  }, [contentIdea, contentTone]); // eslint-disable-line react-hooks/exhaustive-deps

  const generateContent = async () => {
    if (!contentIdea.trim()) return;
    
    setIsGenerating(true);
    
    try {
      // Create a prompt based on the content idea and tone
      const prompt = `Write a professional LinkedIn post about: ${contentIdea}. 
                     Use a ${contentTone} tone. 
                     Make it engaging and include relevant hashtags at the end.`;
                     
      const { data, error } = await supabase.functions.invoke('enhance-text', {
        body: { 
          text: prompt,
          enhancementType: 'linkedin'
        }
      });

      if (error) {
        console.error('Error generating LinkedIn post:', error);
        toast({
          title: "Generation failed",
          description: "There was an error generating your LinkedIn post. Please try again.",
          variant: "destructive"
        });
        return;
      }

      if (data.error) {
        console.error('API error:', data.error);
        toast({
          title: "Generation failed",
          description: data.error || "There was an error generating your LinkedIn post. Please try again.",
          variant: "destructive"
        });
        return;
      }

      // Set the generated content
      setGeneratedContent(data.enhancedText);
      
      toast({
        title: "Post generated",
        description: "Your LinkedIn post has been generated successfully."
      });
    } catch (error) {
      console.error('Exception generating LinkedIn post:', error);
      toast({
        title: "Generation failed",
        description: "There was an error generating your LinkedIn post. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Helper function to get user's department
  const getUserDepartment = async () => {
    if (!user) return 'other';
    
    try {
      const { data: memberData } = await supabase
        .from('company_members')
        .select('department')
        .eq('user_id', user.id)
        .single();
      
      return memberData?.department || 'other';
    } catch (error) {
      console.error('Error fetching user department:', error);
      return 'other';
    }
  };

  // Function to save post with specified status
  const savePostWithStatus = async (status: 'draft' | 'pending_approval' | 'published') => {
    if (!generatedContent || !user) return;
    
    try {
      const department = await getUserDepartment();
      
      // Save post to employee_posts table
      const { data, error } = await supabase
        .from('employee_posts')
        .insert({
          user_id: user.id,
          title: contentIdea.substring(0, 100), // Use content idea as title with max length
          content: generatedContent,
          post_type: activeBox || 'personal', // Default to personal if null
          department: department as any, // Cast to match enum type
          status: status
        });
      
      if (error) {
        console.error('Error saving post:', error);
        toast({
          title: "Save failed",
          description: "There was an error saving your post. Please try again.",
          variant: "destructive"
        });
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Exception saving post:', error);
      toast({
        title: "Save failed",
        description: "There was an error saving your post. Please try again.",
        variant: "destructive"
      });
      return false;
    }
  };

  const saveContent = async () => {
    if (!generatedContent || !user) return;
    
    try {
      // Fetch approval settings to determine if post should be auto-approved or pending
      const { data: brandingData } = await supabase
        .from('company_branding')
        .select('brand_colors')
        .eq('company_id', profile?.id)
        .single();
      
      // Check if admin approvals are enabled - safely handle JSON data
      let adminApprovalsEnabled = true; // Default to requiring approval
      
      if (brandingData?.brand_colors) {
        try {
          // Handle brand_colors as both string and object
          const brandColors: BrandSettings = typeof brandingData.brand_colors === 'string' 
            ? JSON.parse(brandingData.brand_colors) 
            : brandingData.brand_colors as BrandSettings;
            
          adminApprovalsEnabled = brandColors.adminApprovalsEnabled !== undefined 
            ? brandColors.adminApprovalsEnabled 
            : true;
        } catch (e) {
          console.error("Error parsing brand colors JSON:", e);
        }
      }
      
      // Determine post status based on settings and post type
      let status: 'published' | 'pending_approval' = 'published';
      if (activeBox === 'company' && adminApprovalsEnabled) {
        status = 'pending_approval';
      }
      
      const success = await savePostWithStatus(status);
      
      if (!success) return;
      
      toast({
        title: status === 'pending_approval' ? "Post submitted for approval" : "Post saved",
        description: status === 'pending_approval' 
          ? "Your post has been submitted for admin approval." 
          : "Your post has been saved successfully."
      });
      
      // Reset content after successful save
      setContentIdea('');
      setGeneratedContent('');
      setActiveBox(null);
      
    } catch (error) {
      console.error('Exception saving post:', error);
      toast({
        title: "Save failed",
        description: "There was an error saving your post. Please try again.",
        variant: "destructive"
      });
    }
  };

  const saveContentAsDraft = async () => {
    if (!generatedContent || !user) return;
    
    const success = await savePostWithStatus('draft');
    
    if (!success) return;
    
    toast({
      title: "Post saved for later",
      description: "Your post has been saved and can be accessed from your dashboard."
    });
    
    // Reset content after successful save
    setContentIdea('');
    setGeneratedContent('');
    setActiveBox(null);
  };

  const value = {
    activeBox,
    contentIdea,
    contentTone,
    contentType,
    contentUrl,
    generatedContent,
    isGenerating,
    setActiveBox,
    setContentIdea,
    setContentTone,
    setContentType,
    setContentUrl,
    generateContent,
    saveContent,
    saveContentAsDraft
  };

  return <ContentContext.Provider value={value}>{children}</ContentContext.Provider>;
};

export const useContent = (): ContentContextType => {
  const context = useContext(ContentContext);
  if (context === undefined) {
    throw new Error('useContent must be used within a ContentProvider');
  }
  return context;
};
