
-- Enable Row Level Security
ALTER TABLE IF EXISTS public.company_branding ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own company branding" ON public.company_branding;
DROP POLICY IF EXISTS "Users can insert their own company branding" ON public.company_branding;
DROP POLICY IF EXISTS "Users can update their own company branding" ON public.company_branding;
DROP POLICY IF EXISTS "Users can delete their own company branding" ON public.company_branding;

-- Create new policy to allow users to select their company's branding
CREATE POLICY "Users can view their company branding"
  ON public.company_branding
  FOR SELECT
  USING (
    company_id IN (
      SELECT company_id FROM public.company_members WHERE user_id = auth.uid()
    ) OR
    company_ref_id IN (
      SELECT company_id FROM public.company_members WHERE user_id = auth.uid()
    )
  );

-- Create policy to allow admins to insert company branding
CREATE POLICY "Admins can insert company branding"
  ON public.company_branding
  FOR INSERT
  WITH CHECK (
    company_id IN (
      SELECT company_id FROM public.company_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    ) OR
    company_ref_id IN (
      SELECT company_id FROM public.company_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Create policy to allow admins to update company branding
CREATE POLICY "Admins can update company branding"
  ON public.company_branding
  FOR UPDATE
  USING (
    company_id IN (
      SELECT company_id FROM public.company_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    ) OR
    company_ref_id IN (
      SELECT company_id FROM public.company_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Create policy to allow admins to delete company branding
CREATE POLICY "Admins can delete company branding"
  ON public.company_branding
  FOR DELETE
  USING (
    company_id IN (
      SELECT company_id FROM public.company_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    ) OR
    company_ref_id IN (
      SELECT company_id FROM public.company_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );
