
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';

export interface LinkedInPostExample {
  id: string;
  subject: string;
  content: string;
}

interface UseLinkedInExamplesProps {
  initialExamples: LinkedInPostExample[];
  onExamplesChange: (examples: LinkedInPostExample[]) => void;
}

export const useLinkedInExamples = ({ 
  initialExamples, 
  onExamplesChange 
}: UseLinkedInExamplesProps) => {
  const { toast } = useToast();
  const [examples, setExamples] = useState<LinkedInPostExample[]>(initialExamples);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentExample, setCurrentExample] = useState<LinkedInPostExample | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // <PERSON>le adding a new example dialog
  const handleAddExample = () => {
    setCurrentExample({ id: '', subject: '', content: '' });
    setIsEditing(false);
    setDialogOpen(true);
  };

  // Handle editing an example
  const handleEditExample = (example: LinkedInPostExample) => {
    setCurrentExample(example);
    setIsEditing(true);
    setDialogOpen(true);
  };

  // Handle subject change in dialog
  const handleExampleSubjectChange = (value: string) => {
    if (currentExample) {
      setCurrentExample({...currentExample, subject: value});
    }
  };

  // Handle content change in dialog
  const handleExampleContentChange = (value: string) => {
    if (currentExample) {
      setCurrentExample({...currentExample, content: value});
    }
  };

  // Handle saving an example (add or update)
  const handleSaveExample = () => {
    if (!currentExample?.subject || !currentExample?.content) {
      toast({
        title: "Error",
        description: "Both subject and content are required.",
        variant: "destructive"
      });
      return;
    }

    let updatedExamples: LinkedInPostExample[];
    
    if (isEditing) {
      updatedExamples = examples.map(example => 
        example.id === currentExample.id ? currentExample : example
      );
      toast({
        title: "Example updated",
        description: "Your LinkedIn post example has been updated."
      });
    } else {
      updatedExamples = [
        ...examples, 
        { ...currentExample, id: Date.now().toString() }
      ];
      toast({
        title: "Example added",
        description: "Your LinkedIn post example has been added."
      });
    }
    
    setExamples(updatedExamples);
    onExamplesChange(updatedExamples);
    setDialogOpen(false);
    setCurrentExample(null);
    setIsEditing(false);
  };

  // Handle deleting an example
  const handleDeleteExample = (id: string) => {
    const updatedExamples = examples.filter(example => example.id !== id);
    setExamples(updatedExamples);
    onExamplesChange(updatedExamples);
    toast({
      title: "Example deleted",
      description: "Your LinkedIn post example has been deleted."
    });
  };

  return {
    examples,
    dialogOpen,
    currentExample,
    isEditing,
    setDialogOpen,
    handleAddExample,
    handleEditExample,
    handleExampleSubjectChange,
    handleExampleContentChange,
    handleSaveExample,
    handleDeleteExample
  };
};
