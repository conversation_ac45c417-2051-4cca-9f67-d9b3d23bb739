-- Phase 1: Database Migration Runner
-- Run these migrations in order to set up the LinkedIn post sync system

-- Migration 1: Create custom types
\echo 'Running Migration 1: Creating custom types...'
\i 000_create_custom_types.sql

-- Migration 2: Fix and enhance public_users_posts table
\echo 'Running Migration 2: Setting up public_users_posts table...'
\i 001_fix_public_users_posts_schema.sql

-- Migration 3: Create sync tracking system (OPTIONAL - Skip for now)
-- \echo 'Running Migration 3: Creating sync tracking system...'
-- \i 002_create_sync_tracking_table.sql

-- Verification queries
\echo 'Verifying migrations...'

-- Check if tables exist
SELECT
    schemaname,
    tablename,
    tableowner
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('public_users_posts')
ORDER BY tablename;

-- Check if types exist
SELECT
    typname,
    typtype
FROM pg_type
WHERE typname IN ('post_type', 'department', 'user_role')
ORDER BY typname;

-- Check if indexes exist
SELECT
    indexname,
    tablename,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
AND tablename IN ('public_users_posts')
ORDER BY tablename, indexname;

-- Check if functions exist
SELECT
    proname,
    pronargs
FROM pg_proc
WHERE proname IN (
    'calculate_engagement_rate',
    'update_updated_at_column'
)
ORDER BY proname;

-- Check if views exist (sync tracking views skipped for now)
-- SELECT
--     viewname,
--     definition
-- FROM pg_views
-- WHERE schemaname = 'public'
-- AND viewname IN ('sync_statistics', 'user_sync_status')
-- ORDER BY viewname;

\echo 'Migration verification complete!'
\echo 'Phase 1: Database Setup & Schema (Core Tables Only) - COMPLETED ✅'
\echo 'Note: Sync tracking system (002_create_sync_tracking_table.sql) skipped for now'
