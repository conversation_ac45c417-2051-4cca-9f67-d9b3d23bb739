// Phase 2: Post Storage Service
// Handles storing LinkedIn posts in Supabase with deduplication and analytics

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || '';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    throw new Error('Missing Supabase configuration. Please check SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.');
}

// Create Supabase client with service role key for server-side operations
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

export interface LinkedInPostData {
    id: string; // LinkedIn post ID
    social_id?: string; // Alternative LinkedIn ID
    text?: string;
    content?: string;
    type?: string;
    created_at?: string;
    published_at?: string;
    author?: {
        id?: string;
        name?: string;
        username?: string;
    };
    metrics?: {
        likes?: number;
        comments?: number;
        shares?: number;
        impressions?: number;
    };
    reactions?: any[];
    comments?: any[];
    url?: string;
}

export interface StoredPost {
    id: string;
    user_id: string;
    linkedin_post_id: string;
    linkedin_post_url?: string;
    title: string;
    content: string;
    post_type: string;
    department: string;
    impressions: number;
    likes: number;
    comments: number;
    shares: number;
    engagement_rate: number;
    post_date: string;
    last_synced_at: string;
    sync_source: string;
    is_newly_added: boolean;
    is_updated: boolean;
    sync_status: string;
    status: string;
}

/**
 * Store a single LinkedIn post in Supabase
 */
export async function storeLinkedInPost(
    userId: string,
    postData: LinkedInPostData,
    department: string = 'other'
): Promise<{ success: boolean; post?: StoredPost; error?: string; isNew?: boolean }> {
    try {
        console.log('📝 Storing LinkedIn post:', {
            userId,
            postId: postData.id,
            postType: postData.type,
            hasMetrics: !!postData.metrics
        });

        // Extract and clean post data
        const linkedinPostId = postData.social_id || postData.id || '';
        const postContent = postData.text || postData.content || '';
        const postTitle = extractPostTitle(postContent);
        const postType = mapLinkedInPostType(postData.type);
        const postDate = postData.published_at || postData.created_at || new Date().toISOString();
        const postUrl = postData.url || `https://linkedin.com/posts/${linkedinPostId}`;

        // Extract metrics
        // console.log("⁉️⁉️⁉️Post Data Before Saving", postData);
        const metrics = postData.metrics || {};
        const likes = metrics.likes || (postData.reactions?.length || 0);
        const comments = metrics.comments || (postData.comments?.length || 0);
        const shares = metrics.shares || 0;
        const impressions = metrics.impressions || 0;

        if (!linkedinPostId) {
            throw new Error('LinkedIn post ID is required');
        }

        // Check if post already exists
        const { data: existingPost, error: checkError } = await supabase
            .from('public_users_posts')
            .select('*')
            .eq('linkedin_post_id', linkedinPostId)
            .eq('user_id', userId)
            .single();

        if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
            throw new Error(`Error checking existing post: ${checkError.message}`);
        }

        const now = new Date().toISOString();
        const isNewPost = !existingPost;

        const postRecord = {
            user_id: userId,
            linkedin_post_id: linkedinPostId,
            linkedin_post_url: postUrl,
            title: postTitle,
            content: postContent,
            post_type: postType,
            department: department,
            impressions: impressions,
            likes: likes,
            comments: comments,
            shares: shares,
            post_date: postDate,
            last_synced_at: now,
            sync_source: 'unipile',
            is_newly_added: isNewPost,
            is_updated: !isNewPost,
            sync_status: 'synced',
            status: 'published'
        };

        let result;
        if (isNewPost) {
            // Insert new post
            console.log('➕ Inserting new post:', linkedinPostId);
            const { data, error } = await supabase
                .from('public_users_posts')
                .insert([postRecord])
                .select()
                .single();

            if (error) {
                throw new Error(`Error inserting post: ${error.message}`);
            }
            result = data;
        } else {
            // Update existing post
            console.log('🔄 Updating existing post:', linkedinPostId);
            const { data, error } = await supabase
                .from('public_users_posts')
                .update({
                    ...postRecord,
                    is_newly_added: false, // Keep original newly_added status
                    is_updated: true
                })
                .eq('linkedin_post_id', linkedinPostId)
                .eq('user_id', userId)
                .select()
                .single();

            if (error) {
                throw new Error(`Error updating post: ${error.message}`);
            }
            result = data;
        }

        console.log(`✅ Post ${isNewPost ? 'stored' : 'updated'} successfully:`, {
            postId: linkedinPostId,
            title: postTitle.substring(0, 50) + '...',
            metrics: { likes, comments, shares, impressions }
        });

        return {
            success: true,
            post: result,
            isNew: isNewPost
        };

    } catch (error: any) {
        console.error('❌ Error storing LinkedIn post:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Store multiple LinkedIn posts in batch
 */
export async function storeLinkedInPosts(
    userId: string,
    posts: LinkedInPostData[],
    department: string = 'other'
): Promise<{
    success: boolean;
    results: {
        total: number;
        stored: number;
        updated: number;
        failed: number;
        errors: string[];
    };
}> {
    console.log(`📦 Batch storing ${posts.length} LinkedIn posts for user:`, userId);

    const results = {
        total: posts.length,
        stored: 0,
        updated: 0,
        failed: 0,
        errors: [] as string[]
    };

    for (const post of posts) {
        try {
            const result = await storeLinkedInPost(userId, post, department);
            
            if (result.success) {
                if (result.isNew) {
                    results.stored++;
                } else {
                    results.updated++;
                }
            } else {
                results.failed++;
                if (result.error) {
                    results.errors.push(`Post ${post.id}: ${result.error}`);
                }
            }
        } catch (error: any) {
            results.failed++;
            results.errors.push(`Post ${post.id}: ${error.message}`);
        }
    }

    console.log('📊 Batch storage results:', results);

    return {
        success: results.failed === 0,
        results
    };
}

/**
 * Get stored posts for a user
 */
export async function getUserPosts(
    userId: string,
    limit: number = 50,
    offset: number = 0
): Promise<{ success: boolean; posts?: StoredPost[]; error?: string; total?: number }> {
    try {
        const { data, error, count } = await supabase
            .from('public_users_posts')
            .select('*', { count: 'exact' })
            .eq('user_id', userId)
            .order('post_date', { ascending: false })
            .range(offset, offset + limit - 1);

        if (error) {
            throw new Error(`Error fetching posts: ${error.message}`);
        }

        return {
            success: true,
            posts: data || [],
            total: count || 0
        };
    } catch (error: any) {
        console.error('❌ Error fetching user posts:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Helper function to extract post title from content
 */
function extractPostTitle(content: string): string {
    if (!content) return 'Untitled Post';
    
    // Take first line or first 100 characters, whichever is shorter
    const firstLine = content.split('\n')[0];
    const title = firstLine.length > 100 ? firstLine.substring(0, 97) + '...' : firstLine;
    
    return title || 'Untitled Post';
}

/**
 * Mark posts as deleted on LinkedIn (keep for history tracking)
 * 4. POST UPDATES HANDLING - If deleted → DO NOT delete from Supabase. Keep for history tracking.
 */
export async function markPostsAsDeletedOnLinkedIn(
    userId: string,
    linkedinPostIds: string[]
): Promise<{ success: boolean; updated: number; error?: string }> {
    try {
        console.log(`📝 Marking ${linkedinPostIds.length} posts as deleted on LinkedIn for user:`, userId);

        const { data, error } = await supabase
            .from('public_users_posts')
            .update({
                status: 'deleted_on_linkedin',
                updated_at: new Date().toISOString(),
                is_updated: true
            })
            .eq('user_id', userId)
            .in('linkedin_post_id', linkedinPostIds)
            .select();

        if (error) {
            throw new Error(`Error marking posts as deleted: ${error.message}`);
        }

        console.log(`✅ Marked ${data?.length || 0} posts as deleted on LinkedIn`);

        return {
            success: true,
            updated: data?.length || 0
        };
    } catch (error: any) {
        console.error('❌ Error marking posts as deleted:', error);
        return {
            success: false,
            updated: 0,
            error: error.message
        };
    }
}

/**
 * Helper function to map LinkedIn post types to our enum
 * Current enum values: "personal" | "company"
 */
function mapLinkedInPostType(_linkedinType?: string): string {
    // For now, all LinkedIn posts from personal accounts are "personal"
    // In the future, we could detect company posts vs personal posts
    return 'personal';
}
