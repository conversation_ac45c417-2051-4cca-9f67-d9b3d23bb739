import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface UpdateEffectProps {
  children: React.ReactNode;
  isUpdated: boolean;
  className?: string;
  effectType?: 'outline' | 'gradient' | 'pulse' | 'glow';
  duration?: number;
  onAnimationComplete?: () => void;
}

export const UpdateEffect: React.FC<UpdateEffectProps> = ({
  children,
  isUpdated,
  className = '',
  effectType = 'gradient',
  duration = 3000,
  onAnimationComplete
}) => {
  const [showEffect, setShowEffect] = useState(false);

  useEffect(() => {
    if (isUpdated) {
      setShowEffect(true);
      const timer = setTimeout(() => {
        setShowEffect(false);
        onAnimationComplete?.();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isUpdated, duration, onAnimationComplete]);

  const getEffectClasses = () => {
    if (!showEffect) return '';

    switch (effectType) {
      case 'outline':
        return 'ring-2 ring-blue-500 ring-opacity-75 animate-pulse';
      
      case 'gradient':
        return 'bg-gradient-to-r from-blue-50 via-green-50 to-blue-50 animate-pulse border-l-4 border-blue-500';
      
      case 'pulse':
        return 'animate-pulse bg-blue-50 border border-blue-200';
      
      case 'glow':
        return 'shadow-lg shadow-blue-500/50 bg-blue-50 border border-blue-300 animate-pulse';
      
      default:
        return 'bg-gradient-to-r from-blue-50 via-green-50 to-blue-50 animate-pulse';
    }
  };

  return (
    <div
      className={cn(
        'transition-all duration-500 ease-in-out',
        getEffectClasses(),
        className
      )}
    >
      {children}
      {showEffect && (
        <div className="absolute top-0 right-0 -mt-1 -mr-1">
          <div className="flex items-center space-x-1 bg-green-500 text-white text-xs px-2 py-1 rounded-full animate-bounce">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>Updated</span>
          </div>
        </div>
      )}
    </div>
  );
};

// Hook for managing update states
export const useUpdateEffect = (initialData: any) => {
  const [data, setData] = useState(initialData);
  const [updatedItems, setUpdatedItems] = useState<Set<string>>(new Set());

  const updateData = (newData: any, itemId?: string) => {
    setData(newData);
    
    if (itemId) {
      setUpdatedItems(prev => new Set(prev).add(itemId));
      
      // Auto-clear the update indicator after animation
      setTimeout(() => {
        setUpdatedItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(itemId);
          return newSet;
        });
      }, 3000);
    }
  };

  const isItemUpdated = (itemId: string) => updatedItems.has(itemId);

  const clearUpdateIndicator = (itemId: string) => {
    setUpdatedItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(itemId);
      return newSet;
    });
  };

  return {
    data,
    updateData,
    isItemUpdated,
    clearUpdateIndicator
  };
};
