
import React from 'react';
import { TestTube, TrendingUp, BarChart3, UserRound, Network, Grid2X2 } from 'lucide-react';

const JiveBenefits = () => {
  return (
    <section className="px-4 max-w-5xl mx-auto py-16">
      <h2 className="text-3xl sm:text-4xl font-bold text-center mb-2">Why Choose Us</h2>
      <p className="text-center text-gray-600 mb-16">Maximize employee advocacy with these advantages</p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {/* Benefit 1 */}
        <div className="p-6 border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow bg-white/60 backdrop-blur-sm">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <TestTube size={24} className="text-blue-600" />
          </div>
          <h3 className="text-lg font-bold mb-2">Cost-Effective Testing</h3>
          <p className="text-gray-600">
            Use employee posts to A/B test messaging—no ad spend required.
          </p>
        </div>
        
        {/* Benefit 2 */}
        <div className="p-6 border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow bg-white/60 backdrop-blur-sm">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <TrendingUp size={24} className="text-green-600" />
          </div>
          <h3 className="text-lg font-bold mb-2">Organic Brand Reach</h3>
          <p className="text-gray-600">
            Tap into every employee's network for free, authentic exposure.
          </p>
        </div>
        
        {/* Benefit 3 */}
        <div className="p-6 border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow bg-white/60 backdrop-blur-sm">
          <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
            <BarChart3 size={24} className="text-purple-600" />
          </div>
          <h3 className="text-lg font-bold mb-2">Instant Impressions Insights</h3>
          <p className="text-gray-600">
            See total and per-post reach without paying for ad analytics.
          </p>
        </div>
        
        {/* Benefit 4 */}
        <div className="p-6 border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow bg-white/60 backdrop-blur-sm">
          <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
            <UserRound size={24} className="text-yellow-600" />
          </div>
          <h3 className="text-lg font-bold mb-2">Personal Brand Boost</h3>
          <p className="text-gray-600">
            Empower employees to build credibility that reflects on your company.
          </p>
        </div>
        
        {/* Benefit 5 */}
        <div className="p-6 border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow bg-white/60 backdrop-blur-sm">
          <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
            <Network size={24} className="text-orange-600" />
          </div>
          <h3 className="text-lg font-bold mb-2">Exponential Network Growth</h3>
          <p className="text-gray-600">
            Each team member's posts multiply your overall visibility.
          </p>
        </div>
        
        {/* Benefit 6 */}
        <div className="p-6 border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow bg-white/60 backdrop-blur-sm">
          <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
            <Grid2X2 size={24} className="text-indigo-600" />
          </div>
          <h3 className="text-lg font-bold mb-2">Centralized Oversight</h3>
          <p className="text-gray-600">
            Manage, approve, and track all posts and metrics from one dashboard.
          </p>
        </div>
      </div>
    </section>
  );
};

export default JiveBenefits;
