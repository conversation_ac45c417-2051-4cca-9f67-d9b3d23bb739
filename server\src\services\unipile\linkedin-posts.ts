import { UnipileConfig, createUnipileHeaders, handleUnipileError, logUnipileSuccess, logUnipileRequest } from './config';

/**
 * Get LinkedIn posts using Unipile users endpoint
 */
export async function getLinkedInPosts(
    config: UnipileConfig,
    accountId: string,
    userIdentifier: string,
    limit?: number
): Promise<any> {
    try {
        console.log('📝 Fetching LinkedIn posts:', {
            accountId: accountId,
            userIdentifier: userIdentifier,
            limit: limit || 'default',
            endpoint: 'GET /api/v1/users/{userIdentifier}/posts'
        });

        // Build URL without limit parameter if not specified
        let url = `${config.baseUrl}/api/v1/users/${userIdentifier}/posts?account_id=${accountId}`;
        if (limit && limit > 0) {
            url += `&limit=${limit}`;
        }

        logUnipileRequest('Posts', url);

        const response = await fetch(url, {
            method: 'GET',
            headers: createUnipileHeaders(config.apiKey)
        });

        if (!response.ok) {
            await handleUnipileError(response, 'Posts API');
        }

        const data = await response.json();
        logUnipileSuccess('LinkedIn posts', {
            totalPosts: data.items?.length || 0,
            hasItems: !!data.items,
            firstPostId: data.items?.[0]?.id,
            dataStructure: Object.keys(data)
        });

        return data;
    } catch (error: any) {
        console.error('💥 Error fetching LinkedIn posts:', error);
        throw new Error(`Failed to fetch LinkedIn posts: ${error.message}`);
    }
}

/**
Specific LinkedIn post details with comprehensive logging
 */
export async function getLinkedInPost(config: UnipileConfig, accountId: string, postId: string): Promise<any> {
    const startTime = Date.now();
    console.log(`🔍 [${new Date().toISOString()}] STEP 1: Fetching detailed post information`);
    console.log(`📊 Post ID: ${postId}`);
    console.log(`🔗 Account ID: ${accountId}`);

    try {
        const url = `${config.baseUrl}/api/v1/posts/${postId}?account_id=${accountId}`;
        logUnipileRequest('Post Details', url);

        const response = await fetch(url, {
            method: 'GET',
            headers: createUnipileHeaders(config.apiKey)
        });

        if (!response.ok) {

            await handleUnipileError(response, 'Post details');
        }

        const data = await response.json();
        console.log("🚀 ~ getLinkedInPost ~ data:", data)
        const duration = Date.now() - startTime;

        console.log(`✅ [${new Date().toISOString()}] Post details fetched successfully (${duration}ms)`);
        console.log(`📝 Post content: "${data.text?.substring(0, 100)}..."`);
        console.log(`👤 Author: ${data.author?.name} (${data.author?.is_company ? 'Company' : 'Individual'})`);
        console.log(`📊 Engagement: ${data.impressions_counter || 0} impressions, ${data.reaction_counter || 0} reactions, ${data.comment_counter || 0} comments`);
        console.log(`📅 Published: ${data.parsed_datetime || data.date || 'N/A'}`);

        logUnipileSuccess('LinkedIn post details', {
            postId: data.id,
            socialId: data.social_id,
            authorName: data.author?.name,
            impressions: data.impressions_counter,
            reactions: data.reaction_counter,
            comments: data.comment_counter
        });

        return data;
    } catch (error: any) {
        const duration = Date.now() - startTime;
        console.error(`❌ [${new Date().toISOString()}] Failed to fetch post details (${duration}ms):`, error);
        throw new Error(`Failed to fetch LinkedIn post details: ${error.message}`);
    }
}

/**
 * Get post reactions using Unipile API with detailed logging
 */
export async function getPostReactions(config: UnipileConfig, accountId: string, postId: string, limit: number = 100): Promise<any> {
    const startTime = Date.now();
    console.log(`👍 [${new Date().toISOString()}] STEP 3: Fetching post reactions`);
    console.log(`📊 Post ID: ${postId}`);
    console.log(`🔢 Limit: ${limit} reactions`);

    try {
        const url = `${config.baseUrl}/api/v1/posts/${postId}/reactions?limit=${limit}&account_id=${accountId}`;
        logUnipileRequest('Post Reactions', url);

        const response = await fetch(url, {
            method: 'GET',
            headers: createUnipileHeaders(config.apiKey)
        });

        if (!response.ok) {
            await handleUnipileError(response, 'Post reactions');
        }

        const data = await response.json();
        const duration = Date.now() - startTime;

        console.log(`✅ [${new Date().toISOString()}] Reactions fetched successfully (${duration}ms)`);
        console.log(`📊 Total reactions: ${data.items?.length || 0}`);

        // Log reaction breakdown by type
        if (data.items && data.items.length > 0) {
            const reactionCounts: { [key: string]: number } = {};
            const reactionUsers: { [key: string]: string[] } = {};

            data.items.forEach((reaction: any) => {
                const type = reaction.reaction_type || 'LIKE';
                reactionCounts[type] = (reactionCounts[type] || 0) + 1;

                if (!reactionUsers[type]) reactionUsers[type] = [];
                reactionUsers[type].push(`${reaction.author?.name} (${reaction.author?.is_company ? 'Company' : 'Individual'})`);
            });

            console.log(`👍 Reaction breakdown:`);
            Object.entries(reactionCounts).forEach(([type, count]) => {
                console.log(`  ${type}: ${count} reactions`);
                console.log(`    Users: ${reactionUsers[type].slice(0, 3).join(', ')}${reactionUsers[type].length > 3 ? ` and ${reactionUsers[type].length - 3} more` : ''}`);
            });
        }

        logUnipileSuccess('LinkedIn post reactions', {
            totalReactions: data.items?.length || 0,
            reactionTypes: data.items ? [...new Set(data.items.map((r: any) => r.reaction_type))] : []
        });

        return data;
    } catch (error: any) {
        const duration = Date.now() - startTime;
        console.error(`❌ [${new Date().toISOString()}] Failed to fetch reactions (${duration}ms):`, error);
        throw new Error(`Failed to fetch post reactions: ${error.message}`);
    }
}

/**
 * Get post comments using Unipile API with detailed logging
 */
export async function getPostComments(config: UnipileConfig, accountId: string, postId: string, limit: number = 50): Promise<any> {
    const startTime = Date.now();
    console.log(`📝 [${new Date().toISOString()}] STEP 2: Fetching post comments`);
    console.log(`📊 Post ID: ${postId}`);
    console.log(`🔢 Limit: ${limit} comments`);

    try {
        const url = `${config.baseUrl}/api/v1/posts/${postId}/comments?account_id=${accountId}&limit=${limit}`;
        logUnipileRequest('Post Comments', url);

        const response = await fetch(url, {
            method: 'GET',
            headers: createUnipileHeaders(config.apiKey)
        });

        if (!response.ok) {
            await handleUnipileError(response, 'Post comments');
        }

        const data = await response.json();
        const duration = Date.now() - startTime;

        console.log(`✅ [${new Date().toISOString()}] Comments fetched successfully (${duration}ms)`);
        console.log(`📊 Total comments: ${data.items?.length || 0}`);

        // Log comment details
        if (data.items && data.items.length > 0) {
            console.log(`📝 Comment breakdown:`);
            data.items.forEach((comment: any, index: number) => {
                const commentText = comment.text?.substring(0, 50) || 'No text';
                const authorName = comment.author?.name || 'Unknown';
                const authorType = comment.author?.is_company ? 'Company' : 'Individual';
                const commentDate = comment.created_at || comment.date || 'N/A';

                console.log(`  ${index + 1}. ${authorName} (${authorType}) [${commentDate}]: "${commentText}..."`);

                // Log comment reactions if available
                if (comment.reaction_counter && comment.reaction_counter > 0) {
                    console.log(`     👍 ${comment.reaction_counter} reactions on this comment`);
                }
            });
        }

        logUnipileSuccess('LinkedIn post comments', {
            totalComments: data.items?.length || 0,
            hasReplies: data.items ? data.items.some((c: any) => c.reply_count > 0) : false
        });

        return data;
    } catch (error: any) {
        const duration = Date.now() - startTime;
        console.error(`❌ [${new Date().toISOString()}] Failed to fetch comments (${duration}ms):`, error);
        throw new Error(`Failed to fetch post comments: ${error.message}`);
    }
}

/**
 * Get comprehensive post data (post details + comments + reactions) with detailed step-by-step logging
 */
export async function getComprehensivePostData(config: UnipileConfig, accountId: string, postId: string): Promise<any> {
    const overallStartTime = Date.now();
    console.log(`🚀 [${new Date().toISOString()}] ===== COMPREHENSIVE POST DATA FETCH STARTED =====`);
    console.log(`📊 Post ID: ${postId}`);
    console.log(`🔗 Account ID: ${accountId}`);
    console.log(`⏰ Start time: ${new Date().toISOString()}`);

    try {
        // Step 1: Get post details using regular post_id (not URN)
        console.log(`🔄 [${new Date().toISOString()}] Starting Step 1: Post Details (using post_id: ${postId})`);
        const postDetails = await getLinkedInPost(config, accountId, postId);

        // Extract social_id from post details for comments and reactions
        const socialId = postDetails.social_id || postId;
        console.log(`🔗 [${new Date().toISOString()}] Using social_id for comments/reactions: ${socialId}`);

        // Step 2 & 3: Get comments and reactions in parallel using social_id (URN format)
        console.log(`🔄 [${new Date().toISOString()}] Starting Steps 2-3: Comments and Reactions (Parallel) with social_id`);
        const parallelStartTime = Date.now();

        const [comments, reactions] = await Promise.all([
            getPostComments(config, accountId, socialId, 100),
            getPostReactions(config, accountId, socialId, 100)
        ]);

        const parallelDuration = Date.now() - parallelStartTime;
        console.log(`✅ [${new Date().toISOString()}] Parallel fetch completed (${parallelDuration}ms)`);

        // Step 4: Compile comprehensive data
        console.log(`📊 [${new Date().toISOString()}] STEP 4: Compiling comprehensive post data`);

        // Format post date properly
        // const formatPostDate = (dateString: string) => {
        //     try {
        //         const date = new Date(dateString);
        //         return {
        //             iso: date.toISOString(),
        //             readable: date.toLocaleDateString('en-US', {
        //                 year: 'numeric',
        //                 month: 'long',
        //                 day: 'numeric',
        //                 hour: '2-digit',
        //                 minute: '2-digit'
        //             }),
        //             relative: getRelativeTime(date)
        //         };
        //     } catch {
        //         return {
        //             iso: null,
        //             readable: 'N/A',
        //             relative: 'N/A'
        //         };
        //     }
        // };
const formatPostDate = (dateString: string) => {
    try {
        const date = new Date(dateString);

        if (isNaN(date.getTime())) throw new Error('Invalid date');

        return {
            iso: date.toISOString(),
            readable: date.toLocaleString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }),
            relative: getRelativeTime(date)
        };
    } catch {
        return {
            iso: null,
            readable: 'N/A',
            relative: 'N/A'
        };
    }
};

        // Helper function for relative time
        const getRelativeTime = (date: Date) => {
            const now = new Date();
            const diffTime = Math.abs(now.getTime() - date.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 1) return '1 day ago';
            if (diffDays < 7) return `${diffDays} days ago`;
            if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
            if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
            return `${Math.floor(diffDays / 365)} years ago`;
        };

        const comprehensiveData = {
            post: {
                ...postDetails,
                formatted_date: formatPostDate(postDetails.parsed_datetime)
            },
            comments: {
                items: comments.items || [],
                total_count: comments.items?.length || 0,
                has_more: comments.cursor ? true : false
            },
            reactions: {
                items: reactions.items || [],
                total_count: reactions.items?.length || 0,
                breakdown: getReactionBreakdown(reactions.items || []),
                has_more: reactions.cursor ? true : false
            },
            metadata: {
                total_comments: comments.items?.length || 0,
                total_reactions: reactions.items?.length || 0,
                fetch_timestamp: new Date().toISOString(),
                fetch_duration_ms: Date.now() - overallStartTime,
                steps_completed: ['post_details', 'comments', 'reactions', 'compilation']
            }
        };

        const overallDuration = Date.now() - overallStartTime;
        console.log(`🎉 [${new Date().toISOString()}] ===== COMPREHENSIVE POST DATA FETCH COMPLETED =====`);
        console.log(`⏱️ Total duration: ${overallDuration}ms`);
        console.log(`📊 Final summary:`);
        console.log(`  - Post ID used: ${postId} (regular post_id)`);
        console.log(`  - Social ID used for comments/reactions: ${socialId} (URN format)`);
        console.log(`  - Post: "${postDetails.text?.substring(0, 50)}..."`);
        console.log(`  - Author: ${postDetails.author?.name} (${postDetails.author?.is_company ? 'Company' : 'Individual'})`);
        console.log(`  - Published: ${comprehensiveData.post.formatted_date.readable}`);
        console.log(`  -🖌️🖌️🖌️ Impressions: ${postDetails.impressions_counter || 0}`);
        console.log(`  - Comments: ${comprehensiveData.metadata.total_comments}`);
        console.log(`  - Reactions: ${comprehensiveData.metadata.total_reactions}`);
        console.log(`  - Engagement Rate: ${postDetails.impressions_counter ? ((comprehensiveData.metadata.total_reactions + comprehensiveData.metadata.total_comments) / postDetails.impressions_counter * 100).toFixed(2) : 0}%`);

        return comprehensiveData;

    } catch (error) {
        const overallDuration = Date.now() - overallStartTime;
        console.error(`❌ [${new Date().toISOString()}] ===== COMPREHENSIVE POST DATA FETCH FAILED =====`);
        console.error(`⏱️ Failed after: ${overallDuration}ms`);
        console.error(`❌ Error: ${error}`);
        throw error;
    }
}

/**
 * Helper function to get reaction breakdown
 */
function getReactionBreakdown(reactions: any[]) {
    const breakdown: { [key: string]: { count: number; users: any[] } } = {};

    reactions.forEach((reaction) => {
        const type = reaction.reaction_type || 'LIKE';
        if (!breakdown[type]) {
            breakdown[type] = { count: 0, users: [] };
        }
        breakdown[type].count++;
        breakdown[type].users.push({
            name: reaction.author?.name,
            is_company: reaction.author?.is_company,
            headline: reaction.author?.headline,
            profile_url: reaction.author?.profile_url
        });
    });

    return breakdown;
}
