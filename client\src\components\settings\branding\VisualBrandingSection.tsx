
import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

interface ColorConfig {
  name: string;
  value: string;
  label: string;
}

interface VisualBrandingSectionProps {
  brandColors: ColorConfig[];
  onEditColors: () => void;
  onSave: () => void;
}

const VisualBrandingSection = ({ 
  brandColors, 
  onEditColors,
  onSave 
}: VisualBrandingSectionProps) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label>Company Logo</Label>
          <div className="border rounded-md p-4 flex flex-col items-center justify-center">
            <div className="h-20 w-20 rounded-md bg-enterprise-blue flex items-center justify-center mb-4">
              <span className="font-bold text-white text-xl">A</span>
            </div>
            <Button variant="outline" size="sm">
              Change Logo
            </Button>
          </div>
        </div>
        
        <div className="space-y-2">
          <Label>Brand Colors</Label>
          <div className="border rounded-md p-4 space-y-3">
            <div className="grid grid-cols-3 gap-2">
              {brandColors.map((color, index) => (
                <div key={index}>
                  <div 
                    className="h-8 rounded-md mb-1"
                    style={{ backgroundColor: color.value }}
                  ></div>
                  <p className="text-xs text-center">{color.value}</p>
                </div>
              ))}
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={onEditColors}
            >
              Edit Brand Colors
            </Button>
          </div>
        </div>
      </div>
      <div className="flex justify-end">
        <Button 
          onClick={onSave}
          className="bg-enterprise-blue hover:bg-enterprise-blue/90"
        >
          Save Visual Assets
        </Button>
      </div>
    </div>
  );
};

export default VisualBrandingSection;
