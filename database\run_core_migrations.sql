-- Phase 1: Core Database Migration Runner (Simplified)
-- Run only the essential migrations for LinkedIn post sync system

-- Migration 1: Create custom types (user/admin only)
\echo 'Running Migration 1: Creating custom types (user/admin roles only)...'
\i 000_create_custom_types.sql

-- Migration 2: Create main posts table
\echo 'Running Migration 2: Setting up public_users_posts table...'
\i 001_fix_public_users_posts_schema.sql

-- Skip Migration 3: Sync tracking system (not needed for basic functionality)
\echo 'Skipping Migration 3: Sync tracking system (can be added later if needed)'

-- Verification queries
\echo 'Verifying core migrations...'

-- Check if main table exists
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename = 'public_users_posts';

-- Check if types exist
SELECT 
    typname,
    typtype,
    CASE 
        WHEN typname = 'user_role' THEN (
            SELECT string_agg(enumlabel, ', ' ORDER BY enumsortorder) 
            FROM pg_enum 
            WHERE enumtypid = pg_type.oid
        )
        ELSE 'N/A'
    END as enum_values
FROM pg_type 
WHERE typname IN ('post_type', 'department', 'user_role')
ORDER BY typname;

-- Check if main table indexes exist
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename = 'public_users_posts'
ORDER BY indexname;

-- Check if core functions exist
SELECT 
    proname,
    pronargs,
    prorettype::regtype as return_type
FROM pg_proc 
WHERE proname IN (
    'calculate_engagement_rate',
    'update_updated_at_column',
    'auto_calculate_engagement_rate'
)
ORDER BY proname;

-- Test the main table structure
\echo 'Testing table structure...'
\d public.public_users_posts

\echo 'Core migration verification complete!'
\echo 'Phase 1: Core Database Setup - COMPLETED ✅'
\echo ''
\echo 'What you have now:'
\echo '✅ public_users_posts table with all necessary fields'
\echo '✅ Custom types: post_type, department, user_role (user/admin only)'
\echo '✅ Automatic engagement rate calculation'
\echo '✅ Optimized indexes for performance'
\echo '✅ Proper constraints and relationships'
\echo ''
\echo 'What was skipped (can add later):'
\echo '⏭️  sync_operations table (detailed sync tracking)'
\echo '⏭️  sync monitoring views and functions'
\echo ''
\echo 'Ready for Phase 2: Core Services! 🚀'
