import React, { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Bell, User, PlusCircle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/context/AuthContext';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import PageBackground from '@/components/layout/PageBackground';
import { useNavigate } from 'react-router-dom';
import { useLinkedInConnection } from '@/hooks/useLinkedInConnection';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface AdminDashboardLayoutProps {
  children: React.ReactNode;
}

export const AdminDashboardLayout = ({ children }: AdminDashboardLayoutProps) => {
  const { user, profile } = useAuth();
  const [pendingApprovals, setPendingApprovals] = useState(0);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { isLinkedInConnected, isLoading } = useLinkedInConnection();
  
  useEffect(() => {
    // Fetch pending approvals count when the component mounts
    fetchPendingApprovals();
    
    // Subscribe to real-time updates for new pending approvals
    const channel = supabase
      .channel('admin-approval-notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'employee_posts',
          // Using an existing column that might indicate approval status
          // For now, we'll track company posts that might need approval
          filter: 'post_type=eq.company'
        },
        (payload) => {
          console.log('New post pending approval:', payload);
          updateNotificationCount(prev => prev + 1);
          toast({
            title: "New post needs approval",
            description: "A team member has submitted a post for approval.",
          });
        }
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
    };
  }, [toast]);
  
  const fetchPendingApprovals = async () => {
    try {
      // Modify the query to check for company posts that need approval
      // This approach assumes company posts are the ones that need approval
      const { count, error } = await supabase
        .from('employee_posts')
        .select('*', { count: 'exact', head: true })
        .eq('post_type', 'company');
      
      if (error) throw error;
      
      if (count !== null) {
        updateNotificationCount(count);
      }
    } catch (error) {
      console.error('Error fetching pending approvals count:', error);
    }
  };
  
  // Function to update both notification badges
  const updateNotificationCount = (countValue: number | ((prev: number) => number)) => {
    // Update state
    if (typeof countValue === 'function') {
      setPendingApprovals(prev => {
        const newCount = countValue(prev);
        updateNavbarBadge(newCount);
        return newCount;
      });
    } else {
      setPendingApprovals(countValue);
      updateNavbarBadge(countValue);
    }
  };
  
  // Helper function to update the navbar badge
  const updateNavbarBadge = (count: number) => {
    const navbarBadge = document.getElementById('navbar-notification-badge');
    if (navbarBadge) {
      navbarBadge.textContent = count.toString();
      navbarBadge.classList.toggle('opacity-0', count === 0);
      navbarBadge.classList.toggle('opacity-100', count > 0);
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col relative overflow-hidden">
      <PageBackground />
      <Navbar />
      
      <div className="flex-1 container mx-auto px-4 py-8 relative z-10">
        {/* Header section */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-enterprise-blue to-enterprise-teal bg-clip-text text-transparent">Team LinkedIn Performance</h1>
            <p className="text-enterprise-gray-600">
              Monitor and analyze your team's social engagement
            </p>
          </div>
          
          <div className="flex items-center gap-4 w-full md:w-auto">
            {/* We're keeping this for layout consistency but it's now handled by the Navbar */}
            <div className="opacity-0 pointer-events-none">
              <Button
                variant="ghost"
                size="icon"
                className="text-enterprise-gray-600 relative"
              >
                <span className="w-5 h-5"></span>
              </Button>
            </div>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    className="flex-1 md:flex-none bg-black hover:bg-black/90 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => navigate('/content-creator')}
                    disabled={!isLinkedInConnected || isLoading}
                  >
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Create Post
                  </Button>
                </TooltipTrigger>
                {(!isLinkedInConnected && !isLoading) && (
                  <TooltipContent>
                    <p>Connect your LinkedIn account to create posts</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        
        {children}
      </div>
      
      <Footer />
    </div>
  );
};
