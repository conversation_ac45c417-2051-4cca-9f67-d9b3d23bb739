
export type UserRole = 'admin' | 'user';

export type LinkedInConnectionStatus = 'connected' | 'disconnected' | 'pending' | 'verification_required';

export type CheckpointType = 'CAPTCHA' | '2FA' | 'OTP' | 'QRCODE' | 'PHONE_REGISTER' | 'IN_APP_VALIDATION';

export interface BaseCheckpoint {
  type: CheckpointType;
  account_id: string;
  message?: string;
  instructions?: string;
}

export interface CaptchaCheckpoint extends BaseCheckpoint {
  type: 'CAPTCHA';
  public_key?: string;
  data?: string;
  image_url?: string;
  challenge_id?: string;
}

export interface TwoFactorCheckpoint extends BaseCheckpoint {
  type: '2FA';
  source?: 'SMS' | 'APP' | 'EMAIL';
  phone_number?: string;
  email?: string;
}

export interface OTPCheckpoint extends BaseCheckpoint {
  type: 'OTP';
  phone_number?: string;
  email?: string;
}

export interface QRCodeCheckpoint extends BaseCheckpoint {
  type: 'QRCODE';
  qrcode: string;
  qr_data?: string;
}

export interface PhoneRegisterCheckpoint extends BaseCheckpoint {
  type: 'PHONE_REGISTER';
  required_format?: string;
}

export interface InAppValidationCheckpoint extends BaseCheckpoint {
  type: 'IN_APP_VALIDATION';
  validation_url?: string;
  app_name?: string;
}

export type Checkpoint =
  | CaptchaCheckpoint
  | TwoFactorCheckpoint
  | OTPCheckpoint
  | QRCodeCheckpoint
  | PhoneRegisterCheckpoint
  | InAppValidationCheckpoint;

export interface Profile {
  linkedin_account_id: any;
  id: string;
  email: string;
  role: UserRole;
  company_id?: string;
  first_name?: string;
  last_name?: string;
  linkedin_url?: string;
  linkedin_connection_status?: LinkedInConnectionStatus;
  account_id?: string;
  phyllo_id?: string;
  created_at: string;
  updated_at: string;
}

export interface SignupFormData {
  // App credentials
  email: string;
  password: string;
  confirmPassword: string;

  // LinkedIn credentials (email only - password not stored for security)
  linkedinEmail: string;

  // User info
  firstName: string;
  lastName: string;
  linkedinUrl?: string;

  // Options
  useSameCredentials: boolean;
}

export interface LinkedInVerificationState {
  isVerifying: boolean;
  isVerified: boolean;
  accountId?: string;
  requiresCheckpoint: boolean;
  checkpoint?: Checkpoint;
  error?: string;
}

export interface AuthContextType {
  session: any;
  user: any;
  profile: Profile | null;
  isLoading: boolean;
  signUp: (data: { email: string; password: string; options?: { data?: Record<string, any> } }) => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  isAdmin: () => boolean;
  isUser: () => boolean;
}
