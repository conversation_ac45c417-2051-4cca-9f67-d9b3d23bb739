import { Router } from 'express';
import {
    handleGetAnalyticsMetrics,
    handleSyncAnalytics,
    handleGetDashboardData,
    handleTrackAction,
    handleGetOnboardingMetrics,
    handleGetUserJourney
} from '../handlers/analytics.handlers';

/**
 * Functional Analytics Routes
 * Clean, specific endpoints for analytics operations
 */

const router = Router();

// Analytics metrics operations
router.get('/metrics/:userId', handleGetAnalyticsMetrics);       // GET /api/analytics/metrics/:userId

router.post('/sync/:userId', handleSyncAnalytics);              // POST /api/analytics/sync/:userId

// Dashboard operations
router.get('/dashboard/:userId', handleGetDashboardData);        // GET /api/analytics/dashboard/:userId

// User tracking operations
router.post('/track', handleTrackAction);                       // POST /api/analytics/track

router.get('/onboarding/metrics', handleGetOnboardingMetrics);  // GET /api/analytics/onboarding/metrics

router.get('/journey/:sessionId', handleGetUserJourney);        // GET /api/analytics/journey/:sessionId

export default router;
