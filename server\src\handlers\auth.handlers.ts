import { Request, Response } from 'express';
import { unipileService } from '../services/unipile/index';
import { createUser } from '../services/user.service';
import { CreateUserRequest } from '../types/user';
import {
    sendSuccess,
    sendError,
    sendValidationError,
    sendNotFound,
    sendCreated,
    handleAsyncError,
    validateRequiredFields,
    logRequest,
    logResponse
} from '../utils/response.utils';
import {
    validateUserSignup,
    isValidEmail,
    sanitizeObject
} from '../utils/validation.utils';

// Temporary storage for webhook account IDs (in-memory for simplicity)
// In production, you might want to use Redis or a database
const webhookAccountStorage = new Map<string, {
    account_id: string;
    status: string;
    timestamp: number;
    user_email: string;
}>();

// Clean up old entries (older than 10 minutes)
const cleanupOldEntries = () => {
    const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
    for (const [key, value] of webhookAccountStorage.entries()) {
        if (value.timestamp < tenMinutesAgo) {
            webhookAccountStorage.delete(key);
        }
    }
};

/**
 * Functional Auth Handlers
 * Pure functions for handling authentication-related HTTP requests
 * Using utility functions for consistent responses and validation
 */

/**
 * Official Unipile webhook handler
 * POST /auth/webhook/unipile-auth
 */
export const handleUnipileWebhook = async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();

    try {
        logRequest('POST', '/auth/webhook/unipile-auth', undefined, req.body);

        const { status, account_id, name } = req.body;

        // Validate required webhook fields
        if (!validateRequiredFields(res, req.body, ['status', 'account_id', 'name'])) {
            return;
        }

        console.log('🎯 ===== WEBHOOK: PROCESSING LINKEDIN VERIFICATION =====');
        console.log('🎯 Webhook data received:', {
            status,
            account_id,
            name,
            timestamp: new Date().toISOString()
        });

        // Clean up old entries first
        cleanupOldEntries();

        // Store the account ID temporarily for frontend polling
        const storageKey = `webhook_${name}_${Date.now()}`;
        webhookAccountStorage.set(storageKey, {
            account_id,
            status,
            timestamp: Date.now(),
            user_email: name
        });

        console.log('💾 Stored webhook data for frontend polling:', {
            storageKey,
            account_id,
            user_email: name
        });

        const isReconnection = status === 'RECONNECTED';
        console.log('🔄 Connection type:', isReconnection ? 'Reconnection' : 'First connection');
        console.log('✅ Webhook: LinkedIn verification data processed and stored for frontend');
        console.log('🎯 ===== WEBHOOK: LINKEDIN VERIFICATION PROCESSING COMPLETED =====');

        sendSuccess(res, {
            status,
            account_id,
            name,
            processed_at: new Date().toISOString()
        }, 'Webhook processed successfully');

        logResponse('POST', '/auth/webhook/unipile-auth', true, Date.now() - startTime);
    } catch (error: any) {
        logResponse('POST', '/auth/webhook/unipile-auth', false, Date.now() - startTime);
        handleAsyncError(res, error, 'webhook processing');
    }
};

/**
 * Create hosted auth link (Official Unipile Pattern)
 * POST /auth/hosted-auth/create
 */
export const handleCreateHostedAuth = async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();

    try {
        const { userEmail, type = 'create' } = req.body;

        logRequest('POST', '/auth/hosted-auth/create', undefined, { userEmail, type });

        // Validate required fields
        if (!validateRequiredFields(res, req.body, ['userEmail'])) {
            return;
        }

        // Validate email format
        if (!isValidEmail(userEmail)) {
            sendValidationError(res, 'Invalid email format');
            return;
        }

        console.log('🔗 Creating hosted auth link for:', userEmail);

        const hostedAuthLink = await unipileService.createHostedAuthLink({
            name: userEmail,
            type: type,
            success_redirect_url: `${process.env.CLIENT_URL}/auth/success`,
            failure_redirect_url: `${process.env.CLIENT_URL}/auth/failure`,
            notify_url: `${process.env.SERVER_URL}/auth/webhook/unipile-auth`
        });

        sendSuccess(res, hostedAuthLink, 'Hosted auth link created successfully');
        logResponse('POST', '/auth/hosted-auth/create', true, Date.now() - startTime);
    } catch (error: any) {
        logResponse('POST', '/auth/hosted-auth/create', false, Date.now() - startTime);
        handleAsyncError(res, error, 'creating hosted auth link');
    }
};

/**
 * Create reconnect hosted auth link (Official Unipile Pattern)
 * POST /auth/hosted-auth/reconnect
 */
export const handleCreateReconnectAuth = async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();

    try {
        const { userEmail, accountId, type = 'reconnect' } = req.body;

        logRequest('POST', '/auth/hosted-auth/reconnect', userEmail, { accountId, type });

        // Validate required fields
        if (!validateRequiredFields(res, req.body, ['userEmail', 'accountId'])) {
            return;
        }

        const hostedAuthLink = await unipileService.createHostedAuthLink({
            name: userEmail,
            type: type,
            reconnect_account: accountId,
            success_redirect_url: `${process.env.CLIENT_URL}/auth/success`,
            failure_redirect_url: `${process.env.CLIENT_URL}/auth/failure`,
            notify_url: `${process.env.SERVER_URL}/auth/webhook/unipile-auth`
        });

        sendSuccess(res, hostedAuthLink, 'Reconnect auth link created successfully');
        logResponse('POST', '/auth/hosted-auth/reconnect', true, Date.now() - startTime);
    } catch (error: any) {
        logResponse('POST', '/auth/hosted-auth/reconnect', false, Date.now() - startTime);
        handleAsyncError(res, error, 'creating reconnect auth link');
    }
};

/**
 * Get account by email - immediate check without polling
 * POST /auth/account/email
 */
export const handleGetAccountByEmail = async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();

    try {
        const { email } = req.body;

        logRequest('POST', '/auth/account/email', undefined, { email });

        // Validate required fields
        if (!validateRequiredFields(res, req.body, ['email'])) {
            return;
        }

        // Validate email format
        if (!isValidEmail(email)) {
            sendValidationError(res, 'Invalid email format');
            return;
        }

        // In a real app, you would check your database here
        console.log('📧 Email check completed (mock response)');

        sendSuccess(res, {
            email: email,
            accountExists: false, // Mock response
            accountId: null,
            connectionStatus: 'not_connected'
        }, 'Account check completed');

        logResponse('POST', '/auth/account/email', true, Date.now() - startTime);
    } catch (error: any) {
        logResponse('POST', '/auth/account/email', false, Date.now() - startTime);
        handleAsyncError(res, error, 'checking account by email');
    }
};

/**
 * Verify hosted auth account creation
 * POST /auth/verify-account
 */
export const handleVerifyHostedAuthAccount = async (req: Request, res: Response): Promise<void> => {
    try {
        const { internalUserId } = req.body;

        console.log('🔍 Verifying hosted auth account for user:', internalUserId);

        if (!internalUserId) {
            res.status(400).json({
                success: false,
                error: 'Internal user ID is required'
            });
            return;
        }

        // In a real app, you would check if the webhook was received and account was created
        // For now, return a mock response
        console.log('✅ Account verification completed (mock response)');

        res.json({
            success: true,
            data: {
                verified: true,
                accountId: 'mock_account_id',
                status: 'connected'
            }
        });
    } catch (error: any) {
        console.error('❌ Error verifying hosted auth account:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to verify hosted auth account',
            details: error.message
        });
    }
};

/**
 * Check account status for IN_APP_VALIDATION polling
 * GET /auth/account/status/:accountId
 */
export const handleCheckAccountStatus = async (req: Request, res: Response): Promise<void> => {
    try {
        const { accountId } = req.params;

        console.log('🔍 Checking account status for:', accountId);

        if (!accountId) {
            res.status(400).json({
                success: false,
                error: 'Account ID is required'
            });
            return;
        }

        // Get account status from Unipile
        const accountStatus = await unipileService.getAccountStatus(accountId);

        console.log('✅ Account status retrieved:', accountStatus);
        res.json({
            success: true,
            data: accountStatus
        });
    } catch (error: any) {
        console.error('❌ Error checking account status:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to check account status',
            details: error.message
        });
    }
};

/**
 * Get the latest LinkedIn account from webhook storage or Unipile
 * GET /auth/linkedin/latest
 */


export const handleGetLatestLinkedInAccount = async (_req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();
    try {

        logRequest('GET', '/auth/linkedin/latest');

        // Clean up old entries first
        cleanupOldEntries();

        // STEP 1: Check webhook storage for recent account creation
        console.log('🔍 Step 1: Checking webhook storage for recent account creation...');

        if (webhookAccountStorage.size > 0) {
            // Get the most recent webhook entry
            const entries = Array.from(webhookAccountStorage.entries());
            entries.sort((a, b) => b[1].timestamp - a[1].timestamp);
            const latestWebhookEntry = entries[0];

            if (latestWebhookEntry) {
                const [storageKey, webhookData] = latestWebhookEntry;
                console.log('✅ Found recent webhook account:', {
                    storageKey,
                    account_id: webhookData.account_id,
                    user_email: webhookData.user_email,
                    age_minutes: Math.round((Date.now() - webhookData.timestamp) / (1000 * 60))
                });

                // Return the webhook account data in the expected format
                const webhookAccount = {
                    id: webhookData.account_id,
                    account_id: webhookData.account_id, // For backward compatibility
                    type: 'LINKEDIN',
                    status: webhookData.status,
                    name: webhookData.user_email,
                    created_at: new Date(webhookData.timestamp).toISOString(),
                    source: 'webhook'
                };

                console.log('✅ Returning webhook account data:', webhookAccount.id);
                sendSuccess(res, webhookAccount, 'Latest LinkedIn account from webhook');
                logResponse('GET', '/auth/linkedin/latest', true, Date.now() - startTime);
                return;
            }
        }

        // STEP 2: Fallback to Unipile API if no webhook data
        console.log('🔍 Step 2: No recent webhook data, checking Unipile API...');

        const accountsResponse = await unipileService.getAllAccounts();
        console.log('Fetching all accounts from Unipile');
        console.log('Accounts response:', accountsResponse);

        // Extract the items array from the response
        const accounts = accountsResponse.items || [];
        const linkedinAccounts = accounts.filter((account: any) => account.type === 'LINKEDIN');

        if (linkedinAccounts.length === 0) {
            sendNotFound(res, 'LinkedIn accounts');
            return;
        }

        // Get the most recent account (sort by created_at and take the latest)
        linkedinAccounts.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        const latestAccount = linkedinAccounts[0];

        console.log('✅ Latest LinkedIn account found from Unipile:', latestAccount.id);
        sendSuccess(res, latestAccount, 'Latest LinkedIn account retrieved from Unipile');
        logResponse('GET', '/auth/linkedin/latest', true, Date.now() - startTime);
    } catch (error: any) {
        logResponse('GET', '/auth/linkedin/latest', false, Date.now() - startTime);
        handleAsyncError(res, error, 'getting latest LinkedIn account');
    }
};

/**
 * Save LinkedIn data manually after successful verification
 * POST /auth/linkedin/save
 */
export const handleSaveLinkedInDataManually = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userEmail, accountId } = req.body;

        console.log('💾 Manual LinkedIn data save request:', { userEmail, accountId });

        if (!userEmail || !accountId) {
            res.status(400).json({
                success: false,
                error: 'User email and account ID are required'
            });
            return;
        }

        // Fetch the LinkedIn account data from Unipile (frontend handles data persistence)
        console.log('🔍 Step 1: Fetching LinkedIn account from Unipile API...');
        console.log('🔗 Unipile API URL: https://{subdomain}.unipile.com:{port}/api/v1/accounts/' + accountId);

        const linkedInProfile = await unipileService.getLinkedInProfile(accountId);
        console.log('✅ Step 1 Complete: LinkedIn profile fetched successfully');
        console.log('📊 LinkedIn profile data:', JSON.stringify(linkedInProfile, null, 2));

        console.log('✅ Manual LinkedIn data fetch completed successfully (frontend handles persistence)');

        res.json({
            success: true,
            data: {
                userEmail,
                accountId,
                profile: linkedInProfile,
                message: 'LinkedIn data fetched successfully'
            }
        });
    } catch (error: any) {
        console.error('❌ Error in manual LinkedIn data save:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch LinkedIn data',
            details: error.message
        });
    }
};

/**
 * Get LinkedIn profile for frontend fallback
 * GET /auth/linkedin/profile/:accountId
 */
export const handleGetLinkedInProfileForFrontend = async (req: Request, res: Response): Promise<void> => {
    try {
        const { accountId } = req.params;

        console.log('🔍 Getting LinkedIn profile for frontend:', accountId);

        if (!accountId) {
            res.status(400).json({
                success: false,
                error: 'Account ID is required'
            });
            return;
        }

        const profile = await unipileService.getLinkedInProfile(accountId);

        console.log('✅ LinkedIn profile retrieved for frontend');
        res.json({
            success: true,
            data: profile
        });
    } catch (error: any) {
        console.error('❌ Error getting LinkedIn profile for frontend:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get LinkedIn profile',
            details: error.message
        });
    }
};

/**
 * Reconnect existing LinkedIn account
 * POST /auth/linkedin/reconnect
 */
export const handleReconnectLinkedInAccount = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId, accountId, type } = req.body;

        console.log('🔄 Reconnecting LinkedIn account:', { userId, accountId, type });

        if (!userId || !accountId) {
            res.status(400).json({
                success: false,
                error: 'User ID and account ID are required'
            });
            return;
        }

        // Connection status updates are handled by frontend
        console.log('✅ Step 3: Connection status updates handled by frontend');

        const hostedAuthLink = await unipileService.createHostedAuthLink({
            name: userId,
            type: 'reconnect',
            reconnect_account: accountId,
            success_redirect_url: `${process.env.CLIENT_URL}/auth/success`,
            failure_redirect_url: `${process.env.CLIENT_URL}/auth/failure`,
            notify_url: `${process.env.SERVER_URL}/auth/webhook/unipile-auth`
        });

        console.log('✅ Reconnection link created successfully');
        res.json({
            success: true,
            data: hostedAuthLink
        });
    } catch (error: any) {
        console.error('❌ Error reconnecting LinkedIn account:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to reconnect LinkedIn account',
            details: error.message
        });
    }
};

/**
 * Complete user signup after LinkedIn verification
 * POST /auth/signup/complete
 */
export const handleCompleteSignup = async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();

    try {
        const signupData = req.body;

        logRequest('POST', '/auth/signup/complete', undefined, { email: signupData.email });

        // Validate required fields
        if (!validateRequiredFields(res, signupData, ['email', 'first_name', 'last_name'])) {
            return;
        }

        // Validate email format
        if (!isValidEmail(signupData.email)) {
            sendValidationError(res, 'Invalid email format');
            return;
        }

        // Validate signup data using utility function
        const validation = validateUserSignup(signupData);
        if (!validation.isValid) {
            sendValidationError(res, 'Invalid signup data', validation.errors.join(', '));
            return;
        }

        const user = await createUser({
            email: signupData.email,
            first_name: signupData.first_name,
            last_name: signupData.last_name,
            linkedin_url: signupData.linkedin_url,
            linkedin_email: signupData.linkedin_email,
            account_id: signupData.account_id,
            linkedin_connection_status: signupData.linkedin_connection_status || 'pending',
            role: signupData.role || 'user',
            company_id: signupData.company_id
        });

        // Analytics initialization is now handled by frontend
        console.log('📊 Analytics initialization handled by frontend');

        sendCreated(res, user, 'User signup completed successfully');
        logResponse('POST', '/auth/signup/complete', true, Date.now() - startTime);
    } catch (error: any) {
        logResponse('POST', '/auth/signup/complete', false, Date.now() - startTime);
        handleAsyncError(res, error, 'completing signup');
    }
};
