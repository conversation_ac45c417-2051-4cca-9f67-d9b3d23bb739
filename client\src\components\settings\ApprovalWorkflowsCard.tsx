
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

interface ApprovalWorkflowsCardProps {
  adminApprovalsEnabled: boolean;
  updateApprovalSettings: (enabled: boolean) => Promise<void>;
}

const ApprovalWorkflowsCard: React.FC<ApprovalWorkflowsCardProps> = ({ 
  adminApprovalsEnabled, 
  updateApprovalSettings 
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Approval Workflows</CardTitle>
        <CardDescription>
          Configure content approval processes
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between py-2">
          <div className="flex items-center space-x-2">
            <Switch 
              id="admin-approval" 
              checked={adminApprovalsEnabled}
              onCheckedChange={updateApprovalSettings}
            />
            <Label htmlFor="admin-approval">
              Administrative Approvals
            </Label>
          </div>
          <div className="text-sm text-muted-foreground">
            {adminApprovalsEnabled 
              ? "All content requires admin approval before publishing" 
              : "Content is published without admin review"}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button className="bg-enterprise-blue hover:bg-enterprise-blue/90">
          Save Workflow Settings
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ApprovalWorkflowsCard;
