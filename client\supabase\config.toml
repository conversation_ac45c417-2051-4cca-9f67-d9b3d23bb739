
project_id = "qpvdmrvkrwhplskipfco"

[api]
enabled = true
port = 54321
schemas = ["public", "storage", "graphql_public"]

[db]
port = 54322

[studio]
port = 54323

[inbucket]
port = 54324

[auth]
enabled = true

[storage]
enabled = true

[realtime]
enabled = true

[analytics]
enabled = false

[vector]
enabled = false

[functions.send-invitation]
verify_jwt = true

[functions.accept-invitation]
verify_jwt = true

[functions.enhance-text]
verify_jwt = true

[functions.get-user-role]
verify_jwt = true

[functions.send-password-reset]
verify_jwt = true
