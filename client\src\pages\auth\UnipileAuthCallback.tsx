import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import PageBackground from '@/components/layout/PageBackground';

import { supabase } from '@/integrations/supabase/client';
import { analyticsService } from '@/services/analytics';
import { getApiUrl } from '@/config/env';

// Official Unipile Auth Callback Handler
const UnipileAuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('');
  const [accountId, setAccountId] = useState<string | null>(null);

  const saveLinkedInDataToSupabase = async (accountId: string, userEmail: string) => {
    console.log('💾 Saving LinkedIn data to Supabase:', { accountId, userEmail });

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('❌ No authenticated user found');
        return false;
      }

      // Fetch LinkedIn profile data from backend
      console.log('🔍 Fetching LinkedIn profile data from backend...');
      const profileResponse = await fetch(getApiUrl(`/auth/get-linkedin-profile/${accountId}`), {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      let linkedInProfile = null;
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        linkedInProfile = profileData.data;
        console.log('✅ LinkedIn profile data fetched:', linkedInProfile);
      } else {
        console.warn('⚠️ Could not fetch LinkedIn profile data, proceeding with account ID only');
      }

      // Extract first and last name from LinkedIn profile
      let firstName = '';
      let lastName = '';

      if (linkedInProfile?.name) {
        const nameParts = linkedInProfile.name.trim().split(' ');
        firstName = nameParts[0] || '';
        lastName = nameParts.slice(1).join(' ') || '';
      }

      // Also check if names are available in connection_params
      if (linkedInProfile?.connection_params?.im?.firstName) {
        firstName = linkedInProfile.connection_params.im.firstName;
      }
      if (linkedInProfile?.connection_params?.im?.lastName) {
        lastName = linkedInProfile.connection_params.im.lastName;
      }

      console.log('📝 Extracted names from LinkedIn profile:', {
        fullName: linkedInProfile?.name,
        firstName,
        lastName
      });

      // Update user profile in Supabase with complete LinkedIn data
      const now = new Date().toISOString();
      const updateData: any = {
        first_name: firstName || undefined, // Save name from LinkedIn
        last_name: lastName || undefined,   // Save name from LinkedIn
        account_id: accountId, // Use the correct field name
        linkedin_email: userEmail,
        linkedin_user_id: linkedInProfile?.connection_params?.im?.id || linkedInProfile?.id,
        linkedin_connection_status: 'connected',
        linkedin_connected_at: now, // Set connection timestamp
        linkedin_last_sync: now,
        linkedin_profile_data: linkedInProfile || {},
        linkedin_reconnection_count: 0, // First-time connection
        updated_at: now
      };

      // Add LinkedIn URL if available
      if (linkedInProfile?.connection_params?.im?.publicIdentifier) {
        updateData.linkedin_url = `https://linkedin.com/in/${linkedInProfile.connection_params.im.publicIdentifier}`;
      }

      console.log('💾 Updating Supabase profile with LinkedIn data:', updateData);

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', user.id);

      if (error) {
        console.error('❌ Error updating profile in Supabase:', error);
        return false;
      }

      console.log('✅ LinkedIn data saved to Supabase successfully');

      // Also update user metadata for quick access
      const { error: metadataError } = await supabase.auth.updateUser({
        data: {
          linkedinAccountId: accountId,
          linkedin_account_id: accountId,
          linkedinUserId: linkedInProfile?.connection_params?.im?.id || linkedInProfile?.id,
          linkedin_user_id: linkedInProfile?.connection_params?.im?.id || linkedInProfile?.id,
          linkedinEmail: userEmail,
          linkedin_email: userEmail,
          linkedinUrl: linkedInProfile?.connection_params?.im?.publicIdentifier
            ? `https://linkedin.com/in/${linkedInProfile.connection_params.im.publicIdentifier}`
            : null,
          linkedin_url: linkedInProfile?.connection_params?.im?.publicIdentifier
            ? `https://linkedin.com/in/${linkedInProfile.connection_params.im.publicIdentifier}`
            : null,
          linkedin_connected: true,
          linkedin_connected_at: now,
          linkedin_profile_name: linkedInProfile?.name
        }
      });

      if (metadataError) {
        console.warn('⚠️ Could not update user metadata:', metadataError);
      } else {
        console.log('✅ User metadata updated with LinkedIn data');
      }

      return true;
    } catch (error: any) {
      console.error('❌ Error saving LinkedIn data to Supabase:', error);
      return false;
    }
  };



  useEffect(() => {
    // Check URL parameters for success/failure (official Unipile pattern)
    const success = searchParams.get('success');
    const error = searchParams.get('error');
    const account_id = searchParams.get('account_id') || searchParams.get('accountId');
    const user_id = searchParams.get('userId'); // Official pattern uses 'userId'
    const type = searchParams.get('type');

    console.log('🔍 Unipile auth callback received:', {
      success,
      error,
      account_id,
      user_id,
      type,
      allParams: Object.fromEntries(searchParams.entries())
    });

    if (success === 'true') {
      console.log('✅ LinkedIn OAuth success callback received');

      // Check if this is a reconnection
      const isReconnection = type === 'reconnect';
      console.log(isReconnection ? '🔄 Reconnection detected' : '🆕 First-time connection detected');

      if (account_id && user_id) {
        // We have both account ID and user email - save to Supabase
        setAccountId(account_id);
        console.log('✅ Account ID received from callback:', account_id);

        // Save LinkedIn data to Supabase (frontend handles all database operations)
        saveLinkedInDataToSupabase(account_id, user_id).then((success) => {
          if (success) {
            setStatus('success');
            setMessage('LinkedIn account connected successfully!');
            toast.success('LinkedIn account verified and saved successfully!');

            // Track successful verification
            analyticsService.trackLinkedInVerificationSuccess(user_id, account_id);

            // Auto-redirect to dashboard after 3 seconds
            setTimeout(() => {
              handleContinue();
            }, 3000);
          } else {
            setStatus('error');
            setMessage('Failed to save LinkedIn data. Please try again.');
            toast.error('Failed to save LinkedIn data');
          }
        });

      } else {
        console.log('⚠️ Success callback but missing account ID or user email');
        setStatus('error');
        setMessage('LinkedIn connection succeeded but missing required data. Please try again.');
        toast.error('Missing LinkedIn account data');
      }

    } else if (error) {
      setStatus('error');
      setMessage(`Connection failed: ${decodeURIComponent(error)}`);
      console.error('❌ Auth callback error:', error);
      toast.error(`LinkedIn connection failed: ${decodeURIComponent(error)}`);

    } else {
      // If no clear success/error params, check for other indicators
      const status_param = searchParams.get('status');
      if (status_param === 'CREATION_SUCCESS' && account_id && user_id) {
        console.log('✅ LinkedIn account creation success via status param');
        setAccountId(account_id);

        // Save LinkedIn data to Supabase
        saveLinkedInDataToSupabase(account_id, user_id).then((success) => {
          if (success) {
            setStatus('success');
            setMessage('Account connection completed successfully!');
            toast.success('LinkedIn account connected and saved successfully!');

            // Track successful verification
            analyticsService.trackLinkedInVerificationSuccess(user_id, account_id);

            // Auto-redirect to dashboard after 3 seconds
            setTimeout(() => {
              handleContinue();
            }, 3000);
          } else {
            setStatus('error');
            setMessage('Failed to save LinkedIn data. Please try again.');
            toast.error('Failed to save LinkedIn data');
          }
        });
      } else {
        setStatus('loading');
        setMessage('Processing your account connection...');

        // Set a timeout to avoid infinite loading
        setTimeout(() => {
          if (status === 'loading') {
            setStatus('error');
            setMessage('Connection timeout. Please try again.');
          }
        }, 10000);
      }
    }
  }, [searchParams, status]);



  const handleContinue = async () => {
    console.log('🔄 Determining where to redirect user...');

    try {
      // Check if user is already authenticated
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        // User is already authenticated - redirect to dashboard
        console.log('✅ User is authenticated, redirecting to dashboard');
        navigate('/dashboard');
      } else {
        // User is not authenticated - redirect to signup to complete registration
        console.log('🔄 User not authenticated, redirecting to signup with account ID:', accountId);
        navigate('/signup?linkedin_verified=true');
      }
    } catch (error) {
      console.error('❌ Error checking user authentication:', error);
      // Default to signup if there's an error
      navigate('/signup?linkedin_verified=true');
    }
  };

  const handleRetry = () => {
    // Go back to signup to try again
    console.log('🔄 Redirecting to signup to retry');
    navigate('/signup');
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      <PageBackground />

      <div className="text-center p-8 relative z-10 max-w-md bg-white rounded-lg shadow-xl bg-opacity-95 backdrop-blur-sm border border-opacity-20">
        {status === 'loading' && (
          <div className="space-y-4">
            <div className="flex justify-center">
              <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900">Processing Connection</h2>
            <p className="text-gray-600">{message}</p>
            <div className="text-sm text-gray-500">
              <p>Please wait while we verify your LinkedIn account...</p>
            </div>
          </div>
        )}

        {status === 'success' && (
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
            </div>
            <h2 className="text-2xl font-semibold text-green-900">Success!</h2>
            <p className="text-gray-600">{message}</p>

            {accountId && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-sm text-green-800">
                  <strong>Account ID:</strong> {accountId}
                </p>
              </div>
            )}

            <Button
              onClick={handleContinue}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              Continue to Dashboard
            </Button>

            <div className="text-xs text-gray-500">
              <p>Your LinkedIn account has been successfully connected.</p>
              <p>Redirecting you to your dashboard...</p>
            </div>
          </div>
        )}

        {status === 'error' && (
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="p-3 bg-red-100 rounded-full">
                <XCircle className="h-12 w-12 text-red-600" />
              </div>
            </div>
            <h2 className="text-2xl font-semibold text-red-900">Connection Failed</h2>
            <p className="text-gray-600">{message}</p>

            <div className="space-y-2">
              <Button
                onClick={handleRetry}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                Try Again
              </Button>

              <Button
                onClick={() => navigate('/')}
                variant="outline"
                className="w-full"
              >
                Go to Home
              </Button>
            </div>

            <div className="text-xs text-gray-500">
              <p>If you continue to experience issues, please contact support.</p>
            </div>
          </div>
        )}

        {/* Debug Info */}
        <div className="mt-6 p-3 bg-gray-50 rounded-lg text-xs text-gray-600">
          <p><strong>Debug Info:</strong></p>
          <p>Status: {status}</p>
          <p>Account ID: {accountId || 'None'}</p>
          <p>URL Params: {searchParams.toString()}</p>
        </div>
      </div>
    </div>
  );
};

export default UnipileAuthCallback;
