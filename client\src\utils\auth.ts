
import { supabase } from '@/integrations/supabase/client';

interface UserRoleResponse {
  role: 'user' | 'admin';
  userId: string;
}

/**
 * Fetches the current user's role from the server
 * @returns Promise with user role information
 */
export const fetchUserRole = async (): Promise<UserRoleResponse> => {
  try {
    const { data: sessionData } = await supabase.auth.getSession();
    
    if (!sessionData.session) {
      throw new Error('No active session');
    }
    
    const { data, error } = await supabase.functions.invoke('get-user-role', {
      method: 'GET',
    });

    if (error) {
      console.error('Error fetching user role:', error);
      throw new Error(error.message || 'Failed to fetch user role');
    }
    
    return data as UserRoleResponse;
  } catch (error) {
    console.error('Error in fetchUserRole:', error);
    throw error;
  }
};
