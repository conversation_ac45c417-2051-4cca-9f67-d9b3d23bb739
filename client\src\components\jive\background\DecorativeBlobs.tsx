
import React from 'react';
import AnimatedEmoji from './AnimatedEmoji';

const DecorativeBlobs = () => {
  return (
    <div className="relative overflow-hidden">
      {/* Decorative blobs - background elements */}
      <div className="fixed top-0 left-0 w-72 h-72 bg-yellow-300 rounded-full filter blur-3xl opacity-20 -translate-x-1/2 -translate-y-1/2 animate-pulse"></div>
      <div className="fixed top-1/4 right-0 w-80 h-80 bg-orange-400 rounded-full filter blur-3xl opacity-15 translate-x-1/3 -translate-y-1/4"></div>
      <div className="fixed bottom-0 left-1/4 w-96 h-96 bg-blue-400 rounded-full filter blur-3xl opacity-10 -translate-x-1/2 translate-y-1/3"></div>
      <div className="fixed bottom-1/4 right-1/4 w-64 h-64 bg-teal-400 rounded-full filter blur-3xl opacity-15 translate-x-1/2 translate-y-1/4"></div>
      <div className="fixed top-1/2 left-1/2 w-72 h-72 bg-purple-400 rounded-full filter blur-3xl opacity-10 -translate-x-1/2 -translate-y-1/2"></div>
      <div className="fixed top-2/3 left-1/3 w-80 h-80 bg-pink-300 rounded-full filter blur-3xl opacity-10 -translate-x-1/2 -translate-y-1/2"></div>
      
      {/* Add animated emojis in more visible positions around the page */}
      <AnimatedEmoji position="top-[30%] left-[8%]" />
      <AnimatedEmoji position="top-[35%] right-[8%]" delay="0.5s" />
      <AnimatedEmoji position="bottom-[30%] left-[8%]" delay="1s" />
      <AnimatedEmoji position="bottom-[35%] right-[8%]" delay="1.5s" />
    </div>
  );
};

export default DecorativeBlobs;
