
import React from 'react';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useLinkedInConnection } from '@/hooks/useLinkedInConnection';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface UserHeaderProps {
  onLinkedInRequired?: () => void;
}

const UserHeader = ({ onLinkedInRequired }: UserHeaderProps = {}) => {
  const { profile } = useAuth();
  const navigate = useNavigate();
  const { isLinkedInConnected, isLoading, isInitialized } = useLinkedInConnection();

  const firstName = profile?.first_name || 'User';

  const handleCreatePostClick = () => {
    console.log('🔍 Create Post Click:', {
      isLinkedInConnected,
      isLoading,
      isInitialized
    });

    if (!isLoading && !isLinkedInConnected && onLinkedInRequired) {
      // Trigger LinkedIn connection popup for returning users
      onLinkedInRequired();
    } else if (isLinkedInConnected) {
      navigate('/content-creator');
    }
  };

  // Show loading state while checking connection
  if (isLoading) {
    console.log('🔄 UserHeader: Still loading LinkedIn connection status');
  }

  return (
    <div className="mb-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold mb-1 text-black">
            Welcome, {firstName}!
          </h1>
          <p className="text-gray-600">
            Track your LinkedIn performance and share your company's story
          </p>
        </div>

        <div className="flex w-full md:w-auto">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className={`${!isLinkedInConnected && !isLoading ? 'cursor-pointer' : ''}`}>
                  <Button
                    variant="outline"
                    className="flex-1 md:flex-none bg-black hover:bg-black/90 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={handleCreatePostClick}
                    disabled={isLoading}
                  >
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {isLoading ? 'Checking...' : 'Create Post'}
                  </Button>
                </div>
              </TooltipTrigger>
              {(!isLinkedInConnected && !isLoading) && (
                <TooltipContent>
                  <p>Connect your LinkedIn account to create posts</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
};

export default UserHeader;
