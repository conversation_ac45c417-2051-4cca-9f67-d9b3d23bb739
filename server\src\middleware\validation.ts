import { Request, Response, NextFunction } from 'express';

export interface ValidationError {
  field: string;
  message: string;
}

export class ValidationMiddleware {


  /**
   * Validate signup completion request
   */
  static validateSignupComplete = (req: Request, res: Response, next: NextFunction) => {
    const {
      email,
      password,
      linkedin_email,
      first_name,
      last_name,
      account_id,
      use_same_credentials
    } = req.body;
    const errors: ValidationError[] = [];

    // LinkedIn email is required (password not stored for security)
    if (!linkedin_email || !ValidationMiddleware.isValidEmail(linkedin_email)) {
      errors.push({ field: 'linkedin_email', message: 'Valid LinkedIn email is required' });
    }

    if (!account_id) {
      errors.push({ field: 'account_id', message: 'LinkedIn account must be verified first' });
    }

    // App credentials (if not using same credentials)
    if (!use_same_credentials) {
      if (!email || !ValidationMiddleware.isValidEmail(email)) {
        errors.push({ field: 'email', message: 'Valid app email is required' });
      }

      if (!password || password.length < 8) {
        errors.push({ field: 'password', message: 'App password must be at least 8 characters' });
      }
    }

    // User info
    if (!first_name || first_name.trim() === '') {
      errors.push({ field: 'first_name', message: 'First name is required' });
    }

    if (!last_name || last_name.trim() === '') {
      errors.push({ field: 'last_name', message: 'Last name is required' });
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors
      });
    }

    next();
  };

  /**
   * Validate analytics request
   */
  static validateAnalyticsRequest = (req: Request, res: Response, next: NextFunction) => {
    const { userId } = req.params;
    const errors: ValidationError[] = [];

    if (!userId || typeof userId !== 'string' || userId.trim() === '') {
      errors.push({ field: 'userId', message: 'User ID is required' });
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors
      });
    }

    next();
  };

  /**
   * Validate analytics initialization request
   */
  static validateAnalyticsInit = (req: Request, res: Response, next: NextFunction) => {
    const { userId, linkedinAccountId } = req.body;
    const errors: ValidationError[] = [];

    if (!userId || typeof userId !== 'string' || userId.trim() === '') {
      errors.push({ field: 'userId', message: 'User ID is required' });
    }

    if (!linkedinAccountId || typeof linkedinAccountId !== 'string' || linkedinAccountId.trim() === '') {
      errors.push({ field: 'linkedinAccountId', message: 'LinkedIn Account ID is required' });
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors
      });
    }

    next();
  };

  /**
   * Helper method to validate email format
   */
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

/**
 * Error handling middleware
 */
export const errorHandler = (error: any, _req: Request, res: Response, _next: NextFunction) => {
  console.error('Unhandled error:', error);

  // Default error response
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal server error';

  res.status(statusCode).json({
    success: false,
    error: message,
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
};

/**
 * Not found middleware
 */
export const notFoundHandler = (req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: `Route ${req.method} ${req.path} not found`
  });
};
