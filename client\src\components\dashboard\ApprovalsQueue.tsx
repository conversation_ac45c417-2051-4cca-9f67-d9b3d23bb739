
import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Clock, Edit, FileText, ThumbsDown, ThumbsUp, User, Loader2 } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ContentCard from '../ui/ContentCard';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { Post, ApprovalPost } from '@/types/post';
import { Avatar, AvatarImage, AvatarFallback } from '@radix-ui/react-avatar';
import { formatDistanceToNow } from 'date-fns';

// Define expected user profile shape
interface UserProfile {
  first_name?: string | null;
  last_name?: string | null;
  email: string;
  avatar_url?: string | null;
}

// Update Post type to match the expected user profile
interface UpdatedPost extends Omit<Post, 'user'> {
  user: UserProfile | null;
}

const ApprovalsQueue = () => {
  const { toast } = useToast();
  const { profile } = useAuth();
  const [pendingApprovals, setPendingApprovals] = useState<UpdatedPost[]>([]);
  const [approvedContent, setApprovedContent] = useState<UpdatedPost[]>([]);
  const [rejectedContent, setRejectedContent] = useState<UpdatedPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [processingItems, setProcessingItems] = useState<Record<string, boolean>>({});

  useEffect(() => {
    fetchPosts();
    
    // Set up realtime subscription for new posts
    const channel = supabase
      .channel('post-changes')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'employee_posts' },
        (payload) => {
          console.log('New post added:', payload);
          if (payload.new && (payload.new as any).status === 'pending_approval') {
            fetchPosts();
            toast({
              title: "New post for review",
              description: "A new post has been submitted for your approval."
            });
          }
        }
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
    };
  }, [profile?.id]);

  const fetchPosts = async () => {
    try {
      setIsLoading(true);
      
      // Get company ID for admin
      const { data: memberData, error: memberError } = await supabase
        .from('company_members')
        .select('company_id')
        .eq('user_id', profile?.id)
        .single();
        
      if (memberError) {
        console.error('Error fetching company ID:', memberError);
        return;
      }
      
      if (!memberData?.company_id) {
        console.error('No company ID found for the user');
        return;
      }
      
      // Get all company members to find their posts
      const { data: companyMembers, error: companyError } = await supabase
        .from('company_members')
        .select('user_id')
        .eq('company_id', memberData.company_id);
        
      if (companyError) {
        console.error('Error fetching company members:', companyError);
        return;
      }
      
      // Extract user IDs
      const userIds = companyMembers.map(member => member.user_id);
      
      if (userIds.length === 0) {
        setIsLoading(false);
        return;
      }
      
      // Fetch posts with user information
      const { data: posts, error: postsError } = await supabase
        .from('employee_posts')
        .select(`
          *,
          user:profiles(
            first_name,
            last_name,
            email
          )
        `)
        .in('user_id', userIds)
        .order('created_at', { ascending: false });
        
      if (postsError) {
        console.error('Error fetching posts:', postsError);
        return;
      }
      
      // Convert and filter posts by status
      const typedPosts: Post[] = posts.map(post => {
        const postData: Post = {
          id: post.id,
          user_id: post.user_id,
          title: post.title,
          content: post.content,
          post_date: post.post_date,
          created_at: post.created_at || '',
          updated_at: post.updated_at,
          post_type: post.post_type,
          department: post.department,
          impressions: post.impressions,
          likes: post.likes,
          comments: post.comments,
          engagement_rate: post.engagement_rate,
          status: post.status as 'draft' | 'pending_approval' | 'published' | 'approved' | 'rejected',
          user: null
        };
        
        // Handle user data safely - could be null or an error object
        if (post.user && typeof post.user === 'object' && !('error' in post.user)) {
          postData.user = {
            first_name: post.user.first_name || null,
            last_name: post.user.last_name || null,
            email: post.user.email || ''
          };
        }
        
        return postData;
      });
      
      // Filter posts by status
      const pending = typedPosts.filter(post => post.status === 'pending_approval');
      const approved = typedPosts.filter(post => post.status === 'approved');
      const rejected = typedPosts.filter(post => post.status === 'rejected');
      
      setPendingApprovals(pending);
      setApprovedContent(approved);
      setRejectedContent(rejected);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (postId: string) => {
    setProcessingItems(prev => ({ ...prev, [postId]: true }));
    
    try {
      const { error } = await supabase
        .from('employee_posts')
        .update({ 
          status: 'approved',
          post_date: new Date().toISOString() 
        })
        .eq('id', postId);
        
      if (error) {
        throw error;
      }
      
      // Update local state
      setPendingApprovals(prev => prev.filter(post => post.id !== postId));
      const approvedPost = pendingApprovals.find(post => post.id === postId);
      if (approvedPost) {
        setApprovedContent(prev => [{ ...approvedPost, status: 'approved' }, ...prev]);
      }
      
      toast({
        title: "Post approved",
        description: "The post has been approved and is now published."
      });
    } catch (error) {
      console.error('Error approving post:', error);
      toast({
        title: "Approval failed",
        description: "There was an error approving this post. Please try again.",
        variant: "destructive"
      });
    } finally {
      setProcessingItems(prev => ({ ...prev, [postId]: false }));
    }
  };

  const handleReject = async (postId: string) => {
    setProcessingItems(prev => ({ ...prev, [postId]: true }));
    
    try {
      const { error } = await supabase
        .from('employee_posts')
        .update({ status: 'rejected' })
        .eq('id', postId);
        
      if (error) {
        throw error;
      }
      
      // Update local state
      setPendingApprovals(prev => prev.filter(post => post.id !== postId));
      const rejectedPost = pendingApprovals.find(post => post.id === postId);
      if (rejectedPost) {
        setRejectedContent(prev => [{ ...rejectedPost, status: 'rejected' }, ...prev]);
      }
      
      toast({
        title: "Post rejected",
        description: "The post has been rejected and won't be published."
      });
    } catch (error) {
      console.error('Error rejecting post:', error);
      toast({
        title: "Rejection failed",
        description: "There was an error rejecting this post. Please try again.",
        variant: "destructive"
      });
    } finally {
      setProcessingItems(prev => ({ ...prev, [postId]: false }));
    }
  };

  // Helper function to format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  // Helper function to safely get user display name
  const getUserDisplayName = (post: UpdatedPost) => {
    if (!post.user) return 'Unknown User';
    
    const firstName = post.user.first_name || '';
    const lastName = post.user.last_name || '';
    
    if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim();
    }
    
    return post.user.email || 'Unknown User';
  };

  // Helper function to safely get user department
  const getUserDepartment = (post: UpdatedPost) => {
    return post.department || 'N/A';
  };

  // Helper function to safely get user avatar text
  const getUserAvatarText = (post: UpdatedPost) => {
    if (!post.user) return 'U';
    
    const firstName = post.user.first_name || '';
    const lastName = post.user.last_name || '';
    
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`;
    } else if (firstName) {
      return firstName.charAt(0);
    } else if (lastName) {
      return lastName.charAt(0);
    } else if (post.user.email) {
      return post.user.email.charAt(0).toUpperCase();
    }
    
    return 'U';
  };

  // Function to create content card props from post data
  const createContentCardProps = (post: UpdatedPost, status: string): ApprovalPost => {
    return {
      id: post.id,
      title: post.title,
      content: post.content,
      author: getUserDisplayName(post),
      department: getUserDepartment(post),
      platform: post.post_type === 'company' ? 'Company LinkedIn' : 'Personal LinkedIn',
      submitted: formatDate(post.created_at),
      ...(status === 'approved' && { approved: formatDate(post.post_date || '') }),
      ...(status === 'rejected' && { rejected: formatDate(post.updated_at || '') })
    };
  };

  return (
    <div className="space-y-6">
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Content Approval Queue</CardTitle>
          <CardDescription>
            Review and approve content created by your team
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="pending" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="pending" className="flex items-center">
                <Clock className="mr-1 h-4 w-4" />
                <span>Pending</span>
                <Badge className="ml-2 bg-amber-500">{pendingApprovals.length}</Badge>
              </TabsTrigger>
              <TabsTrigger value="approved">
                <CheckCircle className="mr-1 h-4 w-4" />
                <span>Approved</span>
              </TabsTrigger>
              <TabsTrigger value="rejected">
                <ThumbsDown className="mr-1 h-4 w-4" />
                <span>Rejected</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="pending" className="space-y-4 mt-4">
              {isLoading ? (
                <div className="text-center py-8">
                  <Loader2 className="mx-auto h-8 w-8 animate-spin text-enterprise-blue" />
                  <p className="mt-2 text-muted-foreground">Loading content...</p>
                </div>
              ) : pendingApprovals.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                  <p className="mt-2 text-muted-foreground">No pending content to review</p>
                </div>
              ) : (
                pendingApprovals.map((post) => (
                  <Card key={post.id} className="overflow-hidden">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={post.user?.avatar_url || undefined} />
                            <AvatarFallback className="bg-enterprise-blue text-white">
                              {post.user?.first_name?.charAt(0) || ''}
                              {post.user?.last_name?.charAt(0) || ''}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <CardTitle className="text-base">
                              {post.title}
                            </CardTitle>
                            <div className="flex items-center text-sm text-enterprise-gray-600">
                              <span>{post.user?.first_name || 'Unknown'} {post.user?.last_name || 'User'}</span>
                              <span className="mx-1">•</span>
                              <span>{formatDistanceToNow(new Date(post.created_at))} ago</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button 
                            size="sm" 
                            variant="outline" 
                            className="border-red-200 hover:bg-red-50"
                            onClick={() => handleReject(post.id)}
                            disabled={processingItems[post.id]}
                          >
                            {processingItems[post.id] ? (
                              <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                            ) : (
                              <ThumbsDown className="mr-1 h-4 w-4" />
                            )}
                            Reject
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline" 
                            className="border-amber-200 hover:bg-amber-50"
                          >
                            <Edit className="mr-1 h-4 w-4" />
                            Suggest Edits
                          </Button>
                          <Button 
                            size="sm" 
                            className="bg-green-600 hover:bg-green-700"
                            onClick={() => handleApprove(post.id)}
                            disabled={processingItems[post.id]}
                          >
                            {processingItems[post.id] ? (
                              <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                            ) : (
                              <ThumbsUp className="mr-1 h-4 w-4" />
                            )}
                            Approve
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={post.user?.avatar_url || undefined} />
                              <AvatarFallback className="bg-enterprise-blue text-white">
                                {post.user?.first_name?.charAt(0) || ''}
                                {post.user?.last_name?.charAt(0) || ''}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="text-sm font-medium text-enterprise-gray-600">
                                {post.user?.first_name || 'Unknown'} {post.user?.last_name || 'User'}
                              </p>
                              <p className="text-sm text-enterprise-gray-500">
                                {getUserDepartment(post)}
                              </p>
                            </div>
                          </div>
                          <p className="text-sm text-enterprise-gray-500">
                            {post.impressions} impressions
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-sm text-enterprise-gray-500">
                            {post.likes} likes
                          </p>
                          <p className="text-sm text-enterprise-gray-500">
                            {post.comments} comments
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </TabsContent>
            
            <TabsContent value="approved" className="space-y-4 mt-4">
              {isLoading ? (
                <div className="text-center py-8">
                  <Loader2 className="mx-auto h-8 w-8 animate-spin text-enterprise-blue" />
                  <p className="mt-2 text-muted-foreground">Loading content...</p>
                </div>
              ) : approvedContent.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                  <p className="mt-2 text-muted-foreground">No approved content yet</p>
                </div>
              ) : (
                approvedContent.map((post) => (
                  <ContentCard 
                    key={post.id}
                    content={createContentCardProps(post, 'approved')}
                    status="approved"
                  />
                ))
              )}
            </TabsContent>
            
            <TabsContent value="rejected" className="space-y-4 mt-4">
              {isLoading ? (
                <div className="text-center py-8">
                  <Loader2 className="mx-auto h-8 w-8 animate-spin text-enterprise-blue" />
                  <p className="mt-2 text-muted-foreground">Loading content...</p>
                </div>
              ) : rejectedContent.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                  <p className="mt-2 text-muted-foreground">No rejected content</p>
                </div>
              ) : (
                rejectedContent.map((post) => (
                  <ContentCard 
                    key={post.id}
                    content={createContentCardProps(post, 'rejected')}
                    status="rejected"
                    rejectionReason="This content does not align with our brand guidelines."
                  />
                ))
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApprovalsQueue;
