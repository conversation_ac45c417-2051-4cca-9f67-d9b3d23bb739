-- Phase 2: Add attachments_links column to public_users_posts table
-- This migration adds support for storing LinkedIn post attachments

-- Step 1: Add attachments_links column to store array of attachment URLs
ALTER TABLE public.public_users_posts 
ADD COLUMN IF NOT EXISTS attachments_links text[];

-- Step 2: Add comment for documentation
COMMENT ON COLUMN public.public_users_posts.attachments_links IS 'Array of attachment URLs from LinkedIn posts (images, videos, documents)';

-- Step 3: Create index for attachments queries (optional, for performance)
CREATE INDEX IF NOT EXISTS idx_public_users_posts_has_attachments 
ON public.public_users_posts(user_id) 
WHERE attachments_links IS NOT NULL AND array_length(attachments_links, 1) > 0;

-- Step 4: Verification query
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'public_users_posts' 
AND column_name = 'attachments_links';

-- Step 5: Show sample of existing data structure
SELECT 
    id,
    linkedin_post_id,
    title,
    attachments_links,
    CASE 
        WHEN attachments_links IS NULL THEN 'No attachments'
        WHEN array_length(attachments_links, 1) IS NULL THEN 'Empty array'
        ELSE 'Has ' || array_length(attachments_links, 1) || ' attachments'
    END as attachment_status
FROM public.public_users_posts 
LIMIT 5;
