import { useMemo, useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';

/**
 * Custom hook to check if user is connected to LinkedIn
 * Uses the same logic as UserDashboard.tsx for consistency
 * Properly handles loading states and initialization
 */
export const useLinkedInConnection = () => {
  const { user, profile, isLoading: authLoading } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize when user and profile are loaded
  useEffect(() => {
    /**
     * 
     */
    if (!authLoading && user && profile !== undefined) {
      setIsInitialized(true);
    }
  }, [authLoading, user, profile]);

  const isLinkedInConnected = useMemo(() => {
    // Don't return false immediately if not initialized
    if (!isInitialized || !user) return null;

    // Check if LinkedIn is connected using the same logic as UserDashboard
    const userMetadata = user?.user_metadata;
    const hasLinkedInAccountId = userMetadata?.linkedinAccountId || userMetadata?.linkedin_account_id;
    const hasLinkedInProfile = profile?.linkedin_account_id || profile?.linkedin_url;

    console.log('🔍 LinkedIn Connection Check:', {
      userId: user?.id,
      hasLinkedInAccountId: !!hasLinkedInAccountId,
      hasLinkedInProfile: !!hasLinkedInProfile,
      userMetadata: userMetadata,
      profile: profile,
      isInitialized
    });

    return !!(hasLinkedInAccountId || hasLinkedInProfile);
  }, [user, profile, isInitialized]);

  const isLoading = authLoading || !isInitialized || isLinkedInConnected === null;

  return {
    isLinkedInConnected: isLinkedInConnected === null ? false : isLinkedInConnected,
    isLoading,
    isInitialized
  };
};
