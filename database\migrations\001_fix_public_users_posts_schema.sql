-- Phase 1: Fix and enhance public_users_posts table schema
-- This migration corrects naming issues and adds sync tracking capabilities

-- Step 1: Drop existing table if it exists with wrong constraints
DROP TABLE IF EXISTS public.public_users_posts CASCADE;

-- Step 2: Create the corrected public_users_posts table
CREATE TABLE public.public_users_posts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  
  -- LinkedIn Integration Fields
  linkedin_post_id text UNIQUE NOT NULL, -- Unique LinkedIn post identifier
  linkedin_post_url text, -- Direct URL to LinkedIn post
  
  -- Post Content Fields
  title text NOT NULL,
  content text NOT NULL,
  post_type public.post_type NOT NULL,
  department public.department NOT NULL,
  
  -- Analytics Fields
  impressions integer DEFAULT 0,
  likes integer DEFAULT 0,
  comments integer DEFAULT 0,
  shares integer DEFAULT 0, -- Added shares tracking
  engagement_rate numeric(5, 2) DEFAULT 0,
  
  -- Timing Fields
  post_date timestamp with time zone NOT NULL, -- When post was originally published on LinkedIn
  created_at timestamp with time zone DEFAULT now(), -- When record was created in our DB
  updated_at timestamp with time zone DEFAULT now(), -- When record was last updated
  
  -- Sync Tracking Fields
  last_synced_at timestamp with time zone DEFAULT now(), -- Last time we synced this post
  sync_source text DEFAULT 'unipile', -- Source of the sync (unipile, manual, etc.)
  
  -- Status Tracking Fields
  is_newly_added boolean DEFAULT true, -- Mark posts as new for highlighting
  is_updated boolean DEFAULT false, -- Mark posts that had analytics updates
  sync_status text DEFAULT 'synced' CHECK (sync_status IN ('synced', 'pending', 'failed', 'skipped')),
  
  -- Post Status
  status text DEFAULT 'published' CHECK (status IN ('draft', 'published', 'archived', 'deleted')),
  
  -- Error Tracking
  last_sync_error text, -- Store any sync errors for debugging
  sync_retry_count integer DEFAULT 0, -- Track retry attempts
  
  -- Constraints
  CONSTRAINT public_users_posts_pkey PRIMARY KEY (id),
  CONSTRAINT public_users_posts_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
  CONSTRAINT public_users_posts_linkedin_id_unique UNIQUE (linkedin_post_id)
);

-- Step 3: Create optimized indexes for performance
CREATE INDEX idx_public_users_posts_user_id ON public.public_users_posts(user_id);
CREATE INDEX idx_public_users_posts_linkedin_id ON public.public_users_posts(linkedin_post_id);
CREATE INDEX idx_public_users_posts_post_date ON public.public_users_posts(post_date DESC);
CREATE INDEX idx_public_users_posts_last_synced ON public.public_users_posts(last_synced_at);
CREATE INDEX idx_public_users_posts_sync_status ON public.public_users_posts(sync_status);

-- Partial indexes for performance optimization
CREATE INDEX idx_public_users_posts_newly_added ON public.public_users_posts(user_id, created_at) 
WHERE is_newly_added = true;

CREATE INDEX idx_public_users_posts_needs_sync ON public.public_users_posts(user_id, last_synced_at) 
WHERE sync_status IN ('pending', 'failed');

CREATE INDEX idx_public_users_posts_updated ON public.public_users_posts(user_id, updated_at) 
WHERE is_updated = true;

-- Step 4: Create trigger for automatic updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_public_users_posts_updated_at 
    BEFORE UPDATE ON public.public_users_posts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Step 5: Create function to calculate engagement rate
CREATE OR REPLACE FUNCTION calculate_engagement_rate(
    p_impressions integer,
    p_likes integer,
    p_comments integer,
    p_shares integer
) RETURNS numeric(5,2) AS $$
BEGIN
    -- Avoid division by zero
    IF p_impressions = 0 OR p_impressions IS NULL THEN
        RETURN 0.00;
    END IF;
    
    -- Calculate engagement rate: (likes + comments + shares) / impressions * 100
    RETURN ROUND(
        ((COALESCE(p_likes, 0) + COALESCE(p_comments, 0) + COALESCE(p_shares, 0))::numeric / p_impressions::numeric) * 100,
        2
    );
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create trigger to auto-calculate engagement rate
CREATE OR REPLACE FUNCTION auto_calculate_engagement_rate()
RETURNS TRIGGER AS $$
BEGIN
    NEW.engagement_rate = calculate_engagement_rate(
        NEW.impressions,
        NEW.likes,
        NEW.comments,
        NEW.shares
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_engagement_rate_trigger
    BEFORE INSERT OR UPDATE ON public.public_users_posts
    FOR EACH ROW
    EXECUTE FUNCTION auto_calculate_engagement_rate();

-- Step 7: Add helpful comments for documentation
COMMENT ON TABLE public.public_users_posts IS 'Stores LinkedIn posts for public users with sync tracking and analytics';
COMMENT ON COLUMN public.public_users_posts.linkedin_post_id IS 'Unique identifier from LinkedIn/Unipile API';
COMMENT ON COLUMN public.public_users_posts.is_newly_added IS 'Flag to highlight new posts in UI';
COMMENT ON COLUMN public.public_users_posts.is_updated IS 'Flag to highlight posts with updated analytics';
COMMENT ON COLUMN public.public_users_posts.sync_status IS 'Current sync status for monitoring';
COMMENT ON COLUMN public.public_users_posts.engagement_rate IS 'Auto-calculated engagement percentage';
