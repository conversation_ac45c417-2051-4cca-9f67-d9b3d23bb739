import { Router } from 'express';
import {
    handleTrackAction,
    handleGetOnboardingMetrics,
    handleGetUserJourney,
    handleSyncAnalytics,
    handleGetAnalyticsData,
    handleGetProfile,
    handleInitializeAnalytics,
    handleGetDashboardData
} from '../handlers/analytics-legacy.handlers';

/**
 *  Analytics Routes
 */

const router = Router();

// User action tracking
router.post('/track', handleTrackAction);                           // POST /analytics/track

// Onboarding analytics
router.get('/onboarding/metrics', handleGetOnboardingMetrics);      // GET /analytics/onboarding/metrics

router.get('/journey/:sessionId', handleGetUserJourney);            // GET /analytics/journey/:sessionId

// User analytics operations
router.post('/sync/:userId', handleSyncAnalytics);                  // POST /analytics/sync/:userId

router.get('/:userId', handleGetAnalyticsData);                     // GET /analytics/:userId

router.get('/profile/:userId', handleGetProfile);                   // GET /analytics/profile/:userId

router.post('/initialize', handleInitializeAnalytics);              // POST /analytics/initialize

// Dashboard operations
router.get('/dashboard/:userId', handleGetDashboardData);           // GET /analytics/dashboard/:userId

export default router;
