import { User, UserProfile, LinkedInAnalytics, CreateUserRequest } from '../types/user';
import { createClient } from '@supabase/supabase-js';

// Supabase configuration (same as postStorageService.ts)
const SUPABASE_URL = process.env.SUPABASE_URL || '';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

console.log('🔧 Supabase Configuration Check:', {
    hasUrl: !!SUPABASE_URL,
    urlLength: SUPABASE_URL.length,
    urlPrefix: SUPABASE_URL.substring(0, 30) + '...',
    hasServiceKey: !!SUPABASE_SERVICE_KEY,
    serviceKeyLength: SUPABASE_SERVICE_KEY.length,
    serviceKeyPrefix: SUPABASE_SERVICE_KEY.substring(0, 20) + '...'
});

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    console.error('❌ Missing Supabase configuration:', {
        SUPABASE_URL: !!SUPABASE_URL,
        SUPABASE_SERVICE_ROLE_KEY: !!SUPABASE_SERVICE_KEY
    });
    throw new Error('Missing Supabase configuration. Please check SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.');
}

// Create Supabase client with service role key for server-side operations
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
console.log('✅ Supabase client created successfully');

/**
 * Functional User Service
 * Pure functions for user data operations - no classes, easy to test and debug
 * NOTE: This project handles ALL database operations on the frontend
 * Backend only provides mock responses for API compatibility
 */

/**
 * Generate a unique ID (placeholder implementation)
 */
const generateId = (): string => {
    return Math.random().toString(36).substring(2, 11);
};

/**
 * Create a new user (Mock response - Frontend handles actual database operations)
 */
export const createUser = async (userData: CreateUserRequest): Promise<User> => {
    try {
        console.log('🔄 Mock user creation (Frontend handles actual database):', userData.email);

        // Return mock user - Frontend handles actual Supabase operations
        const mockUser: User = {
            id: userData.id || generateId(),
            email: userData.email!,
            first_name: userData.first_name,
            last_name: userData.last_name,
            linkedin_url: userData.linkedin_url,
            linkedin_email: userData.linkedin_email,
            account_id: userData.account_id,
            linkedin_connection_status: userData.linkedin_connection_status || 'not_connected',
            role: userData.role || 'user',
            company_id: userData.company_id,
            created_at: new Date(),
            updated_at: new Date()
        };

        console.log('✅ Mock user created (Frontend will handle actual persistence):', mockUser.id);
        return mockUser;
    } catch (error: any) {
        console.error('❌ Error creating mock user:', error);
        throw new Error(`Failed to create user: ${error.message}`);
    }
};

/**
 * Get user by ID
 */
export const getUserById = async (userId: string): Promise<User | null> => {
    try {
        console.log('🔍 Fetching user by ID from profiles table:', userId);
        console.log('🔗 Supabase URL:', process.env.SUPABASE_URL);
        console.log('🔑 Using service role key:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);

        const { data, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

        console.log('📋 Raw Supabase response:', {
            hasData: !!data,
            hasError: !!error,
            errorCode: error?.code,
            errorMessage: error?.message,
            dataKeys: data ? Object.keys(data) : null,
            dataContent: data ? JSON.stringify(data, null, 2) : null
        });

        if (error) {
            if (error.code === 'PGRST116') {
                console.log('⚠️ User not found in profiles table:', userId);
                return null;
            }
            console.error('❌ Database error details:', {
                code: error.code,
                message: error.message,
                details: error.details,
                hint: error.hint
            });
            throw new Error(`Database error: ${error.message}`);
        }

        if (!data) {
            console.log('⚠️ No user data returned for ID:', userId);
            return null;
        }

        // Map profiles table data to User interface
        const user: User = {
            id: data.id,
            email: data.linkedin_email,
            first_name: data.first_name || 'Unknown',
            last_name: data.last_name || 'User',
            account_id: data.account_id || null,
            linkedin_user_id: data.linkedin_user_id || null,
            linkedin_connection_status: data.linkedin_connection_status || 'disconnected',
            role: data.role || 'user',
            created_at: new Date(data.created_at),
            updated_at: new Date(data.updated_at || data.created_at)
        };

        console.log('✅ Real user found from profiles table:', {
            id: user.id,
            email: user.email,
            account_id: user.account_id, // Unipile account ID
            linkedin_user_id: user.linkedin_user_id, // LinkedIn internal user ID
            linkedin_connection_status: user.linkedin_connection_status
        });

        return user;
    } catch (error: any) {
        console.error('❌ Error fetching user from database:', error);
        throw new Error(`Failed to fetch user: ${error.message}`);
    }
};

/**
 * Get user by email
 */
export const getUserByEmail = async (email: string): Promise<User | null> => {
    try {
        console.log('🔍 Fetching user by email:', email);
        // In a real app, fetch from database
        return null; // Placeholder
    } catch (error: any) {
        console.error('❌ Error fetching user by email:', error);
        throw new Error(`Failed to fetch user by email: ${error.message}`);
    }
};

/**
 * Update user
 */
export const updateUser = async (userId: string, updates: Partial<User>): Promise<User> => {
    try {
        console.log('🔄 Updating user:', userId, updates);

        // Placeholder return
        const updatedUser: User = {
            id: userId,
            email: '<EMAIL>',
            linkedin_connection_status: 'connected',
            role: 'user',
            created_at: new Date(),
            updated_at: new Date(),
            ...updates
        } as User;

        console.log('✅ User updated:', updatedUser.id);
        return updatedUser;
    } catch (error: any) {
        console.error('❌ Error updating user:', error);
        throw new Error(`Failed to update user: ${error.message}`);
    }
};

/**
 * Initialize analytics tracking for a user
 */
export const initializeAnalytics = async (userId: string, linkedinAccountId: string): Promise<LinkedInAnalytics> => {
    try {
        console.log('🔄 Initializing analytics for user:', userId);

        const analytics: LinkedInAnalytics = {
            id: generateId(),
            user_id: userId,
            account_id: linkedinAccountId,
            sync_status: 'pending',
            created_at: new Date(),
            updated_at: new Date()
        };

        console.log('✅ Analytics initialized:', analytics.id);
        return analytics;
    } catch (error: any) {
        console.error('❌ Error initializing analytics:', error);
        throw new Error(`Failed to initialize analytics: ${error.message}`);
    }
};

/**
 * Update analytics data
 */
export const updateAnalytics = async (userId: string, analyticsData: Partial<LinkedInAnalytics>): Promise<LinkedInAnalytics> => {
    try {
        console.log('🔄 Updating analytics for user:', userId);

        // Placeholder return
        const updatedAnalytics: LinkedInAnalytics = {
            id: generateId(),
            user_id: userId,
            account_id: 'placeholder',
            sync_status: 'completed',
            created_at: new Date(),
            updated_at: new Date(),
            ...analyticsData
        } as LinkedInAnalytics;

        console.log('✅ Analytics updated:', updatedAnalytics.id);
        return updatedAnalytics;
    } catch (error: any) {
        console.error('❌ Error updating analytics:', error);
        throw new Error(`Failed to update analytics: ${error.message}`);
    }
};

/**
 * Get analytics for a user
 */
export const getAnalytics = async (userId: string): Promise<LinkedInAnalytics | null> => {
    try {
        console.log('🔍 Fetching analytics for user:', userId);
        // In a real app, fetch from database
        return null; // Placeholder
    } catch (error: any) {
        console.error('❌ Error fetching analytics:', error);
        throw new Error(`Failed to fetch analytics: ${error.message}`);
    }
};

/**
 * Get user's LinkedIn account ID
 */
export const getUserLinkedInAccountId = async (userId: string): Promise<string | null> => {
    try {
        console.log('🔍 Getting LinkedIn account ID for user:', userId);
        const user = await getUserById(userId);
        return user?.account_id || null;
    } catch (error: any) {
        console.error('❌ Error getting LinkedIn account ID:', error);
        throw new Error(`Failed to get LinkedIn account ID: ${error.message}`);
    }
};
