
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON><PERSON>, Loader2, AlertCircle } from 'lucide-react';

interface EnhanceableTextSectionProps {
  id: string;
  label: string;
  value: string;
  placeholder?: string;
  description?: string;
  onChange: (value: string) => void;
  onEnhance: () => void;
  isEnhancing: boolean;
  rows?: number;
  enhancementType?: string;
}

const EnhanceableTextSection = ({
  id,
  label,
  value,
  placeholder,
  description,
  onChange,
  onEnhance,
  isEnhancing,
  rows = 4,
  enhancementType
}: EnhanceableTextSectionProps) => {
  // Add a local state to track if the current value has been modified
  // This helps with visual feedback when editing
  const [isModified, setIsModified] = useState(false);
  
  // When the value prop changes from outside, reset the modified state
  useEffect(() => {
    setIsModified(false);
  }, [value]);
  
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Mark as modified when user types
    setIsModified(true);
    // Call the parent's onChange handler
    onChange(e.target.value);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label htmlFor={id} className="text-base">{label}</Label>
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => {
            // Reset modified state when enhancing
            setIsModified(false);
            onEnhance();
          }}
          disabled={isEnhancing || !value.trim()}
          className="flex items-center gap-1"
        >
          {isEnhancing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-1" />
              Enhancing...
            </>
          ) : (
            <>
              Enhance
              <Sparkles className="h-4 w-4 text-yellow-500" />
            </>
          )}
        </Button>
      </div>
      <Textarea 
        id={id} 
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        rows={rows}
        className={`resize-none ${isModified ? 'border-amber-300 border-2' : ''}`}
        disabled={isEnhancing}
      />
      {description && (
        <p className="text-xs text-muted-foreground">
          {description}
        </p>
      )}
      
      {isModified && (
        <div className="flex items-center text-xs text-amber-500">
          <AlertCircle className="h-3 w-3 mr-1" />
          <span>Unsaved changes</span>
        </div>
      )}
    </div>
  );
};

export default EnhanceableTextSection;
