import { Request, Response } from 'express';
import { getUserById } from '../services/user.service';
import { unipileService } from '../services/unipile/index';

/**
 * Functional LinkedIn Handlers
 * Pure functions for handling LinkedIn-related HTTP requests
 */

/**
 * Get LinkedIn profile for a user
 * GET /api/linkedin/profile/:userId
 */
export const handleGetLinkedInProfile = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;
        
        console.log('🔍 GET /api/linkedin/profile/:userId - Request for user:', userId);

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        // Get user's LinkedIn account ID
        const user = await getUserById(userId);
        if (!user || !user.account_id) {
            res.status(404).json({
                success: false,
                error: 'User not found or LinkedIn account not connected'
            });
            return;
        }

        console.log('🔗 Fetching LinkedIn profile for account:', user.account_id);

        // Get LinkedIn profile from Unipile
        const profile = await unipileService.getLinkedInProfile(user.account_id);

        console.log('✅ LinkedIn profile retrieved successfully');
        res.json({
            success: true,
            data: profile
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetLinkedInProfile:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch LinkedIn profile',
            details: error.message
        });
    }
};

/**
 * Get LinkedIn posts for a user
 * GET /api/linkedin/posts/:userId
 */
export const handleGetLinkedInPosts = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;
        const { limit = 50 } = req.query;
        
        console.log('🔍 GET /api/linkedin/posts/:userId - Request for user:', userId, 'limit:', limit);

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        // Get user's LinkedIn account ID
        const user = await getUserById(userId);
        if (!user || !user.account_id) {
            res.status(404).json({
                success: false,
                error: 'User not found or LinkedIn account not connected'
            });
            return;
        }

        console.log('🔗 Fetching LinkedIn account data to extract user ID for posts');

        // Get LinkedIn account data to extract the actual LinkedIn user ID
        const linkedInAccount = await unipileService.getLinkedInProfile(user.account_id);

        // Extract the actual LinkedIn user ID from account connection_params
        const actualLinkedInUserId = linkedInAccount.connection_params?.im?.id;

        if (!actualLinkedInUserId) {
            res.status(400).json({
                success: false,
                error: 'Could not extract LinkedIn user ID from account data'
            });
            return;
        }

        console.log('🔗 LinkedIn account data for posts:', {
            account_id: user.account_id,
            linkedin_user_id: actualLinkedInUserId,
            account_name: linkedInAccount.name
        });

        // Get LinkedIn posts from Unipile
        const posts = await unipileService.getLinkedInPosts(
            user.account_id, 
            actualLinkedInUserId, 
            Number(limit)
        );

        console.log('✅ LinkedIn posts retrieved successfully, count:', posts.items?.length || 0);
        res.json({
            success: true,
            data: posts
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetLinkedInPosts:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch LinkedIn posts',
            details: error.message
        });
    }
};

/**
 * Get specific LinkedIn post details
 * GET /api/linkedin/post/:userId/:postId
 */
export const handleGetLinkedInPost = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId, postId } = req.params;
        
        console.log('🔍 GET /api/linkedin/post/:userId/:postId - Request for user:', userId, 'post:', postId);

        if (!userId || !postId) {
            res.status(400).json({
                success: false,
                error: 'User ID and post ID are required'
            });
            return;
        }

        // Get user's LinkedIn account ID
        const user = await getUserById(userId);
        if (!user || !user.account_id) {
            res.status(404).json({
                success: false,
                error: 'User not found or LinkedIn account not connected'
            });
            return;
        }

        console.log('🔗 Fetching LinkedIn post details for account:', user.account_id);

        // Get LinkedIn post from Unipile
        const post = await unipileService.getLinkedInPost(user.account_id, postId);

        console.log('✅ LinkedIn post details retrieved successfully');
        res.json({
            success: true,
            data: post
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetLinkedInPost:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch LinkedIn post details',
            details: error.message
        });
    }
};

/**
 * Get LinkedIn post comments
 * GET /api/linkedin/comments/:userId/:postId
 * Note: postId should be the social_id (URN format) for proper API calls
 */
export const handleGetLinkedInPostComments = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId, postId } = req.params;
        const { limit = 50 } = req.query;

        console.log('🔍 GET /api/linkedin/comments/:userId/:postId - Request for user:', userId, 'post:', postId);
        console.log('📝 Note: postId should be social_id (URN format) like "urn:li:activity:7324767572325810176"');

        if (!userId || !postId) {
            res.status(400).json({
                success: false,
                error: 'User ID and post ID are required'
            });
            return;
        }

        // Get user's LinkedIn account ID
        const user = await getUserById(userId);
        if (!user || !user.account_id) {
            res.status(404).json({
                success: false,
                error: 'User not found or LinkedIn account not connected'
            });
            return;
        }

        console.log('🔗 Fetching LinkedIn post comments for account:', user.account_id);
        console.log('🆔 Using post identifier:', postId);

        // Get post comments from Unipile (postId should be social_id in URN format)
        const comments = await unipileService.getPostComments(user.account_id, postId);

        console.log('✅ LinkedIn post comments retrieved successfully, count:', comments.items?.length || 0);
        res.json({
            success: true,
            data: comments,
            meta: {
                postId,
                accountId: user.account_id,
                totalComments: comments.items?.length || 0
            }
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetLinkedInPostComments:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch LinkedIn post comments',
            details: error.message
        });
    }
};

/**
 * Get LinkedIn post reactions
 * GET /api/linkedin/reactions/:userId/:postId
 * Note: postId should be the social_id (URN format) for proper API calls
 */
export const handleGetLinkedInPostReactions = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId, postId } = req.params;
        const { limit = 50 } = req.query;

        console.log('🔍 GET /api/linkedin/reactions/:userId/:postId - Request for user:', userId, 'post:', postId);
        console.log('📝 Note: postId should be social_id (URN format) like "urn:li:activity:7324767572325810176"');

        if (!userId || !postId) {
            res.status(400).json({
                success: false,
                error: 'User ID and post ID are required'
            });
            return;
        }

        // Get user's LinkedIn account ID
        const user = await getUserById(userId);
        if (!user || !user.account_id) {
            res.status(404).json({
                success: false,
                error: 'User not found or LinkedIn account not connected'
            });
            return;
        }

        console.log('🔗 Fetching LinkedIn post reactions for account:', user.account_id);
        console.log('🆔 Using post identifier:', postId);

        // Get post reactions from Unipile (postId should be social_id in URN format)
        const reactions = await unipileService.getPostReactions(user.account_id, postId);

        console.log('✅ LinkedIn post reactions retrieved successfully, count:', reactions.items?.length || 0);
        res.json({
            success: true,
            data: reactions,
            meta: {
                postId,
                accountId: user.account_id,
                totalReactions: reactions.items?.length || 0
            }
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetLinkedInPostReactions:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch LinkedIn post reactions',
            details: error.message
        });
    }
};
