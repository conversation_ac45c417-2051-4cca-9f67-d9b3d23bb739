
export interface LinkedInCredentials {
  email: string;
  password: string;
}

export interface LinkedIn2FADetails {
  type: '2fa_code' | 'sms_code' | 'app_code' | 'email_code';
  message: string;
  codeLength?: number;
}

export type LinkedInConnectionStatus = 'disconnected' | 'connecting' | 'connected' | '2fa_required' | 'error' | 'checking';

export interface LinkedInConnectionState {
  status: LinkedInConnectionStatus;
  credentials?: LinkedInCredentials;
  error?: string;
  twoFactorDetails?: LinkedIn2FADetails;
  accountId?: string;
}
