# Frontend Environment Configuration

This document explains how to set up and use environment variables in the frontend application.

## Environment Files

### `.env` (Main Configuration)
The main environment file that contains all configuration variables.

### `.env.example` (Template)
A template file showing all available environment variables with example values.

## Environment Variables

### API Configuration
- `VITE_API_BASE_URL` - Backend API base URL (default: `http://localhost:3000`)
- `VITE_CLIENT_URL` - Frontend client URL (default: `https://localhost:8080`)

### Phyllo Integration
- `VITE_PHYLLO_CLIENT_ID` - Phyllo client ID for social media integrations
- `VITE_PHYLLO_CLIENT_SECRET` - Phyllo client secret
- `VITE_PHYLLO_API_BASE_URL` - Phyllo API base URL (default: `https://api.sandbox.getphyllo.com/v1`)

### Unipile Integration
- `VITE_UNIPILE_API_URL` - Unipile API URL (default: `https://api.unipile.com`)
- `VITE_UNIPILE_ACCOUNT_URL` - Unipile account URL (default: `https://account.unipile.com`)
- `VITE_UNIPILE_ACCESS_TOKEN` - Unipile access token for LinkedIn integrations

### Environment
- `VITE_NODE_ENV` - Environment mode (`development`, `production`, `staging`)

## Setup Instructions

1. **Copy the example file:**
   ```bash
   cp .env.example .env
   ```

2. **Update the values:**
   - Replace placeholder values with your actual credentials
   - Update URLs for your deployment environment

3. **Development vs Production:**
   - Development: Use `localhost` URLs
   - Production: Use your actual domain URLs

## Usage in Code

### Import the configuration:
```typescript
import { getApiUrl, CLIENT_URL, UNIPILE_API_URL } from '@/config/env';
```

### Helper Functions:
- `getApiUrl(endpoint)` - Constructs full API URLs
- `getUnipileUrl(endpoint)` - Constructs Unipile API URLs
- `getUnipileAccountUrl(path)` - Constructs Unipile account URLs

### Example Usage:
```typescript
// API calls
const response = await fetch(getApiUrl('/auth/connect'), {
  method: 'POST',
  // ...
});

// Unipile CAPTCHA URL
const captchaUrl = getUnipileAccountUrl(`/${data}`);
```

## Environment Validation

The configuration includes automatic validation:
- **Development**: Warns about missing variables, uses fallbacks
- **Production**: Should fail fast if required variables are missing

## Security Notes

1. **Never commit `.env` files** - They contain sensitive credentials
2. **Use `.env.example`** for documentation and onboarding
3. **Rotate credentials regularly** - Especially API tokens
4. **Use different credentials** for different environments

## Troubleshooting

### Common Issues:

1. **404 Errors on API calls:**
   - Check `VITE_API_BASE_URL` is correct
   - Ensure backend server is running

2. **CORS Errors:**
   - Verify `VITE_CLIENT_URL` matches your frontend URL
   - Check backend CORS configuration

3. **LinkedIn CAPTCHA Issues:**
   - Verify `VITE_UNIPILE_ACCOUNT_URL` is correct
   - Check `VITE_UNIPILE_ACCESS_TOKEN` is valid

4. **Environment Variables Not Loading:**
   - Ensure variables start with `VITE_`
   - Restart development server after changes
   - Check for typos in variable names

### Debug Commands:
```bash
# Check if variables are loaded
console.log(import.meta.env);

# Check specific configuration
import { env } from '@/config/env';
console.log(env);
```

## Production Deployment

For production deployment:

1. **Update URLs:**
   ```env
   VITE_API_BASE_URL=https://api.yourdomain.com
   VITE_CLIENT_URL=https://yourdomain.com
   ```

2. **Set environment:**
   ```env
   VITE_NODE_ENV=production
   ```

3. **Secure credentials:**
   - Use environment-specific secrets management
   - Never expose credentials in client-side code
   - Use build-time environment injection

## Files Updated

The following files have been updated to use the new environment configuration:

- `client/src/config/env.ts` - Main configuration file
- `client/src/pages/SignUp.tsx` - Signup page API calls
- `client/src/components/auth/CheckpointModal.tsx` - Checkpoint verification
- `client/src/components/auth/LinkedInCaptcha.tsx` - CAPTCHA handling
- `client/src/components/auth/CaptchaTestComponent.tsx` - Test component
- `client/src/hooks/useAuthState.ts` - Authentication state management
- `client/src/hooks/useLinkedInVerification.ts` - LinkedIn verification
- `client/src/services/analyticsApi.ts` - Analytics API service
- `client/src/integrations/unipile.ts` - Unipile integration
- `client/src/integrations/phylloSDK.ts` - Phyllo SDK integration
