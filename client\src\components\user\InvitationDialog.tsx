
import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, DialogContent, DialogDescription, 
  DialogFooter, DialogHeader, DialogTitle, DialogTrigger 
} from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { AlertCircle, FileSpreadsheet, Loader2, Plus, Upload, X } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Papa from 'papaparse';

// Define types
interface Invitation {
  email: string;
  role?: string;
  department?: string;
  jobTitle?: string;
}

interface InvitationResult {
  email: string;
  success: boolean;
  error?: string;
  invitationId?: string;
}

interface CSVRow {
  email: string;
  role?: string;
  department?: string;
  'job title'?: string;
  [key: string]: string | undefined;
}

const ROLES = ['admin', 'user'];
const DEPARTMENTS = ['Marketing', 'Sales', 'Product', 'Engineering', 'HR', 'Finance', 'Other'];

const InvitationDialog = () => {
  const { toast } = useToast();
  const { profile } = useAuth();
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('single');
  
  // Single invitation form state
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('user');
  const [department, setDepartment] = useState('');
  const [jobTitle, setJobTitle] = useState('');
  
  // CSV upload state
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<CSVRow[]>([]);
  const [csvErrors, setCsvErrors] = useState<{[key: number]: string[]}>({});
  const [isUploading, setIsUploading] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  
  // Status state
  const [isSending, setIsSending] = useState(false);
  const [results, setResults] = useState<InvitationResult[]>([]);
  const [showResults, setShowResults] = useState(false);

  // Reset form
  const resetForm = () => {
    setEmail('');
    setRole('user');
    setDepartment('');
    setJobTitle('');
    setCsvFile(null);
    setCsvData([]);
    setCsvErrors({});
    setIsUploading(false);
    setPreviewMode(false);
    setResults([]);
    setShowResults(false);
  };

  // Close dialog handler
  const handleCloseDialog = () => {
    setOpen(false);
    setTimeout(resetForm, 300); // Reset after animation
  };

  // Single invitation handler
  const handleSingleInvite = async () => {
    if (!email) {
      toast({
        title: "Email required",
        description: "Please enter an email address.",
        variant: "destructive"
      });
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast({
        title: "Invalid email",
        description: "Please enter a valid email address.",
        variant: "destructive"
      });
      return;
    }
    
    if (!profile?.id) {
      toast({
        title: "Authentication error",
        description: "You must be logged in to invite users.",
        variant: "destructive"
      });
      return;
    }

    // Get current company
    try {
      setIsSending(true);
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .select('id, name')
        .single();
      
      if (companyError || !companyData) {
        console.error('Error fetching company:', companyError);
        toast({
          title: "Company not found",
          description: "Could not find your company information.",
          variant: "destructive"
        });
        setIsSending(false);
        return;
      }

      // Send invitation
      await processInvitations([{
        email,
        role,
        department: department || undefined,
        jobTitle: jobTitle || undefined
      }], companyData.id, companyData.name);
      
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast({
        title: "Error",
        description: `Failed to send invitation: ${error.message}`,
        variant: "destructive"
      });
    } finally {
      setIsSending(false);
    }
  };

  // CSV file handler
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setCsvFile(selectedFile);
      setIsUploading(true);
      
      Papa.parse(selectedFile, {
        header: true,
        skipEmptyLines: true,
        complete: function(results) {
          setIsUploading(false);
          
          // Validate CSV structure
          const requiredColumns = ['email'];
          const optionalColumns = ['role', 'department', 'job title'];
          const headers = results.meta.fields || [];
          
          if (!headers.includes('email')) {
            setCsvErrors({
              0: ["CSV must include 'email' column"]
            });
            return;
          }
          
          // Validate data in each row
          const validData: CSVRow[] = [];
          const errors: {[key: number]: string[]} = {};
          
          results.data.forEach((row: any, index: number) => {
            const rowErrors: string[] = [];
            
            // Check for empty email
            if (!row.email || row.email.trim() === '') {
              rowErrors.push("Email is required");
            } else {
              // Check email format
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
              if (!emailRegex.test(row.email)) {
                rowErrors.push("Invalid email format");
              }
            }
            
            // Check role if present
            if (row.role && !ROLES.includes(row.role.toLowerCase())) {
              rowErrors.push(`Invalid role: ${row.role}. Must be one of: ${ROLES.join(', ')}`);
            }
            
            if (rowErrors.length > 0) {
              errors[index] = rowErrors;
            }
            
            validData.push({
              email: row.email,
              role: row.role?.toLowerCase(),
              department: row.department,
              'job title': row['job title']
            });
          });
          
          setCsvData(validData);
          setCsvErrors(errors);
          setPreviewMode(true);
        },
        error: function(error) {
          setIsUploading(false);
          toast({
            title: "Error parsing CSV",
            description: error.message,
            variant: "destructive"
          });
        }
      });
    }
  };

  const handleCSVInvite = async () => {
    if (csvData.length === 0 || Object.keys(csvErrors).length > 0) {
      toast({
        title: "Invalid CSV data",
        description: "Please fix the errors in your CSV file before sending invitations.",
        variant: "destructive"
      });
      return;
    }
    
    if (!profile?.id) {
      toast({
        title: "Authentication error",
        description: "You must be logged in to invite users.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSending(true);
      
      // Get current company
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .select('id, name')
        .single();
      
      if (companyError || !companyData) {
        console.error('Error fetching company:', companyError);
        toast({
          title: "Company not found",
          description: "Could not find your company information.",
          variant: "destructive"
        });
        setIsSending(false);
        return;
      }

      // Process invitations
      const invitations: Invitation[] = csvData.map(row => ({
        email: row.email,
        role: row.role || 'user',
        department: row.department,
        jobTitle: row['job title']
      }));
      
      await processInvitations(invitations, companyData.id, companyData.name);
      
    } catch (error) {
      console.error('Error sending bulk invitations:', error);
      toast({
        title: "Error",
        description: `Failed to send invitations: ${error.message}`,
        variant: "destructive"
      });
    } finally {
      setIsSending(false);
    }
  };

  // Process invitations - common function for both methods
  const processInvitations = async (invitations: Invitation[], companyId: string, companyName: string) => {
    try {
      const { data, error } = await supabase.functions.invoke('send-invitation', {
        body: { 
          invitations, 
          companyId, 
          companyName, 
          invitedBy: profile?.id 
        }
      });
      
      if (error) {
        console.error('Edge function error:', error);
        toast({
          title: "Error",
          description: `Failed to process invitations: ${error.message}`,
          variant: "destructive"
        });
        return;
      }
      
      // Process results
      setResults(data.results);
      setShowResults(true);
      
      // Show toast with summary
      const successful = data.results.filter((r: InvitationResult) => r.success).length;
      const failed = data.results.length - successful;
      
      if (successful > 0 && failed === 0) {
        toast({
          title: "Success",
          description: `Successfully sent ${successful} invitation${successful !== 1 ? 's' : ''}.`,
        });
      } else if (successful > 0 && failed > 0) {
        toast({
          title: "Partial success",
          description: `Sent ${successful} invitation${successful !== 1 ? 's' : ''}, but ${failed} failed. See details for more information.`,
          variant: "default"
        });
      } else {
        toast({
          title: "Failed",
          description: `Failed to send ${failed} invitation${failed !== 1 ? 's' : ''}.`,
          variant: "destructive"
        });
      }
      
    } catch (error) {
      console.error('Exception processing invitations:', error);
      toast({
        title: "Error",
        description: `Exception: ${error.message}`,
        variant: "destructive"
      });
    }
  };

  // Download CSV template
  const downloadTemplate = () => {
    const headers = ["email", "role", "department", "job title"];
    const csvContent = Papa.unparse({
      fields: headers,
      data: [
        ["<EMAIL>", "user", "Marketing", "Content Specialist"],
        ["<EMAIL>", "admin", "Executive", "CEO"]
      ]
    });
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'invitation_template.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Render results view
  const renderResults = () => {
    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;
    
    return (
      <div className="space-y-4">
        <div className="text-center mb-4">
          <h3 className="text-lg font-medium">Invitation Results</h3>
          <p className="text-muted-foreground">
            {successful} successful, {failed} failed
          </p>
        </div>
        
        <div className="max-h-[300px] overflow-y-auto">
          {results.map((result, index) => (
            <div 
              key={index} 
              className={`p-3 mb-2 rounded-md ${result.success ? 'bg-green-50' : 'bg-red-50'}`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{result.email}</p>
                  {!result.success && (
                    <p className="text-sm text-red-600">{result.error}</p>
                  )}
                </div>
                {result.success ? (
                  <span className="text-green-600 text-sm">✓ Sent</span>
                ) : (
                  <span className="text-red-600 text-sm">✗ Failed</span>
                )}
              </div>
            </div>
          ))}
        </div>
        
        <Button 
          className="w-full mt-4" 
          variant="default" 
          onClick={() => {
            resetForm();
            setActiveTab('single');
          }}
        >
          Send More Invitations
        </Button>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-enterprise-blue hover:bg-enterprise-blue/90">
          <Plus className="mr-2 h-4 w-4" /> Add User
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Invite Team Members</DialogTitle>
          <DialogDescription>
            Send invitations to your colleagues to join your team.
          </DialogDescription>
        </DialogHeader>
        
        {showResults ? renderResults() : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="single">Single Invite</TabsTrigger>
              <TabsTrigger value="bulk">Bulk Import</TabsTrigger>
            </TabsList>
            
            <TabsContent value="single" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isSending}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select value={role} onValueChange={setRole} disabled={isSending}>
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="department">Department (Optional)</Label>
                <Select value={department} onValueChange={setDepartment} disabled={isSending}>
                  <SelectTrigger id="department">
                    <SelectValue placeholder="Select a department" />
                  </SelectTrigger>
                  <SelectContent>
                    {DEPARTMENTS.map(dept => (
                      <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="jobTitle">Job Title (Optional)</Label>
                <Input
                  id="jobTitle"
                  placeholder="e.g. Marketing Manager"
                  value={jobTitle}
                  onChange={(e) => setJobTitle(e.target.value)}
                  disabled={isSending}
                />
              </div>
              
              <DialogFooter className="pt-4">
                <Button
                  variant="outline"
                  onClick={handleCloseDialog}
                  disabled={isSending}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSingleInvite} 
                  disabled={!email || isSending}
                >
                  {isSending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  {isSending ? "Sending..." : "Send Invitation"}
                </Button>
              </DialogFooter>
            </TabsContent>
            
            <TabsContent value="bulk" className="space-y-4 mt-4">
              {!previewMode ? (
                <div className="space-y-4">
                  <div 
                    className="border-2 border-dashed rounded-md p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
                    onDragOver={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onDrop={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const files = e.dataTransfer.files;
                      if (files && files[0]) {
                        const input = document.getElementById('csv-input') as HTMLInputElement;
                        if (input) {
                          const dataTransfer = new DataTransfer();
                          dataTransfer.items.add(files[0]);
                          input.files = dataTransfer.files;
                          handleFileChange({ target: { files: dataTransfer.files } } as any);
                        }
                      }
                    }}
                  >
                    <div className="flex flex-col items-center gap-2">
                      <FileSpreadsheet className="h-10 w-10 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">
                        Drag and drop a CSV file here, or click to select
                      </p>
                      <Input
                        id="csv-input"
                        type="file"
                        accept=".csv"
                        className="hidden"
                        onChange={handleFileChange}
                        disabled={isUploading}
                      />
                      <Button
                        variant="outline"
                        onClick={() => {
                          document.getElementById('csv-input')?.click();
                        }}
                        disabled={isUploading}
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        {isUploading ? "Uploading..." : "Select CSV File"}
                      </Button>
                    </div>
                  </div>
                  
                  <div className="text-sm text-muted-foreground">
                    <p>CSV should include columns: email (required), role, department, job title (optional)</p>
                    <Button 
                      variant="link" 
                      className="p-0 h-auto text-sm"
                      onClick={downloadTemplate}
                    >
                      Download template CSV
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">Preview ({csvData.length} users)</h3>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => {
                        setCsvFile(null);
                        setCsvData([]);
                        setCsvErrors({});
                        setPreviewMode(false);
                      }}
                    >
                      <X className="h-4 w-4 mr-2" /> Clear
                    </Button>
                  </div>
                  
                  {Object.keys(csvErrors).length > 0 && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        There are errors in your CSV file. Please fix them before proceeding.
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  <div className="max-h-[300px] overflow-y-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b bg-muted">
                          <th className="text-left p-2 text-sm font-medium">Email</th>
                          <th className="text-left p-2 text-sm font-medium">Role</th>
                          <th className="text-left p-2 text-sm font-medium">Department</th>
                        </tr>
                      </thead>
                      <tbody>
                        {csvData.map((row, index) => (
                          <tr 
                            key={index} 
                            className={`border-b ${csvErrors[index] ? 'bg-red-50' : ''}`}
                          >
                            <td className="p-2 text-sm">{row.email || '-'}</td>
                            <td className="p-2 text-sm">{row.role || 'user'}</td>
                            <td className="p-2 text-sm">{row.department || '-'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  
                  {Object.keys(csvErrors).length > 0 && (
                    <div className="mt-2 space-y-2">
                      <h4 className="text-sm font-medium text-red-600">Errors:</h4>
                      {Object.entries(csvErrors).map(([rowIndex, errorMessages]) => (
                        <div key={rowIndex} className="bg-red-50 p-2 rounded text-sm">
                          <p className="font-medium">Row {parseInt(rowIndex) + 1}:</p>
                          <ul className="list-disc list-inside ml-2">
                            {errorMessages.map((error, idx) => (
                              <li key={idx}>{error}</li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
              
              <DialogFooter className="pt-4">
                <Button 
                  variant="outline" 
                  onClick={handleCloseDialog} 
                  disabled={isSending}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleCSVInvite} 
                  disabled={!previewMode || Object.keys(csvErrors).length > 0 || isSending || csvData.length === 0}
                >
                  {isSending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  {isSending ? "Sending..." : `Send ${csvData.length} Invitation${csvData.length !== 1 ? 's' : ''}`}
                </Button>
              </DialogFooter>
            </TabsContent>
          </Tabs>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default InvitationDialog;
