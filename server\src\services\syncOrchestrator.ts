// Phase 2: Sync Orchestrator
// Coordinates LinkedIn post fetching and storage using existing Unipile services

import { UnipileService } from './unipile';
import { storeLinkedInPosts, getUserPosts, LinkedInPostData } from './postStorageService';

export interface SyncOptions {
    userId: string;
    accountId: string;
    userIdentifier: string;
    department?: string;
    limit?: number;
    fullSync?: boolean; // true = fetch all posts, false = incremental
}

export interface SyncResult {
    success: boolean;
    operation: {
        type: 'full_sync' | 'incremental_sync';
        startedAt: string;
        completedAt: string;
        durationMs: number;
    };
    posts: {
        fetched: number;
        stored: number;
        updated: number;
        failed: number;
        skipped: number;
    };
    apiCalls: {
        made: number;
        rateLimitHit: boolean;
    };
    errors: string[];
    lastSyncedPostDate?: string;
}

/**
 * Main sync orchestrator class
 */
export class PostSyncOrchestrator {
    private unipileService: UnipileService;

    constructor() {
        this.unipileService = new UnipileService();
    }

    /**
     * Perform full or incremental sync of LinkedIn posts
     */
    async syncUserPosts(options: SyncOptions): Promise<SyncResult> {
        const startTime = Date.now();
        const startedAt = new Date().toISOString();

        console.log('🚀 Starting post sync:', {
            userId: options.userId,
            accountId: options.accountId,
            type: options.fullSync ? 'full_sync' : 'incremental_sync',
            limit: options.limit || 50
        });

        const result: SyncResult = {
            success: false,
            operation: {
                type: options.fullSync ? 'full_sync' : 'incremental_sync',
                startedAt,
                completedAt: '',
                durationMs: 0
            },
            posts: {
                fetched: 0,
                stored: 0,
                updated: 0,
                failed: 0,
                skipped: 0
            },
            apiCalls: {
                made: 0,
                rateLimitHit: false
            },
            errors: []
        };

        try {
            // Step 1: Fetch posts from LinkedIn via Unipile
            console.log('📥 Step 1: Fetching posts from LinkedIn...');
            const postsResponse = await this.unipileService.getLinkedInPosts(
                options.accountId,
                options.userIdentifier,
                options.limit 
            );

            result.apiCalls.made++;
            
            if (!postsResponse || !postsResponse.items) {
                throw new Error('No posts data received from LinkedIn');
            }

            const posts = postsResponse.items;
            result.posts.fetched = posts.length;
            console.log(`📊 Fetched ${posts.length} posts from LinkedIn`);

            if (posts.length === 0) {
                console.log('ℹ️ No posts found to sync');
                result.success = true;
                return this.completeSync(result, startTime);
            }

            // Step 2: Enrich posts with analytics (reactions, comments)
            console.log('📈 Step 2: Enriching posts with analytics...');
            const enrichedPosts = await this.enrichPostsWithAnalytics(
                options.accountId,
                posts,
                result
            );

            // Step 3: Store posts in Supabase
            console.log('💾 Step 3: Storing posts in Supabase...');
            const storageResult = await storeLinkedInPosts(
                options.userId,
                enrichedPosts,
                options.department || 'other'
            );

            // Update results
            result.posts.stored = storageResult.results.stored;
            result.posts.updated = storageResult.results.updated;
            result.posts.failed = storageResult.results.failed;
            result.errors.push(...storageResult.results.errors);

            // Step 4: Calculate final results
            result.success = storageResult.success;

            if (enrichedPosts.length > 0) {
                // Find the most recent post date
                const sortedPosts = enrichedPosts
                    .filter(p => p.published_at || p.created_at)
                    .sort((a, b) => {
                        const dateA = new Date(a.published_at || a.created_at || 0);
                        const dateB = new Date(b.published_at || b.created_at || 0);
                        return dateB.getTime() - dateA.getTime();
                    });

                if (sortedPosts.length > 0) {
                    result.lastSyncedPostDate = sortedPosts[0].published_at || sortedPosts[0].created_at;
                }
            }

            console.log('✅ Post sync completed successfully:', {
                fetched: result.posts.fetched,
                stored: result.posts.stored,
                updated: result.posts.updated,
                failed: result.posts.failed
            });

        } catch (error: any) {
            console.error('❌ Error during post sync:', error);
            result.success = false;
            result.errors.push(error.message);

            // Check if it's a rate limit error
            if (error.message.includes('rate limit') || error.message.includes('429')) {
                result.apiCalls.rateLimitHit = true;
            }
        }

        return this.completeSync(result, startTime);
    }

    /**
     * Enrich posts with detailed analytics (reactions, comments)
     */
    private async enrichPostsWithAnalytics(
        accountId: string,
        posts: any[],
        result: SyncResult
    ): Promise<LinkedInPostData[]> {
        const enrichedPosts: LinkedInPostData[] = [];

        for (const post of posts) {
            try {
                console.log(`📊 Enriching post: ${post.id}`);

                // Get detailed post data with analytics
                const enrichedPost = await this.unipileService.getComprehensivePostData(
                    accountId,
                    post.id
                );

                result.apiCalls.made += 3; // Comprehensive call makes 3 API requests

                // Transform to our format
                const transformedPost: LinkedInPostData = {
                    id: post.id,
                    social_id: post.social_id,
                    text: post.text || enrichedPost.postDetails?.text,
                    content: post.content || enrichedPost.postDetails?.content,
                    type: post.type || enrichedPost.postDetails?.type,
                    created_at: post.parsed_datetime,
                    published_at: post.published_at || enrichedPost.postDetails?.published_at,
                    author: post.author || enrichedPost.postDetails?.author,
                    share_url: post.share_url || enrichedPost.postDetails?.url,
                    metrics: {
                        likes: enrichedPost.reactions?.items?.length || 0,
                        comments: enrichedPost.comments?.items?.length || 0,
                        shares: post.repost_counter || 0,
                        impressions: post.impressions_counter || 0
                    },
                    reactions: enrichedPost.reactions?.items || [],
                    comments: enrichedPost.comments?.items || []
                };

                enrichedPosts.push(transformedPost);

                // Add small delay to avoid overwhelming the API
                await new Promise(resolve => setTimeout(resolve, 100));

            } catch (error: any) {
                console.error(`❌ Error enriching post ${post.id}:`, error);
                result.errors.push(`Failed to enrich post ${post.id}: ${error.message}`);

                // Add basic post without enrichment
                enrichedPosts.push({
                    id: post.id,
                    social_id: post.social_id,
                    text: post.text,
                    content: post.content,
                    type: post.type,
                    created_at: post.created_at,
                    published_at: post.published_at,
                    author: post.author,
                    share_url: post.share_url,
                    metrics: {
                        likes: 0,
                        comments: 0,
                        shares: 0,
                        impressions: 0
                    }
                });

                // Check for rate limit
                if (error.message.includes('rate limit') || error.message.includes('429')) {
                    result.apiCalls.rateLimitHit = true;
                    console.log('⚠️ Rate limit detected, continuing with basic post data');
                }
            }
        }

        return enrichedPosts;
    }

    /**
     * Get sync status for a user
     */
    async getSyncStatus(userId: string): Promise<{
        success: boolean;
        status: {
            totalPosts: number;
            lastSyncAt?: string;
            newPosts: number;
            updatedPosts: number;
        };
        error?: string;
    }> {
        try {
            const postsResult = await getUserPosts(userId, 1000); // Get all posts for status

            if (!postsResult.success) {
                throw new Error(postsResult.error || 'Failed to fetch posts');
            }

            const posts = postsResult.posts || [];
            const newPosts = posts.filter(p => p.is_newly_added).length;
            const updatedPosts = posts.filter(p => p.is_updated).length;
            
            // Find most recent sync
            const lastSyncAt = posts.length > 0 
                ? posts.sort((a, b) => new Date(b.last_synced_at).getTime() - new Date(a.last_synced_at).getTime())[0].last_synced_at
                : undefined;

            return {
                success: true,
                status: {
                    totalPosts: posts.length,
                    lastSyncAt,
                    newPosts,
                    updatedPosts
                }
            };
        } catch (error: any) {
            console.error('❌ Error getting sync status:', error);
            return {
                success: false,
                status: {
                    totalPosts: 0,
                    newPosts: 0,
                    updatedPosts: 0
                },
                error: error.message
            };
        }
    }

    /**
     * Complete sync operation and calculate final metrics
     */
    private completeSync(result: SyncResult, startTime: number): SyncResult {
        const completedAt = new Date().toISOString();
        const durationMs = Date.now() - startTime;

        result.operation.completedAt = completedAt;
        result.operation.durationMs = durationMs;

        console.log(`🏁 Sync operation completed in ${durationMs}ms:`, {
            success: result.success,
            posts: result.posts,
            apiCalls: result.apiCalls,
            errors: result.errors.length
        });

        return result;
    }
}

// Export singleton instance
export const postSyncOrchestrator = new PostSyncOrchestrator();
