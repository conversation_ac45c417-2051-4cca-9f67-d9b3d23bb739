
import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Loader2 } from 'lucide-react';

export const AdminRoute = () => {
  const { user, profile, isLoading, isAdmin } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-enterprise-blue" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  // If not logged in, redirect to login
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If not an admin, redirect to user dashboard
  if (isAdmin()) {
    return <Navigate to="/user-dashboard" replace />;
  }

  // User is admin, show the admin route
  return <Outlet />;
};
