
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const data = [
  { date: '04/10', impressions: 32000, engagement: 1200 },
  { date: '04/11', impressions: 28000, engagement: 1400 },
  { date: '04/12', impressions: 35000, engagement: 1800 },
  { date: '04/13', impressions: 42000, engagement: 2100 },
  { date: '04/14', impressions: 38000, engagement: 1900 },
  { date: '04/15', impressions: 45000, engagement: 2300 },
  { date: '04/16', impressions: 50000, engagement: 2600 },
];

export const TeamEngagementChart = () => {
  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Team Engagement Trends</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="impressions"
                stroke="#2563eb"
                strokeWidth={2}
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="engagement"
                stroke="#0d9488"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
