
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Loader2 } from 'lucide-react';
import { Invitation } from '@/types/settings';

interface InvitationsTableProps {
  invitations: Invitation[];
  isLoading: boolean;
  resendingId: string | null;
  resendInvitation: (invitationId: string) => Promise<void>;
}

const InvitationsTable: React.FC<InvitationsTableProps> = ({ 
  invitations, 
  isLoading, 
  resendingId, 
  resendInvitation 
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="overflow-x-auto">
      {isLoading ? (
        <div className="flex justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-enterprise-blue" />
        </div>
      ) : invitations.length > 0 ? (
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left py-3 px-4 font-medium">Email</th>
              <th className="text-left py-3 px-4 font-medium">Role</th>
              <th className="text-left py-3 px-4 font-medium">Department</th>
              <th className="text-left py-3 px-4 font-medium">Status</th>
              <th className="text-left py-3 px-4 font-medium">Sent</th>
              <th className="text-right py-3 px-4 font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {invitations.map((invitation) => (
              <tr key={invitation.id} className="border-b hover:bg-muted/50">
                <td className="py-3 px-4">{invitation.email}</td>
                <td className="py-3 px-4">
                  <Badge variant={invitation.role === "admin" ? "default" : "secondary"}>
                    {invitation.role}
                  </Badge>
                </td>
                <td className="py-3 px-4">{invitation.department}</td>
                <td className="py-3 px-4">
                  <Badge 
                    variant="outline" 
                    className={
                      invitation.status === "pending" 
                        ? "border-amber-500 text-amber-600" 
                        : invitation.status === "accepted" 
                        ? "border-green-500 text-green-600" 
                        : "border-red-500 text-red-600"
                    }
                  >
                    {invitation.status === "pending" ? "Pending" : 
                     invitation.status === "accepted" ? "Accepted" : "Expired"}
                  </Badge>
                </td>
                <td className="py-3 px-4 text-sm text-muted-foreground">
                  {formatDate(invitation.created_at)}
                </td>
                <td className="py-3 px-4 text-right">
                  {invitation.status === "pending" ? (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => resendInvitation(invitation.id)}
                      disabled={resendingId === invitation.id}
                    >
                      {resendingId === invitation.id ? (
                        <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                      ) : null}
                      {resendingId === invitation.id ? "Sending..." : "Resend"}
                    </Button>
                  ) : invitation.status === "expired" ? (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => resendInvitation(invitation.id)}
                      disabled={resendingId === invitation.id}
                    >
                      {resendingId === invitation.id ? (
                        <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                      ) : null}
                      {resendingId === invitation.id ? "Sending..." : "Renew"}
                    </Button>
                  ) : (
                    <Button 
                      variant="ghost" 
                      size="sm"
                      disabled
                    >
                      <CheckCircle className="h-3 w-3 mr-2" />
                      Joined
                    </Button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          No pending invitations found.
        </div>
      )}
    </div>
  );
};

export default InvitationsTable;
