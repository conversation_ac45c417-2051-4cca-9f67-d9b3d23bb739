
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import EmojiRow from './EmojiRow';

const JiveHero = () => {
  return (
    <section className="flex-grow flex flex-col items-center px-4 py-10 text-center max-w-4xl mx-auto sm:py-[40px]">
      <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4">
        Empower 
        <br />
        Every Voice.
        <br />
        Amplify Your Brand.
      </h1>
      
      <p className="text-lg text-gray-600 mb-6 max-w-xl">Turn your employees into your most powerful marketing channel by automating posts, measuring impact, and scaling reach.</p>
      
      <Link to="/signup" className="mb-8">
        <Button className="rounded-full bg-black text-white hover:bg-black/90 py-6 px-8 text-lg">
          Get Started for free
        </Button>
      </Link> 
      
      {/* Emoji row below the button */}
      <EmojiRow />
    </section>
  );
};

export default JiveHero;
