
// Types for user management and invitations
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  status: string;
}

export interface Invitation {
  id: string;
  email: string;
  role: string;
  department: string;
  status: string;
  created_at: string;
}

// Type for brand colors from JSON
export interface BrandColors {
  adminApprovalsEnabled?: boolean;
  [key: string]: any;
}

// Type for brand settings (for ContentContext)
export interface BrandSettings {
  adminApprovalsEnabled?: boolean;
  [key: string]: any;
}
