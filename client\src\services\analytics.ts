/**
 * User Analytics Tracking Service
 * Tracks user actions throughout the signup and onboarding flow
 */

interface UserAction {
  action: string;
  data?: Record<string, any>;
  timestamp?: string;
  userId?: string;
  sessionId?: string;
}

interface OnboardingStep {
  step: string;
  completed: boolean;
  timestamp: string;
  data?: Record<string, any>;
}

class AnalyticsService {
  private sessionId: string;
  private userId?: string;
  private onboardingSteps: OnboardingStep[] = [];

  constructor() {
    this.sessionId = this.generateSessionId();
    // No localStorage loading needed
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Onboarding progress is now tracked in database, not localStorage

  setUserId(userId: string): void {
    this.userId = userId;
  }

  async trackUserAction(action: string, data?: Record<string, any>): Promise<void> {
    const actionData: UserAction = {
      action,
      data,
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId,
    };

    try {
      // Log to console for development
      console.log('📊 User Action:', actionData);

      // No local storage - send directly to backend

      // Send to backend analytics
      await this.sendToBackend(actionData);

      // Update onboarding progress if relevant
      if (action.includes('onboarding') || action.includes('signup')) {
        this.updateOnboardingProgress(action, data);
      }

    } catch (error) {
      console.warn('Failed to track user action:', error);
    }
  }

  // User actions are now sent directly to backend, not stored locally

  private async sendToBackend(action: UserAction): Promise<void> {
    try {
      const { getApiUrl } = await import('@/config/env');
      const response = await fetch(getApiUrl('/analytics/track'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(action),
      });

      if (!response.ok) {
        throw new Error(`Analytics API error: ${response.status}`);
      }
    } catch (error) {
      // Fail silently for analytics - don't disrupt user experience
      console.warn('Analytics backend error:', error);
    }
  }

  private updateOnboardingProgress(action: string, data?: Record<string, any>): void {
    const stepMap: Record<string, string> = {
      'signup_attempt': 'account_creation_started',
      'signup_success': 'account_created',
      'linkedin_onboarding_started': 'linkedin_setup_started',
      'linkedin_verification_success': 'linkedin_connected',
      'onboarding_completed': 'onboarding_finished',
    };

    const stepName = stepMap[action];
    if (!stepName) return;

    // Update or add step
    const existingIndex = this.onboardingSteps.findIndex(step => step.step === stepName);
    const stepData: OnboardingStep = {
      step: stepName,
      completed: true,
      timestamp: new Date().toISOString(),
      data,
    };

    if (existingIndex >= 0) {
      this.onboardingSteps[existingIndex] = stepData;
    } else {
      this.onboardingSteps.push(stepData);
    }

    // No localStorage saving needed
  }

  getOnboardingProgress(): OnboardingStep[] {
    return this.onboardingSteps;
  }

  getCompletionPercentage(): number {
    const totalSteps = 4; // account_created, linkedin_setup_started, linkedin_connected, onboarding_finished
    const completedSteps = this.onboardingSteps.filter(step => step.completed).length;
    return Math.round((completedSteps / totalSteps) * 100);
  }

  isStepCompleted(stepName: string): boolean {
    return this.onboardingSteps.some(step => step.step === stepName && step.completed);
  }

  // Predefined tracking methods for common actions
  async trackSignupAttempt(email: string, firstName: string, lastName: string): Promise<void> {
    return this.trackUserAction('signup_attempt', { email, firstName, lastName });
  }

  async trackSignupSuccess(email: string): Promise<void> {
    return this.trackUserAction('signup_success', { email });
  }

  async trackSignupFailed(email: string, error: string): Promise<void> {
    return this.trackUserAction('signup_failed', { email, error });
  }

  async trackLinkedInOnboardingStarted(): Promise<void> {
    return this.trackUserAction('linkedin_onboarding_started');
  }

  async trackLinkedInVerificationAttempt(email: string): Promise<void> {
    return this.trackUserAction('linkedin_verification_attempt', { email });
  }

  async trackLinkedInVerificationSuccess(email: string, accountId: string): Promise<void> {
    return this.trackUserAction('linkedin_verification_success', { email, accountId });
  }

  async trackLinkedInSkipped(): Promise<void> {
    return this.trackUserAction('linkedin_verification_skipped');
  }

  async trackOnboardingCompleted(): Promise<void> {
    return this.trackUserAction('onboarding_completed');
  }

  async trackFeatureAttempt(feature: string, requiresLinkedIn: boolean): Promise<void> {
    return this.trackUserAction('feature_attempt', { feature, requiresLinkedIn });
  }

  async trackLinkedInPromptShown(context: string): Promise<void> {
    return this.trackUserAction('linkedin_prompt_shown', { context });
  }

  // Get analytics summary for debugging (no localStorage)
  getAnalyticsSummary(): {
    sessionId: string;
    userId?: string;
    onboardingProgress: OnboardingStep[];
    completionPercentage: number;
  } {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      onboardingProgress: this.onboardingSteps,
      completionPercentage: this.getCompletionPercentage(),
    };
  }

  // Clear analytics data (no localStorage to clear)
  clearAnalyticsData(): void {
    this.onboardingSteps = [];
    this.sessionId = this.generateSessionId();
    this.userId = undefined;
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();

// Export convenience function
export const trackUserAction = (action: string, data?: Record<string, any>) => {
  return analyticsService.trackUserAction(action, data);
};

export default analyticsService;
