import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

type EnhancementType = 'tone' | 'description' | 'audience' | 'action' | string;

interface UseAIEnhancementProps {
  onToneChange: (value: string) => void;
  onDescriptionChange: (value: string) => void;
  onAudienceChange: (value: string) => void;
  onActionChange: (value: string) => void;
}

export const useAIEnhancement = ({ 
  onToneChange,
  onDescriptionChange, 
  onAudienceChange,
  onActionChange
}: UseAIEnhancementProps) => {
  const { toast } = useToast();
  
  // Loading states for AI enhancement
  const [enhancingTone, setEnhancingTone] = useState(false);
  const [enhancingDesc, setEnhancingDesc] = useState(false);
  const [enhancingAudience, setEnhancingAudience] = useState(false);
  const [enhancingAction, setEnhancingAction] = useState(false);
  
  // AI enhancement function
  const enhanceText = async (text: string, enhancementType: EnhancementType) => {
    if (!text.trim()) {
      toast({
        title: "Empty content",
        description: "Please enter some text to enhance.",
        variant: "destructive"
      });
      return;
    }
    
    // Set loading state based on enhancement type
    switch (enhancementType) {
      case 'tone':
        setEnhancingTone(true);
        break;
      case 'description':
        setEnhancingDesc(true);
        break;
      case 'audience':
        setEnhancingAudience(true);
        break;
      case 'action':
        setEnhancingAction(true);
        break;
    }
    
    try {
      const { data, error } = await supabase.functions.invoke('enhance-text', {
        body: { 
          text, 
          enhancementType 
        }
      });

      if (error) {
        console.error('Error enhancing text:', error);
        toast({
          title: "Enhancement failed",
          description: "There was an error enhancing your content. Please try again.",
          variant: "destructive"
        });
        return;
      }

      if (data.error) {
        console.error('API error:', data.error);
        toast({
          title: "Enhancement failed",
          description: data.error || "There was an error enhancing your content. Please try again.",
          variant: "destructive"
        });
        return;
      }

      // Use the appropriate setter based on enhancement type
      switch (enhancementType) {
        case 'tone':
          onToneChange(data.enhancedText);
          break;
        case 'description':
          onDescriptionChange(data.enhancedText);
          break;
        case 'audience':
          onAudienceChange(data.enhancedText);
          break;
        case 'action':
          onActionChange(data.enhancedText);
          break;
        default:
          console.warn('Unknown enhancement type:', enhancementType);
      }
      
      toast({
        title: "Content enhanced",
        description: "Your content has been enhanced with AI."
      });
    } catch (error) {
      console.error('Exception enhancing text:', error);
      toast({
        title: "Enhancement failed",
        description: "There was an error enhancing your content. Please try again.",
        variant: "destructive"
      });
    } finally {
      // Reset loading state
      switch (enhancementType) {
        case 'tone':
          setEnhancingTone(false);
          break;
        case 'description':
          setEnhancingDesc(false);
          break;
        case 'audience':
          setEnhancingAudience(false);
          break;
        case 'action':
          setEnhancingAction(false);
          break;
      }
    }
  };

  return {
    enhancingTone,
    enhancingDesc,
    enhancingAudience,
    enhancingAction,
    enhanceText
  };
};
