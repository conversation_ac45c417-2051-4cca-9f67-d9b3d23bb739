
import React, { useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Building, Globe, Users } from 'lucide-react';
import PageBackground from '@/components/layout/PageBackground';
import { useAuth } from '@/context/AuthContext';

// Import refactored tab components
import CompanyTab from '@/components/settings/CompanyTab';
import CompanyBrandingTab from '@/components/settings/CompanyBrandingTab';
import PersonalBrandingTab from '@/components/settings/PersonalBrandingTab';
import UsersTab from '@/components/settings/UsersTab';

const Settings = () => {
  const { isAdmin } = useAuth();
  
  // Track tab changes to trigger auto-save
  const handleTabChange = (value: string) => {
    console.log("Tab changed to:", value);
    // Dispatch a custom event for tab changes
    const event = new CustomEvent('settingsTabChange', { detail: { tab: value } });
    document.dispatchEvent(event);
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <PageBackground />
      <Navbar />
      
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-enterprise-gray-600">Manage your account and preferences</p>
        </div>
        
        <Tabs defaultValue="company" className="w-full" onValueChange={handleTabChange}>
          <TabsList className="grid w-full md:w-auto grid-cols-2 md:grid-cols-4 mb-8">
            <TabsTrigger value="company" className="flex items-center">
              <Building className="mr-2 h-4 w-4" />
              <span>Company</span>
            </TabsTrigger>
            <TabsTrigger value="company-branding">
              <Globe className="mr-2 h-4 w-4" />
              <span>Company Branding</span>
            </TabsTrigger>
            <TabsTrigger value="personal-branding">
              <Globe className="mr-2 h-4 w-4" />
              <span>Personal Branding</span>
            </TabsTrigger>
            <TabsTrigger value="users">
              <Users className="mr-2 h-4 w-4" />
              <span>Users</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="company">
            <CompanyTab />
          </TabsContent>
          
          <TabsContent value="company-branding">
            <CompanyBrandingTab />
          </TabsContent>
          
          <TabsContent value="personal-branding">
            <PersonalBrandingTab />
          </TabsContent>
          
          <TabsContent value="users">
            <UsersTab />
          </TabsContent>
        </Tabs>
      </div>
      
      <Footer />
    </div>
  );
};

export default Settings;
