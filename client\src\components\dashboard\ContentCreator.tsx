
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import ContentTypeSelector from './components/ContentTypeSelector';
import PostTypeSelector from './components/PostTypeSelector';
import ContentForm from './components/ContentForm';
import GeneratedContent from './components/GeneratedContent';
import { ContentProvider, useContent } from '@/context/ContentContext';

const ContentCreatorInner = () => {
  const { activeBox, generatedContent } = useContent();
  
  return (
    <div className="grid grid-cols-1 gap-6 max-w-3xl mx-auto">
      <Card className="border border-white/20 bg-white/40 backdrop-blur-sm shadow-lg">
        <CardContent className="space-y-6 p-6">
          <ContentTypeSelector />

          <div className={`space-y-5 ${activeBox ? 'animate-fade-in' : 'hidden'}`}>
            <ContentForm />
            <PostTypeSelector />
            
            {activeBox && (
              <div className="pt-4">
                <GenerateButton />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {generatedContent && (
        <GeneratedContent />
      )}
    </div>
  );
};

// Extract generate button into its own component
const GenerateButton = () => {
  const { contentIdea, isGenerating, activeBox, generateContent } = useContent();
  
  const buttonClass = activeBox === 'company' 
    ? 'bg-gradient-to-r from-enterprise-blue to-blue-400 hover:from-enterprise-blue/90 hover:to-blue-500'
    : 'bg-gradient-to-r from-enterprise-teal to-teal-400 hover:from-enterprise-teal/90 hover:to-teal-500';
  
  return (
    <button 
      onClick={generateContent} 
      className={`w-full px-4 py-3 rounded-md font-medium text-white transition-all shadow-md hover:shadow-lg ${buttonClass}`}
      disabled={!contentIdea.trim() || isGenerating}
    >
      {isGenerating ? (
        <div className="flex items-center justify-center gap-2">
          <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Crafting your post...</span>
        </div>
      ) : (
        <span>Generate LinkedIn Post</span>
      )}
    </button>
  );
};

const ContentCreator = () => {
  return (
    <ContentProvider>
      <ContentCreatorInner />
    </ContentProvider>
  );
};

export default ContentCreator;
