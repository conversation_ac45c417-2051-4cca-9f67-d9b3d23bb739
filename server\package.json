{"name": "unipile-auth-backend", "version": "1.0.0", "description": "Backend service for Unipile account authentication with 2FA support", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "nodemon --watch src --exec ts-node src/app.ts"}, "keywords": [], "author": "Your Name", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.50.2", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "postgres": "^3.4.7", "unipile-node-sdk": "^1.9.3"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/node": "^20.19.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.8.3"}}