import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { toast } from 'sonner';
import LinkedInHostedAuth from './LinkedInHostedAuth';

const HostedAuthTest: React.FC = () => {
  const [userEmail, setUserEmail] = useState('<EMAIL>');
  const [isTestingAuth, setIsTestingAuth] = useState(false);
  const [authResult, setAuthResult] = useState<{
    success: boolean;
    accountId?: string;
    error?: string;
  } | null>(null);

  const handleAuthSuccess = (accountId: string) => {
    console.log('✅ LinkedIn authentication successful:', accountId);
    setAuthResult({
      success: true,
      accountId: accountId
    });
    setIsTestingAuth(false);
    toast.success(`LinkedIn account connected successfully! Account ID: ${accountId}`);
  };

  const handleAuthError = (error: string) => {
    console.error('❌ LinkedIn authentication failed:', error);
    setAuthResult({
      success: false,
      error: error
    });
    setIsTestingAuth(false);
    toast.error(`LinkedIn authentication failed: ${error}`);
  };

  const startTest = () => {
    if (!userEmail) {
      toast.error('Please enter an email address');
      return;
    }
    setIsTestingAuth(true);
    setAuthResult(null);
  };

  const resetTest = () => {
    setIsTestingAuth(false);
    setAuthResult(null);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5 text-blue-500" />
            LinkedIn Hosted Auth Test
          </CardTitle>
          <CardDescription>
            Test the new LinkedIn hosted authentication flow with proper verification waiting.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">🔄 New Verification Flow:</h4>
            <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
              <li>Click "Start LinkedIn Authentication" to generate hosted auth URL</li>
              <li>Popup opens with LinkedIn's official login page</li>
              <li><strong>Complete ALL LinkedIn verification</strong> (CAPTCHA, OTP, security checks)</li>
              <li><strong>Click "I've Completed Verification"</strong> in the popup when done</li>
              <li>System verifies account was actually created via webhook</li>
              <li>Success message shows with real account ID</li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* Test Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Test Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="email">Test Email Address</Label>
            <Input
              id="email"
              type="email"
              value={userEmail}
              onChange={(e) => setUserEmail(e.target.value)}
              placeholder="Enter email for testing"
              disabled={isTestingAuth}
            />
          </div>
          
          <div className="flex gap-3">
            <Button
              onClick={startTest}
              disabled={isTestingAuth || !userEmail}
              className="button-primary"
            >
              {isTestingAuth ? 'Testing in Progress...' : 'Start LinkedIn Authentication Test'}
            </Button>
            
            {(isTestingAuth || authResult) && (
              <Button
                onClick={resetTest}
                variant="outline"
              >
                Reset Test
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Authentication Component */}
      {isTestingAuth && (
        <Card>
          <CardHeader>
            <CardTitle>LinkedIn Authentication</CardTitle>
            <CardDescription>
              Complete the LinkedIn authentication process below.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <LinkedInHostedAuth
              userEmail={userEmail}
              onSuccess={handleAuthSuccess}
              onError={handleAuthError}
            />
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {authResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {authResult.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-red-500" />
              )}
              Test Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            {authResult.success ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-800 mb-2">✅ Authentication Successful!</h4>
                <div className="text-sm text-green-700 space-y-1">
                  <p><strong>Account ID:</strong> {authResult.accountId}</p>
                  <p><strong>Status:</strong> LinkedIn account connected and verified</p>
                  <p><strong>Next Steps:</strong> Account is ready for use in the application</p>
                </div>
              </div>
            ) : (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-800 mb-2">❌ Authentication Failed</h4>
                <div className="text-sm text-red-700 space-y-1">
                  <p><strong>Error:</strong> {authResult.error}</p>
                  <p><strong>Possible Causes:</strong></p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li>LinkedIn verification was not completed</li>
                    <li>User cancelled the verification process</li>
                    <li>Network error during verification</li>
                    <li>Webhook callback was not received</li>
                  </ul>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">⚠️ Important Notes:</h4>
              <ul className="text-sm text-yellow-700 space-y-1 list-disc list-inside">
                <li><strong>Do NOT close the popup early</strong> - wait for LinkedIn verification to complete</li>
                <li><strong>Complete ALL verification steps</strong> - CAPTCHA, OTP, security checks</li>
                <li><strong>Only click "I've Completed Verification"</strong> after successful LinkedIn login</li>
                <li><strong>Wait for account verification</strong> - the system checks that account was actually created</li>
              </ul>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-800 mb-2">✅ Expected Behavior:</h4>
              <ul className="text-sm text-green-700 space-y-1 list-disc list-inside">
                <li>Popup stays open during LinkedIn verification</li>
                <li>Timer shows elapsed time in popup</li>
                <li>User manually confirms completion</li>
                <li>System verifies account via webhook</li>
                <li>Real account ID is returned on success</li>
              </ul>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">🔍 What's Different:</h4>
              <ul className="text-sm text-blue-700 space-y-1 list-disc list-inside">
                <li><strong>Before:</strong> Popup closed immediately, showed fake success</li>
                <li><strong>Now:</strong> Popup waits for real verification completion</li>
                <li><strong>Before:</strong> No account actually created</li>
                <li><strong>Now:</strong> Real account verification via webhook</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HostedAuthTest;
