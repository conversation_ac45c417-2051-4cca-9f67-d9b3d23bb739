// Phase 2: Post Sync Routes
// Express routes for LinkedIn post synchronization API endpoints

import express from 'express';
import {
    handleSyncPosts,
    handleGetSyncStatus,
    handleGetUserPosts,
    handleMarkPostsViewed,
    handleGetPostAnalytics
} from '../handlers/postSync.handlers';

const router = express.Router();

/**
 * POST /api/posts/sync
 * Trigger LinkedIn post synchronization for a user
 * 
 * Body:
 * {
 *   "userId": "string",
 *   "accountId": "string", // Optional if stored mapping exists
 *   "userIdentifier": "string", // Optional if stored mapping exists
 *   "department": "string", // Optional, defaults to "other"
 *   "limit": number, // Optional, defaults to 50
 *   "fullSync": boolean // Optional, defaults to false
 * }
 */
router.post('/sync', handleSyncPosts);

/**
 * GET /api/posts/sync/status/:userId
 * Get synchronization status for a user
 * 
 * Params:
 * - userId: User ID to get sync status for
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "totalPosts": number,
 *     "lastSyncAt": "ISO string",
 *     "newPosts": number,
 *     "updatedPosts": number
 *   }
 * }
 */
router.get('/sync/status/:userId', handleGetSyncStatus);

/**
 * GET /api/posts/list/:userId
 * Get stored LinkedIn posts for a user
 * 
 * Params:
 * - userId: User ID to get posts for
 * 
 * Query Parameters:
 * - limit: Number of posts to return (default: 50)
 * - offset: Number of posts to skip (default: 0)
 * - newOnly: Return only newly added posts (default: false)
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "posts": [...],
 *     "pagination": {
 *       "total": number,
 *       "limit": number,
 *       "offset": number,
 *       "hasMore": boolean
 *     },
 *     "filters": {
 *       "newOnly": boolean
 *     }
 *   }
 * }
 */
router.get('/list/:userId', handleGetUserPosts);

/**
 * POST /api/posts/mark-viewed
 * Mark posts as viewed (clear new/updated flags)
 * 
 * Body:
 * {
 *   "userId": "string",
 *   "postIds": ["string"] // Optional, if not provided marks all posts as viewed
 * }
 */
router.post('/mark-viewed', handleMarkPostsViewed);

/**
 * GET /api/posts/analytics/:userId
 * Get post analytics summary for a user
 * 
 * Params:
 * - userId: User ID to get analytics for
 * 
 * Query Parameters:
 * - days: Number of days to include in analytics (default: 30)
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "analytics": {
 *       "totalPosts": number,
 *       "totalLikes": number,
 *       "totalComments": number,
 *       "totalShares": number,
 *       "totalImpressions": number,
 *       "averageEngagementRate": number,
 *       "topPerformingPost": object,
 *       "postsByType": object
 *     },
 *     "period": {
 *       "days": number,
 *       "from": "ISO string",
 *       "to": "ISO string"
 *     }
 *   }
 * }
 */
router.get('/analytics/:userId', handleGetPostAnalytics);

export default router;
