
import React from 'react';
import { Button } from "@/components/ui/button";
import { FileDown } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

const downloadCSV = async () => {
  try {
    const { data, error } = await supabase
      .from('employee_posts')
      .select(`
        title,
        content,
        post_type,
        department,
        impressions,
        likes,
        comments,
        engagement_rate,
        post_date
      `)
      .order('post_date', { ascending: false });

    if (error) throw error;

    if (data) {
      // Convert data to CSV format
      const headers = ['Title', 'Content', 'Post Type', 'Department', 'Impressions', 'Likes', 'Comments', 'Engagement Rate', 'Post Date'];
      const csvContent = [
        headers.join(','),
        ...data.map(row => [
          `"${row.title.replace(/"/g, '""')}"`,
          `"${row.content.replace(/"/g, '""')}"`,
          row.post_type,
          row.department,
          row.impressions,
          row.likes,
          row.comments,
          `${row.engagement_rate}%`,
          new Date(row.post_date).toLocaleDateString()
        ].join(','))
      ].join('\n');

      // Create and download the CSV file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `employee_posts_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  } catch (error) {
    console.error('Error exporting data:', error);
  }
};

export const AdminFiltersSection = () => {
  return (
    <div className="my-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Export Data</h2>
        <Button 
          onClick={downloadCSV}
          className="bg-enterprise-blue hover:bg-enterprise-blue/90"
        >
          <FileDown className="mr-2 h-4 w-4" />
          Export to CSV
        </Button>
      </div>
    </div>
  );
};
