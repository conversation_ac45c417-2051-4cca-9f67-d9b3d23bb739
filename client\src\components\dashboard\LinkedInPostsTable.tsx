
import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, RefreshCw, Linkedin, Building, User, Filter, Heart, MessageCircle, Share2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Post } from '@/types/post';
import LinkedInPostModal from '@/components/linkedin/LinkedInPostModal';
import { MetricUpdateAnimation, useMetricUpdates } from '@/components/ui/MetricUpdateAnimation';

// LinkedIn Post interface for real data
interface LinkedInPost {
  id: string;
  social_id?: string;
  text: string;
  created_at: string;
  impressions: number;
  likes: number;
  comments: number;
  shares: number;
  engagement_rate: number;
}

interface LinkedInPostsTableProps {
  posts?: LinkedInPost[];
  onViewDetails?: (postId: string) => void;
}

const LinkedInPostsTable: React.FC<LinkedInPostsTableProps> = ({ posts: linkedInPosts = [], onViewDetails }) => {
  const [storedPosts, setStoredPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filterType, setFilterType] = useState<string | null>(null);
  const [selectedPost, setSelectedPost] = useState<LinkedInPost | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  // Metric update animations
  const { updateMetrics, isMetricUpdated, getPreviousValue } = useMetricUpdates();
  const isFirstRender = useRef(true);

  // Track metric changes when posts data updates
  useEffect(() => {
    if (linkedInPosts && Array.isArray(linkedInPosts) && linkedInPosts.length > 0) {
      // Skip metric updates on first render to prevent infinite loops
      if (isFirstRender.current) {
        isFirstRender.current = false;
        return;
      }

      linkedInPosts.forEach(post => {
        if (post && post.id) {
          const metrics = {
            impressions: post.impressions || 0,
            likes: post.likes || 0,
            comments: post.comments || 0,
            shares: post.shares || 0,
            engagement_rate: post.engagement_rate || 0
          };
          updateMetrics(metrics, post.id);
        }
      });
    }
  }, [linkedInPosts]); // Remove updateMetrics from dependencies to prevent infinite loop

  // Check if any metric in a post is updated
  const isPostUpdated = (postId: string) => {
    if (!linkedInPosts || linkedInPosts.length === 0) return false;
    return ['impressions', 'likes', 'comments', 'shares', 'engagement_rate']
      .some(metric => isMetricUpdated(postId, metric));
  };

  // Format post date properly
  const formatPostDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 1) {
        return '1 day ago';
      } else if (diffDays < 7) {
        return `${diffDays} days ago`;
      } else if (diffDays < 30) {
        const weeks = Math.floor(diffDays / 7);
        return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
      } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30);
        return months === 1 ? '1 month ago' : `${months} months ago`;
      } else {
        const years = Math.floor(diffDays / 365);
        return years === 1 ? '1 year ago' : `${years} years ago`;
      }
    } catch {
      return 'N/A';
    }
  };

  const formatFullDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        timeZone: 'UTC', // 👈 Force UTC to prevent local time conversion
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });
    } catch {
      return 'N/A';
    }
  };

  // Use LinkedIn posts if provided, otherwise fetch stored posts
  const useLinkedInData = linkedInPosts && linkedInPosts.length > 0;
  console.log('📊 LinkedInPostsTable received posts:', {
    linkedInPosts: linkedInPosts?.length || 0,
    useLinkedInData,
    hasMetricUpdates: !!updateMetrics
  });
  useEffect(() => {
    if (!useLinkedInData) {
      fetchStoredPosts();
    } else {
      setIsLoading(false);
    }
  }, [user?.id, useLinkedInData]);

  const fetchStoredPosts = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      const { data, error } = await supabase
        .from('employee_posts')
        .select('*')
        .eq('user_id', user.id)
        .in('status', ['published', 'approved'])
        .order('post_date', { ascending: false });

      if (error) throw error;


      setStoredPosts(data as Post[]);
    } catch (error) {
      console.error('Error fetching stored posts:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load your stored posts."
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Filter logic for stored posts
  const filteredStoredPosts = filterType
    ? storedPosts.filter(post => post.post_type === filterType)
    : storedPosts;

  // Get display posts based on data source
  const displayPosts = useLinkedInData ? linkedInPosts : filteredStoredPosts;

  // Handle viewing full post
  const handleViewPost = (post: LinkedInPost | Post) => {
    if (useLinkedInData && 'text' in post) {
      const linkedInPost = post as LinkedInPost;
      console.log('📊 LinkedIn post clicked for comprehensive view:', linkedInPost);

      // Check if we have the comprehensive view handler - prioritize regular id over social_id
      if (onViewDetails && linkedInPost.id) {
        console.log(`🚀 Opening comprehensive view for post_id: ${linkedInPost.id}`);
        console.log(`📝 Note: Will use social_id (${linkedInPost.social_id}) for comments/reactions internally`);
        onViewDetails(linkedInPost.id);
      } else if (onViewDetails && linkedInPost.social_id) {
        console.log(`🚀 Opening comprehensive view for social_id: ${linkedInPost.social_id} (fallback)`);
        onViewDetails(linkedInPost.social_id);
      } else {
        // Fallback to modal view
        console.log('📝 Fallback to modal view');
        setSelectedPost(linkedInPost);
        setIsModalOpen(true);
      }
    } else {
      // For stored posts, could implement a different view
      toast({
        title: "View Post",
        description: "Full post view is available for LinkedIn posts only."
      });
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <CardTitle className="text-xl flex items-center gap-2">
            <Linkedin className="h-5 w-5 text-[#0A66C2]" />
            {useLinkedInData ? 'LinkedIn Posts (Real-time)' : 'Your Stored Posts'}
          </CardTitle>
          <div className="flex gap-2 items-center">
            {/* Only show filters for stored posts */}
            {!useLinkedInData && (
              <ToggleGroup type="single" value={filterType || ''} onValueChange={value => setFilterType(value || null)}>
                <ToggleGroupItem value="" aria-label="Show all posts">
                  <Filter className="h-4 w-4 mr-1" />
                  All
                </ToggleGroupItem>
                <ToggleGroupItem value="company" aria-label="Show company posts">
                  <Building className="h-4 w-4 mr-1" />
                  Company
                </ToggleGroupItem>
                <ToggleGroupItem value="personal" aria-label="Show personal posts">
                  <User className="h-4 w-4 mr-1" />
                  Personal
                </ToggleGroupItem>
              </ToggleGroup>
            )}

            {!useLinkedInData && (
              <Button variant="outline" size="sm" className="text-xs" onClick={fetchStoredPosts}>
                <RefreshCw size={14} className="mr-1" />
                Refresh
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        ) : displayPosts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>
              {useLinkedInData
                ? "No LinkedIn posts found."
                : filterType
                  ? `You don't have any ${filterType} posts published yet.`
                  : "You don't have any posts published yet."}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium">Post</th>
                  <th className="text-center py-3 px-4 font-medium">Published</th>
                  {!useLinkedInData && <th className="text-center py-3 px-4 font-medium">Type</th>}
                  <th className="text-center py-3 px-4 font-medium">Impressions</th>
                  {useLinkedInData && <th className="text-center py-3 px-4 font-medium">Likes</th>}
                  {useLinkedInData && <th className="text-center py-3 px-4 font-medium">Comments</th>}
                  {useLinkedInData && <th className="text-center py-3 px-4 font-medium">Shares</th>}
                  <th className="text-center py-3 px-4 font-medium">Engagement</th>
                  <th className="text-center py-3 px-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {displayPosts.map((post) => {
                  // Handle both LinkedIn and stored post types
                  const isLinkedInPost = 'text' in post;

                  return (
                    <tr
                      key={post.id}
                      className={`border-b hover:bg-gray-50 transition-all duration-500 ${
                        isPostUpdated(post.id) ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <td className="py-3 px-4">
                        <div className="font-medium max-w-xs truncate">
                          {isLinkedInPost
                            ? (post as LinkedInPost).text.substring(0, 60) + (post.text.length > 60 ? '...' : '')
                            : (post as Post).title
                          }
                        </div>

                      </td>
                      <td className="py-3 px-4 text-center text-gray-600">
                        {isLinkedInPost ? (
                          <>
                            <div>{formatPostDate((post as LinkedInPost).created_at)}</div>
                            <div className="text-sm text-gray-400">
                              {formatFullDateTime((post as LinkedInPost).created_at)}
                            </div>
                          </>
                        ) : (post as Post).created_at ? (
                          <>
                            <div>{formatPostDate((post as Post).created_at)}</div>
                            <div className="text-sm text-gray-400">
                              {formatFullDateTime((post as Post).created_at)}
                            </div>
                          </>
                        ) : (
                          'N/A'
                        )}
                      </td>


                      {!useLinkedInData && (
                        <td className="py-3 px-4 text-center">
                          <Badge
                            variant="outline"
                            className={`flex items-center justify-center ${(post as Post).post_type === 'company'
                              ? 'bg-blue-100 text-blue-800 border-blue-200'
                              : 'bg-purple-100 text-purple-800 border-purple-200'
                              }`}
                          >
                            {(post as Post).post_type === 'company' ? (
                              <Building size={12} className="mr-1" />
                            ) : (
                              <User size={12} className="mr-1" />
                            )}
                            {(post as Post).post_type}
                          </Badge>
                        </td>
                      )}
                      <td className="py-3 px-4 text-center">
                        {isLinkedInPost ? (
                          <MetricUpdateAnimation
                            currentValue={(post as LinkedInPost).impressions || 0}
                            previousValue={getPreviousValue(post.id, 'impressions')}
                            showAnimation={isMetricUpdated(post.id, 'impressions')}
                          />
                        ) : (
                          (post as Post).impressions?.toLocaleString() || '0'
                        )}
                      </td>
                      {useLinkedInData && (
                        <>
                          <td className="py-3 px-4 text-center">
                            <div className="flex items-center justify-center gap-1">
                              <Heart size={14} className="text-red-500" />
                              <MetricUpdateAnimation
                                currentValue={(post as LinkedInPost).likes || 0}
                                previousValue={getPreviousValue(post.id, 'likes')}
                                showAnimation={isMetricUpdated(post.id, 'likes')}
                              />
                            </div>
                          </td>
                          <td className="py-3 px-4 text-center">
                            <div className="flex items-center justify-center gap-1">
                              <MessageCircle size={14} className="text-blue-500" />
                              <MetricUpdateAnimation
                                currentValue={(post as LinkedInPost).comments || 0}
                                previousValue={getPreviousValue(post.id, 'comments')}
                                showAnimation={isMetricUpdated(post.id, 'comments')}
                              />
                            </div>
                          </td>
                          <td className="py-3 px-4 text-center">
                            <div className="flex items-center justify-center gap-1">
                              <Share2 size={14} className="text-green-500" />
                              <MetricUpdateAnimation
                                currentValue={(post as LinkedInPost).shares || 0}
                                previousValue={getPreviousValue(post.id, 'shares')}
                                showAnimation={isMetricUpdated(post.id, 'shares')}
                              />
                            </div>
                          </td>
                        </>
                      )}
                      <td className="py-3 px-4 text-center">
                        {isLinkedInPost ? (
                          <div className="flex items-center justify-center">
                            <MetricUpdateAnimation
                              currentValue={(post as LinkedInPost).engagement_rate || 0}
                              previousValue={getPreviousValue(post.id, 'engagement_rate')}
                              showAnimation={isMetricUpdated(post.id, 'engagement_rate')}
                            />
                            <span className="ml-1">%</span>
                          </div>
                        ) : (
                          (post as Post).engagement_rate ? `${(post as Post).engagement_rate}%` : '0%'
                        )}
                      </td>
                      <td className="py-3 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewPost(post)}
                            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                          >
                            <Eye size={16} className="mr-1" />
                            {useLinkedInData ? 'View Full Post' : 'View'}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>

      {/* LinkedIn Post Modal */}
      {selectedPost && (
        <LinkedInPostModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedPost(null);
          }}
          post={selectedPost}
          userId={user?.id || ''}
        />
      )}
    </Card>
  );
};

export default LinkedInPostsTable;
