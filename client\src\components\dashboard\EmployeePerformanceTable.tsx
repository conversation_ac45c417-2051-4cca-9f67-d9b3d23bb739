
import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, ExternalLink } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface Employee {
  id: string;
  name: string;
  department: string;
  impressions: number;
  engagementRate: number;
  postsThisWeek: number;
  lastPostDate: string;
  trend: "up" | "down" | "stable";
}

// No mock data - will fetch real employee data from API

export const EmployeePerformanceTable = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [sortConfig, setSortConfig] = useState<{
    key: keyof Employee;
    direction: "asc" | "desc";
  } | null>(null);

  const sortedEmployees = [...employees].sort((a, b) => {
    if (!sortConfig) return 0;
    
    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];
    
    if (aValue < bValue) return sortConfig.direction === "asc" ? -1 : 1;
    if (aValue > bValue) return sortConfig.direction === "asc" ? 1 : -1;
    return 0;
  });

  const requestSort = (key: keyof Employee) => {
    const direction = 
      sortConfig?.key === key && sortConfig.direction === "asc" 
        ? "desc" 
        : "asc";
    setSortConfig({ key, direction });
  };

  const getTrendBadge = (trend: Employee["trend"]) => {
    const colors = {
      up: "bg-green-100 text-green-800",
      down: "bg-red-100 text-red-800",
      stable: "bg-blue-100 text-blue-800",
    };
    
    return (
      <Badge variant="outline" className={colors[trend]}>
        {trend === "up" ? "↑" : trend === "down" ? "↓" : "→"} {trend}
      </Badge>
    );
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Department</TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => requestSort("impressions")}
                className="h-8 px-2 hover:bg-transparent"
              >
                Impressions
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </Button>
            </TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => requestSort("engagementRate")}
                className="h-8 px-2 hover:bg-transparent"
              >
                Engagement Rate
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </Button>
            </TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => requestSort("postsThisWeek")}
                className="h-8 px-2 hover:bg-transparent"
              >
                Posts This Week
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </Button>
            </TableHead>
            <TableHead>Last Post</TableHead>
            <TableHead>Trend</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedEmployees.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                No employee data available. Employee performance tracking will be available when team members connect their LinkedIn accounts.
              </TableCell>
            </TableRow>
          ) : (
            sortedEmployees.map((employee) => (
              <TableRow key={employee.id}>
                <TableCell className="font-medium">{employee.name}</TableCell>
                <TableCell>{employee.department}</TableCell>
                <TableCell>{employee.impressions.toLocaleString()}</TableCell>
                <TableCell>{employee.engagementRate}%</TableCell>
                <TableCell>{employee.postsThisWeek}</TableCell>
                <TableCell>{new Date(employee.lastPostDate).toLocaleDateString()}</TableCell>
                <TableCell>{getTrendBadge(employee.trend)}</TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};
