
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Check, Image, Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { useAuth } from '@/context/AuthContext';

interface ColorOption {
  name: string;
  value: string;
  textClass: string;
  bgClass: string;
}

const colorOptions: ColorOption[] = [
  { name: "Ocean Blue", value: "blue", textClass: "text-blue-500", bgClass: "bg-blue-100" },
  { name: "<PERSON> Green", value: "green", textClass: "text-green-500", bgClass: "bg-green-100" },
  { name: "Vibrant Purple", value: "purple", textClass: "text-purple-500", bgClass: "bg-purple-100" },
  { name: "Sunset Orange", value: "orange", textClass: "text-orange-500", bgClass: "bg-orange-100" },
  { name: "Ruby Red", value: "red", textClass: "text-red-500", bgClass: "bg-red-100" },
];

const PersonalBrandingTab = () => {
  const { toast } = useToast();
  const { profile } = useAuth();
  
  const [headline, setHeadline] = useState("Product Manager | Tech Enthusiast | Problem Solver");
  const [bio, setBio] = useState(
    "Passionate about building products that make a difference. I love connecting technology with human needs."
  );
  const [linkedInUrl, setLinkedInUrl] = useState(profile?.linkedin_url || "");
  const [preferredColor, setPreferredColor] = useState("blue");
  const [personalHashtags, setPersonalHashtags] = useState("#ProductManagement, #TechTrends, #Innovation");
  
  const [showPersonalInfo, setShowPersonalInfo] = useState(true);
  const [linkArticles, setLinkArticles] = useState(true);
  const [includeName, setIncludeName] = useState(true);
  
  const handleSavePersonalBranding = () => {
    toast({
      title: "Personal branding updated",
      description: "Your personal branding preferences have been saved successfully."
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Personal Brand Profile</CardTitle>
          <CardDescription>
            Customize how your personal brand appears in your social media content
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <div className="h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                <Image className="h-12 w-12 text-gray-400" />
              </div>
              <div className="flex-1">
                <Label htmlFor="profile-name">Your Name</Label>
                <Input 
                  id="profile-name" 
                  value={profile?.first_name && profile?.last_name 
                    ? `${profile.first_name} ${profile.last_name}`
                    : "Your Name"
                  }
                  disabled
                  className="mb-2"
                />
                <Button variant="outline" size="sm">
                  Update Profile Photo
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="headline">Professional Headline</Label>
              <Input 
                id="headline" 
                value={headline}
                onChange={(e) => setHeadline(e.target.value)}
                placeholder="e.g., Marketing Director | Content Strategist | Speaker"
              />
              <p className="text-sm text-muted-foreground">
                This will appear in your posts to establish your expertise
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="bio">Mini Bio</Label>
              <Textarea 
                id="bio" 
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                placeholder="Brief description of your professional background and interests"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="linkedin-url">LinkedIn URL</Label>
              <Input 
                id="linkedin-url" 
                value={linkedInUrl}
                onChange={(e) => setLinkedInUrl(e.target.value)}
                placeholder="https://linkedin.com/in/yourprofile"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="personal-hashtags">Personal Hashtags</Label>
              <Input 
                id="personal-hashtags" 
                value={personalHashtags}
                onChange={(e) => setPersonalHashtags(e.target.value)}
                placeholder="#Leadership, #Innovation, #YourIndustry"
              />
              <p className="text-sm text-muted-foreground">
                Enter hashtags separated by commas
              </p>
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="text-base font-medium">Color Theme Preference</h3>
            <p className="text-sm text-muted-foreground">
              Choose a color that represents your personal brand
            </p>
            
            <RadioGroup 
              value={preferredColor} 
              onValueChange={setPreferredColor}
              className="grid grid-cols-2 md:grid-cols-5 gap-3"
            >
              {colorOptions.map((color) => (
                <div key={color.value} className="flex items-center space-x-2">
                  <RadioGroupItem value={color.value} id={`color-${color.value}`} />
                  <Label 
                    htmlFor={`color-${color.value}`}
                    className="flex items-center cursor-pointer"
                  >
                    <span 
                      className={`h-5 w-5 rounded-full ${color.bgClass} mr-2 flex items-center justify-center`}
                    >
                      <span className={`h-3 w-3 rounded-full ${color.textClass.replace('text', 'bg')}`}></span>
                    </span>
                    {color.name}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
          
          <div className="space-y-4">
            <h3 className="text-base font-medium">Content Preferences</h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="show-personal-info" className="block">
                    Show personal information
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Include your bio in posts
                  </p>
                </div>
                <Switch 
                  id="show-personal-info" 
                  checked={showPersonalInfo}
                  onCheckedChange={setShowPersonalInfo}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="link-articles" className="block">
                    Link to related articles
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Reference articles you've written when relevant
                  </p>
                </div>
                <Switch 
                  id="link-articles" 
                  checked={linkArticles}
                  onCheckedChange={setLinkArticles}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="include-name" className="block">
                    Include name in content
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Add your signature to posts
                  </p>
                </div>
                <Switch 
                  id="include-name" 
                  checked={includeName}
                  onCheckedChange={setIncludeName}
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-base font-medium">Personal Tone Preferences</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">Professional</Badge>
                <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">Conversational</Badge>
                <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">Thoughtful</Badge>
                <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer">+ Add</Badge>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button 
            className="bg-enterprise-blue hover:bg-enterprise-blue/90"
            onClick={handleSavePersonalBranding}
          >
            Save Personal Branding
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default PersonalBrandingTab;
