
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Clock, LinkedinIcon, Twitter, Facebook, AlertCircle, CheckCircle, User } from 'lucide-react';

interface ContentCardProps {
  content: {
    id: string;
    author: string;
    department: string;
    platform: string;
    content: string;
    submitted: string;
    approved?: string;
    rejected?: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  rejectionReason?: string;
  actions?: React.ReactNode;
}

const ContentCard: React.FC<ContentCardProps> = ({ 
  content, 
  status,
  rejectionReason,
  actions 
}) => {
  const getPlatformIcon = () => {
    switch (content.platform.toLowerCase()) {
      case 'linkedin':
        return <LinkedinIcon size={16} className="text-[#0A66C2]" />;
      case 'twitter':
        return <Twitter size={16} className="text-[#1DA1F2]" />;
      case 'facebook':
        return <Facebook size={16} className="text-[#1877F2]" />;
      default:
        return null;
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="border-amber-500 text-amber-600 flex items-center"><Clock size={12} className="mr-1" />Awaiting Approval</Badge>;
      case 'approved':
        return <Badge variant="outline" className="border-green-500 text-green-600 flex items-center"><CheckCircle size={12} className="mr-1" />Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="border-red-500 text-red-600 flex items-center"><AlertCircle size={12} className="mr-1" />Rejected</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="border rounded-lg p-4 shadow-sm">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 rounded-full bg-enterprise-gray-200 flex items-center justify-center">
            <User size={18} className="text-enterprise-gray-600" />
          </div>
          <div>
            <div className="font-medium">{content.author}</div>
            <div className="text-sm text-muted-foreground">{content.department}</div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="flex items-center space-x-1">
            {getPlatformIcon()}
            <span>{content.platform}</span>
          </Badge>
          {getStatusBadge()}
        </div>
      </div>
      
      <div className="mt-3 text-sm p-3 bg-muted rounded-md">{content.content}</div>
      
      {rejectionReason && (
        <div className="mt-2 text-sm bg-red-50 border border-red-100 p-3 rounded-md flex items-start">
          <AlertCircle size={16} className="text-red-500 mr-2 flex-shrink-0 mt-0.5" />
          <span className="text-red-700">{rejectionReason}</span>
        </div>
      )}
      
      <div className="mt-3 flex flex-col sm:flex-row justify-between">
        <div className="text-xs text-muted-foreground">
          Submitted {content.submitted}
          {content.approved && <span> • Approved {content.approved}</span>}
          {content.rejected && <span> • Rejected {content.rejected}</span>}
        </div>
        
        {actions && (
          <div className="mt-3 sm:mt-0">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentCard;
