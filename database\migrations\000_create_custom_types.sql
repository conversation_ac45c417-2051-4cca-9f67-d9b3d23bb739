-- Phase 1: Create custom types for public_users_posts table
-- This migration creates the required ENUM types

-- Step 1: Create post_type enum
DO $$ BEGIN
    CREATE TYPE public.post_type AS ENUM (
        'text',
        'image',
        'video',
        'carousel',
        'document',
        'poll',
        'article',
        'event',
        'job_posting',
        'celebration',
        'repost',
        'native_video'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Step 2: Create department enum
DO $$ BEGIN
    CREATE TYPE public.department AS ENUM (
        'marketing',
        'sales',
        'engineering',
        'product',
        'design',
        'hr',
        'finance',
        'operations',
        'customer_success',
        'business_development',
        'legal',
        'executive',
        'general',
        'other'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Step 3: Create user_role enum (if not exists)
DO $$ BEGIN
    CREATE TYPE public.user_role AS ENUM (
        'user',
        'admin'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Step 4: Add comments for documentation
COMMENT ON TYPE public.post_type IS 'Types of LinkedIn posts that can be synchronized';
COMMENT ON TYPE public.department IS 'Department categories for organizing posts and users';
COMMENT ON TYPE public.user_role IS 'User roles for access control';
