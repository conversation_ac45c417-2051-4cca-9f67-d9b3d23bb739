// Main Unipile Service - Orchestrates all modules
import { initializeUnipileConfig, UnipileConfig } from './config';
import { OfficialUnipileHostedAuth } from './official-hosted-auth';

import {
    storeUserAccountMapping,
    getUserAccountMapping,
    getLinkedInProfile,
    listAccounts,
    checkAccountByEmail,
    UserAccountMapping
} from './account-management';
import { getAccountStatus, AccountStatusResult } from './account-status';
import { getLinkedInPosts, getLinkedInPost, getPostReactions, getPostComments, getComprehensivePostData } from './linkedin-posts';
import { getLinkedInAnalytics, enrichPostsWithAnalytics, calculateAnalytics } from './analytics';

/**
 * Main Unipile Service Class
 * Orchestrates all Unipile functionality through modular components
 */
export class UnipileService {
    private config: UnipileConfig;

    constructor() {
        this.config = initializeUnipileConfig();
    }

    // ===== HOSTED AUTH METHODS =====

    /**
     * Create hosted auth link using official Unipile API
     */
    async createHostedAuthLink(params: {
        name: string;
        type?: 'create' | 'reconnect';
        reconnect_account?: string;
        success_redirect_url?: string;
        failure_redirect_url?: string;
        notify_url?: string;
    }): Promise<any> {
        return OfficialUnipileHostedAuth.createHostedAuthLink(
            params.name,
            ['LINKEDIN'],
            params.type || 'create',
            params.reconnect_account
        );
    }

    /**
     * Get all accounts (alias for listAccounts)
     */
    async getAllAccounts(): Promise<any> {
        return this.listAccounts();
    }



    // ===== ACCOUNT MANAGEMENT METHODS =====

    /**
     * Store user account mapping for hosted auth callback matching
     */
    async storeUserAccountMapping(internalUserId: string, unipileAccountId: string, userEmail: string): Promise<void> {
        return storeUserAccountMapping(internalUserId, unipileAccountId, userEmail);
    }

    /**
     * Get user account mapping by internal user ID
     */
    async getUserAccountMapping(internalUserId: string): Promise<UserAccountMapping | null> {
        return getUserAccountMapping(internalUserId);
    }

    /**
     * Get LinkedIn profile data
     */
    async getLinkedInProfile(accountId: string): Promise<any> {
        return getLinkedInProfile(this.config, accountId);
    }

    /**
     * List all accounts
     */
    async listAccounts(): Promise<any> {
        return listAccounts(this.config);
    }

    /**
     * UNIVERSAL ACCOUNT CHECK - Always use this before any LinkedIn connection
     * This ensures NO duplicate accounts are ever created in Unipile
     */
    async checkAccountByEmail(email: string): Promise<{
        exists: boolean;
        account_id: string | null;
        status: string;
        profile?: any;
        shouldReconnect: boolean;
        error?: string;
        message?: string;
    }> {
        return checkAccountByEmail(this.config, email);
    }

    // ===== ACCOUNT STATUS METHODS =====

    /**
     * Get account status for verification
     */
    async getAccountStatus(accountId: string): Promise<AccountStatusResult> {
        return getAccountStatus(this.config, accountId);
    }

    // ===== LINKEDIN POSTS METHODS =====

    /**
     * Get LinkedIn posts
     */
    async getLinkedInPosts(accountId: string, userIdentifier: string, limit: number = 50): Promise<any> {
        return getLinkedInPosts(this.config, accountId, userIdentifier, limit);
    }

    /**
     * Get specific LinkedIn post details
     */
    async getLinkedInPost(accountId: string, postId: string): Promise<any> {
        return getLinkedInPost(this.config, accountId, postId);
    }

    /**
     * Get post reactions
     */
    async getPostReactions(accountId: string, postId: string): Promise<any> {
        return getPostReactions(this.config, accountId, postId);
    }

    /**
     * Get post comments
     */
    async getPostComments(accountId: string, postId: string): Promise<any> {
        return getPostComments(this.config, accountId, postId);
    }

    /**
     * Get comprehensive post data (post details + comments + reactions)
     */
    async getComprehensivePostData(accountId: string, postId: string): Promise<any> {
        return getComprehensivePostData(this.config, accountId, postId);
    }

    // ===== ANALYTICS METHODS =====

    /**
     * Get comprehensive LinkedIn analytics
     */
    async getLinkedInAnalytics(linkedinAccountId: string): Promise<any> {
        return getLinkedInAnalytics(this.config, linkedinAccountId);
    }

    /**
     * Enrich posts with analytics data
     */
    async enrichPostsWithAnalytics(accountId: string, postsResponse: any): Promise<any[]> {
        return enrichPostsWithAnalytics(this.config, accountId, postsResponse);
    }

    /**
     * Calculate analytics from profile and posts data
     */
    async calculateAnalytics(profile: any, posts: any[]): Promise<any> {
        return calculateAnalytics(profile, posts);
    }

    // ===== UTILITY METHODS =====

    /**
     * Get configuration for debugging
     */
    getConfig(): Partial<UnipileConfig> {
        return {
            baseUrl: this.config.baseUrl,
            apiKey: this.config.apiKey.substring(0, 10) + '...'
        };
    }

    /**
     * Health check method
     */
    async healthCheck(): Promise<{ status: string; message: string }> {
        try {
            await this.listAccounts();
            return {
                status: 'healthy',
                message: 'Unipile service is operational'
            };
        } catch (error: any) {
            return {
                status: 'unhealthy',
                message: `Unipile service error: ${error.message}`
            };
        }
    }
}

// Export the service instance
export const unipileService = new UnipileService();

// Export all types and interfaces
export type { UnipileConfig, UserAccountMapping, AccountStatusResult };

// Export individual modules for direct access if needed
export * from './config';
export * from './account-management';
export * from './account-status';
export * from './linkedin-posts';
export * from './analytics';
