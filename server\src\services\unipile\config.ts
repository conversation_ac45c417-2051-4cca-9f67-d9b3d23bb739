import { UnipileClient } from 'unipile-node-sdk';
import dotenv from 'dotenv';

dotenv.config();

export interface UnipileConfig {
    client: UnipileClient;
    baseUrl: string;
    apiKey: string;
}

/**
 * Initialize Unipile configuration and client
 * Handles environment variables and client setup
 */
export function initializeUnipileConfig(): UnipileConfig {
    const BASE_URL = process.env.UNIPILE_API_URL || "";
    const ACCESS_TOKEN = process.env.UNIPILE_API_ACCESS_TOKEN || "";

    if (!ACCESS_TOKEN) {
        throw new Error('Unipile API key missing. Please check UNIPILE_API_ACCESS_TOKEN in environment variables.');
    }

    const client = new UnipileClient(BASE_URL, ACCESS_TOKEN);

    console.log('🔧 Unipile Service initialized:', {
        baseUrl: BASE_URL,
        commonApiPath: BASE_URL,
        apiKeyLength: ACCESS_TOKEN.length,
        apiKeyPrefix: ACCESS_TOKEN.substring(0, 10) + '...'
    });

    return {
        client,
        baseUrl: BASE_URL,
        apiKey: ACCESS_TOKEN
    };
}

/**
 * Create standard headers for Unipile API requests
 */
export function createUnipileHeaders(apiKey: string): Record<string, string> {
    return {
        'x-api-key': apiKey,
        'Content-Type': 'application/json'
    };
}

/**
 * Handle Unipile API response errors
 */
export async function handleUnipileError(response: Response, context: string): Promise<never> {
    const errorText = await response.text();
    console.error(`❌ ${context} Error:`, {
        status: response.status,
        statusText: response.statusText,
        error: errorText
    });
    throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
}

/**
 * Log successful Unipile API responses
 */
export function logUnipileSuccess(context: string, data: any): void {
    console.log(`✅ ${context} response:`, data);
}

/**
 * Log Unipile API requests
 */
export function logUnipileRequest(context: string, url: string, method: string = 'GET'): void {
    console.log(`📡 ${context} API URL:`, { method, url });
}
