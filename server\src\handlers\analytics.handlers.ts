import { Request, Response } from 'express';
import { getUserById } from '../services/user.service';
import { unipileService } from '../services/unipile/index';

/**
 * Functional Analytics Handlers
 * Pure functions for handling analytics-related HTTP requests
 */

/**
 * Get calculated analytics metrics for a user
 * GET /api/analytics/metrics/:userId
 */
export const handleGetAnalyticsMetrics = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;

        console.log('🔍 GET /api/analytics/metrics/:userId - Request for user:', userId);

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        // Get user's LinkedIn account ID
        const user = await getUserById(userId);
        if (!user || !user.account_id) {
            res.status(404).json({
                success: false,
                error: 'User not found or LinkedIn account not connected'
            });
            return;
        }

        console.log('🔗 Calculating analytics for LinkedIn account:', user.account_id);

        // Get comprehensive LinkedIn analytics
        const analytics = await unipileService.getLinkedInAnalytics(user.account_id);

        console.log('✅ Analytics metrics calculated successfully');
        res.json({
            success: true,
            data: analytics
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetAnalyticsMetrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to calculate analytics metrics',
            details: error.message
        });
    }
};

/**
 * Sync analytics data for a user
 * POST /api/analytics/sync/:userId
 */
export const handleSyncAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;
        const { forceSync = false } = req.body;

        console.log('🔄 POST /api/analytics/sync/:userId - Sync request for user:', userId, 'forceSync:', forceSync);

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        // Get user's LinkedIn account ID
        const user = await getUserById(userId);
        if (!user || !user.account_id) {
            res.status(404).json({
                success: false,
                error: 'User not found or LinkedIn account not connected'
            });
            return;
        }

        console.log('🔗 Syncing analytics for LinkedIn account:', user.account_id);

        // Get fresh analytics data
        const analytics = await unipileService.getLinkedInAnalytics(user.account_id);

        // In a real app, you would save this to your database here
        console.log('💾 Analytics data synced (would save to database in real app)');

        console.log('✅ Analytics sync completed successfully');
        res.json({
            success: true,
            data: {
                userId: user.id,
                linkedinAccountId: user.account_id,
                syncedAt: new Date().toISOString(),
                analytics: analytics
            }
        });
    } catch (error: any) {
        console.error('❌ Error in handleSyncAnalytics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to sync analytics data',
            details: error.message
        });
    }
};

/**
 * Get dashboard data (combined user + analytics)
 * GET /api/analytics/dashboard/:userId
 */
export const handleGetDashboardData = async (req: Request, res: Response): Promise<void> => {
    try {
        const { userId } = req.params;
        const { linkedinAccountId, linkedinUserId } = req.query;

        console.log('🔍 GET /api/analytics/dashboard/:userId - Dashboard request for user:', userId);
        console.log('🔗 Query params:', { linkedinAccountId, linkedinUserId });

        if (!userId) {
            res.status(400).json({
                success: false,
                error: 'User ID is required'
            });
            return;
        }

        // Get user data
        const user = await getUserById(userId);
        if (!user) {
            res.status(404).json({
                success: false,
                error: 'User not found'
            });
            return;
        }

        // Check if LinkedIn is connected
        if (!user.account_id) {
            console.log('⚠️ User has no LinkedIn account connected');
            res.json({
                success: true,
                data: {
                    user: {
                        id: user.id,
                        email: user.email,
                        first_name: user.first_name,
                        last_name: user.last_name,
                        linkedin_connection_status: user.linkedin_connection_status || 'not_connected'
                    },
                    analytics: null,
                    isRealTime: false,
                    last_sync: null
                }
            });
            return;
        }

        console.log('🔗 Fetching analytics for LinkedIn account:', user.account_id);

        // Get analytics data
        const analytics = await unipileService.getLinkedInAnalytics(user.account_id);

        console.log('✅ Dashboard data retrieved successfully');
        res.json({
            success: true,
            data: {
                user: {
                    id: user.id,
                    email: user.email,
                    first_name: user.first_name,
                    last_name: user.last_name,
                    linkedin_connection_status: user.linkedin_connection_status,
                    account_id: user.account_id
                },
                analytics: analytics,
                isRealTime: true,
                last_sync: new Date().toISOString()
            }
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetDashboardData:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch dashboard data',
            details: error.message
        });
    }
};

/**
 * Track user action (for analytics)
 * POST /api/analytics/track
 */
export const handleTrackAction = async (req: Request, res: Response): Promise<void> => {
    try {
        const actionData = req.body;

        console.log('📊 POST /api/analytics/track - Action:', actionData.action, 'User:', actionData.userId);

        // In a real app, you would save this tracking data to your database
        console.log('📊 Action tracked:', {
            action: actionData.action,
            userId: actionData.userId,
            sessionId: actionData.sessionId,
            timestamp: actionData.timestamp || new Date().toISOString()
        });

        res.json({
            success: true,
            data: {
                tracked: true,
                timestamp: new Date().toISOString()
            }
        });
    } catch (error: any) {
        console.error('❌ Error in handleTrackAction:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to track action',
            details: error.message
        });
    }
};

/**
 * Get onboarding metrics
 * GET /api/analytics/onboarding/metrics
 */
export const handleGetOnboardingMetrics = async (req: Request, res: Response): Promise<void> => {
    try {
        console.log('📊 GET /api/analytics/onboarding/metrics - Request for onboarding metrics');

        // Real onboarding metrics - fetch from database
        // TODO: Implement real metrics from user analytics table
        const metrics = {
            totalSignups: 0,
            completedOnboarding: 0,
            linkedinConnections: 0,
            completionRate: 0,
            averageTimeToComplete: 'N/A',
            dropoffPoints: [
                { step: 'email_verification', dropoff: 0 },
                { step: 'linkedin_connection', dropoff: 0 },
                { step: 'profile_completion', dropoff: 0 }
            ]
        };

        console.log('✅ Onboarding metrics retrieved successfully');
        res.json({
            success: true,
            data: metrics
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetOnboardingMetrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch onboarding metrics',
            details: error.message
        });
    }
};

/**
 * Get user journey
 * GET /api/analytics/journey/:sessionId
 */
export const handleGetUserJourney = async (req: Request, res: Response): Promise<void> => {
    try {
        const { sessionId } = req.params;

        console.log('📊 GET /api/analytics/journey/:sessionId - Request for session:', sessionId);

        if (!sessionId) {
            res.status(400).json({
                success: false,
                error: 'Session ID is required'
            });
            return;
        }

        // Real user journey - fetch from database
        // TODO: Implement real journey tracking from user analytics table
        const journey = {
            sessionId: sessionId,
            startTime: null,
            endTime: null,
            steps: [],
            totalDuration: 'N/A',
            completed: false
        };

        console.log('✅ User journey retrieved successfully');
        res.json({
            success: true,
            data: journey
        });
    } catch (error: any) {
        console.error('❌ Error in handleGetUserJourney:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch user journey',
            details: error.message
        });
    }
};

/**
 * Get comprehensive post details including comments and reactions
 * GET /api/analytics/posts/:postId/comprehensive
 */
export const handleGetComprehensivePostData = async (req: Request, res: Response): Promise<void> => {
    try {
        const { postId } = req.params;
        const { accountId } = req.query;

        console.log(`🔍 GET /api/analytics/posts/${postId}/comprehensive - Request for comprehensive post data`);
        console.log(`📊 Post ID: ${postId}`);
        console.log(`🔗 Account ID: ${accountId}`);

        if (!postId) {
            res.status(400).json({
                success: false,
                error: 'Post ID is required'
            });
            return;
        }

        if (!accountId) {
            res.status(400).json({
                success: false,
                error: 'Account ID is required'
            });
            return;
        }

        // Import the comprehensive post data function
        const { getComprehensivePostData } = await import('../services/unipile/linkedin-posts');
        const { initializeUnipileConfig } = await import('../services/unipile/config');

        // Initialize Unipile configuration
        const unipileConfig = initializeUnipileConfig();

        // Fetch comprehensive post data
        const comprehensiveData = await getComprehensivePostData(
            unipileConfig,
            accountId as string,
            postId
        );

        console.log('✅ Comprehensive post data retrieved successfully');
        res.json({
            success: true,
            data: comprehensiveData,
            timestamp: new Date().toISOString()
        });

    } catch (error: any) {
        console.error('❌ Error retrieving comprehensive post data:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve comprehensive post data',
            details: error.message,
            timestamp: new Date().toISOString()
        });
    }
};
