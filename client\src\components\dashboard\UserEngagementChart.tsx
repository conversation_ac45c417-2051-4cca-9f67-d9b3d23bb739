
import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

// Mock data for user post engagement
const engagementData = [
  {
    name: 'Post 1',
    likes: 86,
    comments: 24,
    month: 'Apr',
    title: 'Product Launch'
  },
  {
    name: 'Post 2',
    likes: 62,
    comments: 18,
    month: 'Mar',
    title: 'Company Award'
  },
  {
    name: 'Post 3',
    likes: 35,
    comments: 8,
    month: 'Mar',
    title: 'Industry Insights'
  },
  {
    name: 'Post 4',
    likes: 73,
    comments: 21,
    month: 'Mar',
    title: 'Team Milestone'
  },
  {
    name: 'Post 5',
    likes: 45,
    comments: 15,
    month: 'Feb',
    title: 'Product Update'
  },
  {
    name: 'Post 6',
    likes: 55,
    comments: 17,
    month: 'Feb',
    title: 'Company Culture'
  },
];

const UserEngagementChart = () => {
  // Chart configuration with different colors for each data series
  const chartConfig = {
    likes: {
      label: "Like<PERSON>",
      theme: {
        light: "#2563eb", // enterprise blue
        dark: "#3b82f6",
      },
    },
    comments: {
      label: "Comments",
      theme: {
        light: "#0d9488", // enterprise teal
        dark: "#14b8a6",
      },
    },
  };

  return (
    <ChartContainer className="h-80" config={chartConfig}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={engagementData} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis 
            dataKey="title" 
            tick={{ fontSize: 12 }} 
            axisLine={false}
            tickLine={false}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12 }}
          />
          <Tooltip 
            content={({ active, payload }) => {
              if (!active || !payload?.length) return null;
              
              return (
                <ChartTooltipContent 
                  className="border-none bg-white shadow-lg dark:bg-gray-950"
                  payload={payload}
                />
              );
            }}
          />
          <Legend wrapperStyle={{ paddingTop: 10 }} />
          <Bar 
            dataKey="likes" 
            name="Likes" 
            fill="var(--color-likes)" 
            radius={[4, 4, 0, 0]}
            barSize={28}
          />
          <Bar 
            dataKey="comments" 
            name="Comments" 
            fill="var(--color-comments)" 
            radius={[4, 4, 0, 0]}
            barSize={28}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};

export default UserEngagementChart;
