
-- Update the employee_posts table to enable proper joins with profiles
-- Make sure the posts can be joined with the profiles table through user_id

-- Add a foreign key constraint between employee_posts.user_id and profiles.id
ALTER TABLE public.employee_posts ADD CONSTRAINT employee_posts_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- Make sure status column exists with proper default value
ALTER TABLE public.employee_posts 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'pending_approval' NOT NULL;

-- Enable realtime for employee_posts
ALTER PUBLICATION supabase_realtime ADD TABLE public.employee_posts;
