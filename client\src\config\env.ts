interface Env {
  // API Configuration
  VITE_API_BASE_URL: string;
  VITE_CLIENT_URL: string;

  // Phyllo Integration
  VITE_PHYLLO_CLIENT_ID: string;
  VITE_PHYLLO_CLIENT_SECRET: string;
  VITE_PHYLLO_API_BASE_URL: string;

  // Unipile Integration
  VITE_UNIPILE_API_URL: string;
  VITE_UNIPILE_ACCOUNT_URL: string;

  // Environment
  VITE_NODE_ENV: string;
}

export const env: Env = {
  // API Configuration
  VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  VITE_CLIENT_URL: import.meta.env.VITE_CLIENT_URL || 'https://localhost:8080',

  // Phyllo Integration
  VITE_PHYLLO_CLIENT_ID: import.meta.env.VITE_PHYLLO_CLIENT_ID || '',
  VITE_PHYLLO_CLIENT_SECRET: import.meta.env.VITE_PHYLLO_CLIENT_SECRET || '',
  VITE_PHYLLO_API_BASE_URL: import.meta.env.VITE_PHYLLO_API_BASE_URL || 'https://api.sandbox.getphyllo.com/v1',

  // Unipile Integration
  VITE_UNIPILE_API_URL: import.meta.env.VITE_UNIPILE_API_URL || 'https://api.unipile.com',
  VITE_UNIPILE_ACCOUNT_URL: import.meta.env.VITE_UNIPILE_ACCOUNT_URL || 'https://account.unipile.com',

  // Environment
  VITE_NODE_ENV: import.meta.env.VITE_NODE_ENV || 'development',
};

// Helper functions for API URLs
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = env.VITE_API_BASE_URL.replace(/\/$/, ''); // Remove trailing slash
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

export const getUnipileUrl = (endpoint: string): string => {
  const baseUrl = env.VITE_UNIPILE_API_URL.replace(/\/$/, '');
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

export const getUnipileAccountUrl = (path: string): string => {
  const baseUrl = env.VITE_UNIPILE_ACCOUNT_URL.replace(/\/$/, '');
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
};

// Export commonly used URLs
export const API_BASE_URL = env.VITE_API_BASE_URL;
export const CLIENT_URL = env.VITE_CLIENT_URL;
export const UNIPILE_API_URL = env.VITE_UNIPILE_API_URL;
export const UNIPILE_ACCOUNT_URL = env.VITE_UNIPILE_ACCOUNT_URL;

// Validate required environment variables in development
if (env.VITE_NODE_ENV === 'development') {
  const requiredEnvVars: (keyof Env)[] = ['VITE_API_BASE_URL', 'VITE_CLIENT_URL'];

  requiredEnvVars.forEach((key) => {
    if (!env[key]) {
      console.warn(`⚠️ Missing environment variable: ${key}. Using fallback value.`);
    }
  });

  // Warn about missing Phyllo credentials
  if (!env.VITE_PHYLLO_CLIENT_ID || !env.VITE_PHYLLO_CLIENT_SECRET) {
    console.warn('⚠️ Phyllo credentials not configured. Some features may not work.');
  }
}