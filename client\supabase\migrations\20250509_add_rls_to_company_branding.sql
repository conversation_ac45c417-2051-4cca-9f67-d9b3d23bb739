
-- Enable Row Level Security
ALTER TABLE IF EXISTS public.company_branding ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select their own company branding
CREATE POLICY "Users can view their own company branding"
  ON public.company_branding
  FOR SELECT
  USING (auth.uid() = company_id);

-- Create policy to allow users to insert their own company branding
CREATE POLICY "Users can insert their own company branding"
  ON public.company_branding
  FOR INSERT
  WITH CHECK (auth.uid() = company_id);

-- Create policy to allow users to update their own company branding
CREATE POLICY "Users can update their own company branding"
  ON public.company_branding
  FOR UPDATE
  USING (auth.uid() = company_id);

-- Create policy to allow users to delete their own company branding
CREATE POLICY "Users can delete their own company branding"
  ON public.company_branding
  FOR DELETE
  USING (auth.uid() = company_id);
