// Background Scheduler Service - Manages periodic data refresh operations
// Implements intelligent scheduling to minimize API usage while keeping data fresh

import { softRefreshService } from './softRefreshService';
import { getUserPosts } from './postStorageService';

export interface SchedulerConfig {
    enabled: boolean;
    checkInterval: number;        // Minutes between scheduler runs
    maxUsersPerCycle: number;     // Max users to process per cycle
    quietHours: {                // Hours to reduce activity (24h format)
        start: number;
        end: number;
    };
    priorityBoost: {             // Boost refresh for certain conditions
        newUsers: boolean;        // Prioritize users with no data
        activeUsers: boolean;     // Prioritize recently active users
        staleData: boolean;       // Prioritize very old data
    };
}

export const DEFAULT_SCHEDULER_CONFIG: SchedulerConfig = {
    enabled: true,
    checkInterval: 15,           // Check every 15 minutes
    maxUsersPerCycle: 5,         // Process max 5 users per cycle
    quietHours: {
        start: 23,               // 11 PM
        end: 6                   // 6 AM
    },
    priorityBoost: {
        newUsers: true,
        activeUsers: true,
        staleData: true
    }
};

export class BackgroundScheduler {
    private config: SchedulerConfig;
    private isRunning: boolean = false;
    private intervalId: NodeJS.Timeout | null = null;
    private stats = {
        cyclesRun: 0,
        usersProcessed: 0,
        successfulRefreshes: 0,
        failedRefreshes: 0,
        apiCallsUsed: 0,
        lastRunAt: null as Date | null,
        nextRunAt: null as Date | null
    };

    constructor(config: SchedulerConfig = DEFAULT_SCHEDULER_CONFIG) {
        this.config = config;
    }

    /**
     * Start the background scheduler
     */
    start(): void {
        if (this.isRunning) {
            console.log('⚠️ Background scheduler already running');
            return;
        }

        if (!this.config.enabled) {
            console.log('📴 Background scheduler disabled in config');
            return;
        }

        console.log(`🚀 Starting background scheduler (${this.config.checkInterval}min intervals)`);
        
        this.isRunning = true;
        this.scheduleNextRun();
    }

    /**
     * Stop the background scheduler
     */
    stop(): void {
        if (!this.isRunning) {
            console.log('⚠️ Background scheduler not running');
            return;
        }

        console.log('🛑 Stopping background scheduler');
        
        if (this.intervalId) {
            clearTimeout(this.intervalId);
            this.intervalId = null;
        }
        
        this.isRunning = false;
        this.stats.nextRunAt = null;
    }

    /**
     * Schedule the next scheduler run
     */
    private scheduleNextRun(): void {
        if (!this.isRunning) return;

        const nextRunDelay = this.calculateNextRunDelay();
        this.stats.nextRunAt = new Date(Date.now() + nextRunDelay);

        this.intervalId = setTimeout(async () => {
            await this.runSchedulerCycle();
            this.scheduleNextRun(); // Schedule next run after completion
        }, nextRunDelay);

        console.log(`⏰ Next scheduler run at: ${this.stats.nextRunAt.toISOString()}`);
    }

    /**
     * Calculate delay until next run based on current conditions
     */
    private calculateNextRunDelay(): number {
        const baseInterval = this.config.checkInterval * 60 * 1000; // Convert to milliseconds
        
        // Check if we're in quiet hours
        if (this.isQuietHours()) {
            console.log('🌙 Quiet hours detected, extending interval');
            return baseInterval * 2; // Double the interval during quiet hours
        }

        // Check system load (API usage, active syncs)
        const refreshStatus = softRefreshService.getRefreshStatus();
        
        if (refreshStatus.isInRateLimitBuffer) {
            console.log('⚠️ Rate limit buffer active, extending interval');
            return baseInterval * 3; // Triple interval if rate limited
        }

        if (refreshStatus.activeSyncs >= 3) {
            console.log('🔄 High sync activity, slightly extending interval');
            return baseInterval * 1.5; // Extend interval if many syncs active
        }

        return baseInterval;
    }

    /**
     * Check if current time is in quiet hours
     */
    private isQuietHours(): boolean {
        const now = new Date();
        const currentHour = now.getHours();
        
        const { start, end } = this.config.quietHours;
        
        // Handle overnight quiet hours (e.g., 23:00 to 06:00)
        if (start > end) {
            return currentHour >= start || currentHour < end;
        }
        
        // Handle same-day quiet hours (e.g., 02:00 to 06:00)
        return currentHour >= start && currentHour < end;
    }

    /**
     * Run a single scheduler cycle
     */
    private async runSchedulerCycle(): Promise<void> {
        try {
            console.log(`🔄 Running scheduler cycle #${this.stats.cyclesRun + 1}`);
            this.stats.lastRunAt = new Date();
            this.stats.cyclesRun++;

            // Get list of users who might need refresh
            const candidateUsers = await this.getCandidateUsers();
            
            if (candidateUsers.length === 0) {
                console.log('✅ No users need refresh at this time');
                return;
            }

            console.log(`📋 Found ${candidateUsers.length} candidate users for refresh`);

            // Prioritize and limit users for this cycle
            const usersToProcess = this.prioritizeUsers(candidateUsers)
                .slice(0, this.config.maxUsersPerCycle);

            console.log(`🎯 Processing ${usersToProcess.length} users this cycle`);

            // Process each user
            for (const userId of usersToProcess) {
                try {
                    console.log(`🔄 Processing user: ${userId}`);
                    
                    const refreshResult = await softRefreshService.performSoftRefresh(userId);
                    
                    this.stats.usersProcessed++;
                    this.stats.apiCallsUsed += refreshResult.apiCallsUsed;
                    
                    if (refreshResult.success) {
                        this.stats.successfulRefreshes++;
                        console.log(`✅ User ${userId}: ${refreshResult.refreshType} refresh completed (${refreshResult.postsUpdated} posts updated)`);
                    } else {
                        this.stats.failedRefreshes++;
                        console.log(`❌ User ${userId}: Refresh failed - ${refreshResult.error}`);
                    }

                    // Small delay between users to be gentle on the API
                    await new Promise(resolve => setTimeout(resolve, 1000));

                } catch (error: any) {
                    console.error(`❌ Error processing user ${userId}:`, error);
                    this.stats.failedRefreshes++;
                }
            }

            console.log(`✅ Scheduler cycle completed: ${usersToProcess.length} users processed`);

        } catch (error: any) {
            console.error('❌ Error in scheduler cycle:', error);
        }
    }

    /**
     * Get list of users who are candidates for refresh
     */
    private async getCandidateUsers(): Promise<string[]> {
        // In a real implementation, this would query your user database
        // For now, we'll return a mock list based on your existing patterns
        
        // This should be replaced with actual database query like:
        // SELECT id FROM users WHERE account_id IS NOT NULL
        
        const mockUsers = [
            'user-1', 'user-2', 'user-3', 'user-4', 'user-5',
            'user-6', 'user-7', 'user-8', 'user-9', 'user-10'
        ];

        // Filter users who actually need refresh
        const candidateUsers: string[] = [];
        
        for (const userId of mockUsers) {
            try {
                const shouldRefresh = await softRefreshService.shouldRefreshUser(userId);
                if (shouldRefresh.shouldRefresh) {
                    candidateUsers.push(userId);
                }
            } catch (error) {
                // Skip users with errors
                continue;
            }
        }

        return candidateUsers;
    }

    /**
     * Prioritize users based on refresh urgency and configuration
     */
    private prioritizeUsers(userIds: string[]): string[] {
        // For now, return as-is. In a real implementation, this would:
        // 1. Check each user's data age and activity
        // 2. Apply priority boosts based on config
        // 3. Sort by priority score
        
        return userIds;
    }

    /**
     * Get scheduler statistics
     */
    getStats(): typeof this.stats & { isRunning: boolean; config: SchedulerConfig } {
        return {
            ...this.stats,
            isRunning: this.isRunning,
            config: this.config
        };
    }

    /**
     * Update scheduler configuration
     */
    updateConfig(newConfig: Partial<SchedulerConfig>): void {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ Scheduler configuration updated:', newConfig);
        
        // Restart if running to apply new config
        if (this.isRunning) {
            this.stop();
            this.start();
        }
    }

    /**
     * Force run a scheduler cycle (for testing/manual trigger)
     */
    async forceRun(): Promise<void> {
        if (!this.isRunning) {
            console.log('⚠️ Scheduler not running, starting temporary cycle');
        }
        
        console.log('🔧 Force running scheduler cycle');
        await this.runSchedulerCycle();
    }
}

// Export singleton instance
export const backgroundScheduler = new BackgroundScheduler();
