
import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface LinkedInPostExample {
  id: string;
  subject: string;
  content: string;
}

interface LinkedInExamplesListProps {
  examples: LinkedInPostExample[];
  onAddExample: () => void;
  onEditExample: (example: LinkedInPostExample) => void;
  onDeleteExample: (id: string) => void;
}

const LinkedInExamplesList = ({
  examples,
  onAddExample,
  onEditExample,
  onDeleteExample
}: LinkedInExamplesListProps) => {
  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Button 
          size="sm" 
          onClick={onAddExample}
          className="bg-enterprise-blue hover:bg-enterprise-blue/90"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Example
        </Button>
      </div>
        
      <div className="space-y-4 mt-2">
        {examples.length === 0 ? (
          <div className="text-center p-6 border rounded-md border-dashed">
            <p className="text-muted-foreground">No examples added yet</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2"
              onClick={onAddExample}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Your First Example
            </Button>
          </div>
        ) : (
          examples.map((example) => (
            <div key={example.id} className="border rounded-md p-4 space-y-2">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium">{example.subject}</h4>
                </div>
                <div className="flex space-x-2">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => onEditExample(example)}
                  >
                    Edit
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => onDeleteExample(example.id)}
                    className="text-red-500 hover:text-red-600 hover:bg-red-50"
                  >
                    Delete
                  </Button>
                </div>
              </div>
              <div className="text-sm text-muted-foreground whitespace-pre-line">
                {example.content.length > 150 
                  ? `${example.content.substring(0, 150)}...` 
                  : example.content}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default LinkedInExamplesList;
