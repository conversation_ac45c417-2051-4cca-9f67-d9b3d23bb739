// Auth-related types

export interface UserSignupRequest {
  linkedin_email: string;
  linkedin_password: string;
  use_same_credentials: boolean;
  email?: string;
  password?: string;
  first_name: string;
  last_name: string;
  linkedin_url: string;
}

export type UserRole = 'admin' | 'user';

export type LinkedInConnectionStatus = 'connected' | 'disconnected' | 'pending' | 'verification_required';