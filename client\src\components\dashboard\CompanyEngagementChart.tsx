
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

// Mock data for company engagement
const engagementData = [
  {
    name: 'Week 1',
    likes: 346,
    comments: 128,
    shares: 82
  },
  {
    name: 'Week 2',
    likes: 425,
    comments: 156,
    shares: 96
  },
  {
    name: 'Week 3',
    likes: 378,
    comments: 142,
    shares: 88
  },
  {
    name: 'Week 4',
    likes: 512,
    comments: 187,
    shares: 112
  },
  {
    name: 'Week 5',
    likes: 468,
    comments: 165,
    shares: 103
  },
  {
    name: 'Week 6',
    likes: 542,
    comments: 198,
    shares: 124
  },
];

const CompanyEngagementChart = () => {
  // Chart configuration with different colors for each data series
  const chartConfig = {
    likes: {
      label: "Likes",
      theme: {
        light: "#2563eb", // enterprise blue
        dark: "#3b82f6",
      },
    },
    comments: {
      label: "Comments",
      theme: {
        light: "#0d9488", // enterprise teal
        dark: "#14b8a6",
      },
    },
    shares: {
      label: "Shares",
      theme: {
        light: "#7c3aed", // purple
        dark: "#8b5cf6",
      },
    },
  };

  return (
    <ChartContainer className="h-80" config={chartConfig}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={engagementData} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis 
            dataKey="name" 
            tick={{ fontSize: 12 }} 
            axisLine={false}
            tickLine={false}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12 }}
          />
          <Tooltip 
            content={({ active, payload }) => {
              if (!active || !payload?.length) return null;
              
              return (
                <ChartTooltipContent 
                  className="border-none bg-white shadow-lg dark:bg-gray-950"
                  payload={payload}
                />
              );
            }}
          />
          <Legend wrapperStyle={{ paddingTop: 10 }} />
          <Bar 
            dataKey="likes" 
            name="Likes" 
            fill="var(--color-likes)" 
            radius={[4, 4, 0, 0]}
            barSize={20}
          />
          <Bar 
            dataKey="comments" 
            name="Comments" 
            fill="var(--color-comments)" 
            radius={[4, 4, 0, 0]}
            barSize={20}
          />
          <Bar 
            dataKey="shares" 
            name="Shares" 
            fill="var(--color-shares)" 
            radius={[4, 4, 0, 0]}
            barSize={20}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};

export default CompanyEngagementChart;
