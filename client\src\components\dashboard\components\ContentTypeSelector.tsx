
import React from 'react';
import { Building, User } from 'lucide-react';
import { useContent } from '@/context/ContentContext';

const ContentTypeSelector = () => {
  const { activeBox, setActiveBox } = useContent();

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div 
        className={`transition-all duration-300 rounded-md p-4 border-2 cursor-pointer hover:shadow-md ${
          activeBox === 'company' 
            ? 'border-enterprise-blue bg-gradient-to-br from-blue-50 to-blue-100/80 shadow-md' 
            : 'border-transparent hover:border-gray-200 hover:bg-gray-50/50'
        }`}
        onClick={() => setActiveBox('company')}
      >
        <div className="flex items-center gap-2">
          <div className="bg-enterprise-blue/10 p-2 rounded-full">
            <Building className="h-5 w-5 text-enterprise-blue" />
          </div>
          <h2 className="text-sm font-semibold">Build Our Brand</h2>
        </div>
        <p className="text-xs text-muted-foreground mt-2 ml-9">
          Company updates & milestones
        </p>
      </div>

      <div 
        className={`transition-all duration-300 rounded-md p-4 border-2 cursor-pointer hover:shadow-md ${
          activeBox === 'personal' 
            ? 'border-enterprise-teal bg-gradient-to-br from-teal-50 to-teal-100/80 shadow-md' 
            : 'border-transparent hover:border-gray-200 hover:bg-gray-50/50'
        }`}
        onClick={() => setActiveBox('personal')}
      >
        <div className="flex items-center gap-2">
          <div className="bg-enterprise-teal/10 p-2 rounded-full">
            <User className="h-5 w-5 text-enterprise-teal" />
          </div>
          <h2 className="text-sm font-semibold">Build my Brand</h2>
        </div>
        <p className="text-xs text-muted-foreground mt-2 ml-9">
          Personal achievements & insights
        </p>
      </div>
    </div>
  );
};

export default ContentTypeSelector;
