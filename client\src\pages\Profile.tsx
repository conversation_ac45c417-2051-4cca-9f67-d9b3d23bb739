
import React from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import ProfileHeader from '@/components/profile/ProfileHeader';
import PersonalInfoForm from '@/components/profile/PersonalInfoForm';
import AddressForm from '@/components/profile/AddressForm';

const Profile = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">My Profile</h1>
          <p className="text-enterprise-gray-600">Manage your profile and preferences</p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1">
            <ProfileHeader 
              name="<PERSON> Smith"
              initials="JS"
              jobTitle="Content Creator"
              company="Acme Corporation"
            />
          </div>
          
          <div className="lg:col-span-3">
            <PersonalInfoForm />
            <AddressForm />
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default Profile;
