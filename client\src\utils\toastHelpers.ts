import { toast } from 'sonner';

/**
 * Show email verification toast with custom styling and longer duration
 */
export const showEmailVerificationToast = () => {
  return toast.info(
    "📧 Please check your email to verify your account before logging in.",
    {
      duration: 6000, // 6 seconds
      position: 'top-center',
      style: {
        background: '#3b82f6',
        color: 'white',
        border: '1px solid #2563eb',
        fontSize: '16px',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 10px 25px rgba(59, 130, 246, 0.3)',
      },
      className: 'email-verification-toast',
    }
  );
};

/**
 * Show account creation success toast
 */
export const showAccountCreatedToast = () => {
  return toast.success("Account created successfully!", {
    duration: 2000,
    style: {
      background: '#10b981',
      color: 'white',
      fontSize: '16px',
      padding: '12px',
    },
  });
};

/**
 * Show LinkedIn verification success toast
 */
export const showLinkedInVerificationToast = () => {
  return toast.success("LinkedIn verification completed successfully!", {
    duration: 2000,
    style: {
      background: '#0077b5', // LinkedIn blue
      color: 'white',
      fontSize: '16px',
      padding: '12px',
    },
  });
};

/**
 * Show proceeding with account creation toast
 */
export const showProceedingToast = () => {
  return toast.success("Proceeding with account creation...", {
    duration: 3000,
    style: {
      background: '#8b5cf6',
      color: 'white',
      fontSize: '15px',
      padding: '12px',
    },
  });
};

/**
 * Complete signup flow with proper toast sequence
 */
export const showSignupCompletionToasts = (navigate: (path: string) => void) => {
  // 1. Show account created success
  showAccountCreatedToast();
  
  // 2. Show email verification message after 1 second
  setTimeout(() => {
    showEmailVerificationToast();
  }, 1000);
  
  // 3. Redirect to login after 4 seconds
  setTimeout(() => {
    navigate("/login");
  }, 4000);
};

/**
 * Complete checkpoint verification with proper toast sequence
 */
export const showCheckpointCompletionToasts = () => {
  // 1. Show LinkedIn verification success
  showLinkedInVerificationToast();
  
  // 2. Show proceeding message after 500ms
  setTimeout(() => {
    showProceedingToast();
  }, 500);
};
