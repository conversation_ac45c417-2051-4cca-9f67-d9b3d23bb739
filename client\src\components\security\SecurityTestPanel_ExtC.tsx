import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { 
  runComprehensiveSecurityCheck, 
  displaySecurityCheckResults,
  monitorLinkedInPasswordStorage,
  SecurityCheckResult 
} from '@/utils/securityVerification';
import { Shield, AlertTriangle, CheckCircle, Eye, EyeOff } from 'lucide-react';

const SecurityTestPanel: React.FC = () => {
  const { user } = useAuth();
  const [securityResults, setSecurityResults] = useState<SecurityCheckResult[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [showUserData, setShowUserData] = useState(false);

  useEffect(() => {
    // Run initial security check
    runSecurityCheck();
  }, [user]);

  const runSecurityCheck = () => {
    const results = runComprehensiveSecurityCheck(user);
    setSecurityResults(results);
    displaySecurityCheckResults(results);
  };

  const startMonitoring = () => {
    monitorLinkedInPasswordStorage();
    setIsMonitoring(true);
  };

  const stopMonitoring = () => {
    // Note: In a real implementation, you'd need to restore original methods
    setIsMonitoring(false);
  };

  const clearAllStorage = () => {
    localStorage.clear();
    sessionStorage.clear();
    console.log("🧹 All storage cleared");
    runSecurityCheck();
  };

  const getResultIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <AlertTriangle className="h-4 w-4 text-red-600" />
    );
  };

  const getResultBadge = (passed: boolean) => {
    return (
      <Badge variant={passed ? "default" : "destructive"}>
        {passed ? "PASS" : "FAIL"}
      </Badge>
    );
  };

  const allChecksPassed = securityResults.every(result => result.passed);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            LinkedIn Password Security Test Panel
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Overall Status */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {getResultIcon(allChecksPassed)}
              <span className="font-semibold">
                Overall Security Status
              </span>
            </div>
            {getResultBadge(allChecksPassed)}
          </div>

          {/* Security Check Results */}
          <div className="space-y-3">
            <h3 className="font-semibold text-lg">Security Check Results</h3>
            {securityResults.map((result, index) => (
              <div key={index} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getResultIcon(result.passed)}
                    <span className="text-sm">{result.message}</span>
                  </div>
                  {getResultBadge(result.passed)}
                </div>
                {result.details && result.details.length > 0 && (
                  <div className="ml-6 space-y-1">
                    {result.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="text-xs text-gray-600">
                        • {detail}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button onClick={runSecurityCheck} variant="outline">
              🔍 Run Security Check
            </Button>
            
            <Button 
              onClick={isMonitoring ? stopMonitoring : startMonitoring}
              variant={isMonitoring ? "destructive" : "default"}
            >
              {isMonitoring ? "🛑 Stop Monitoring" : "👁️ Start Monitoring"}
            </Button>
            
            <Button onClick={clearAllStorage} variant="outline">
              🧹 Clear All Storage
            </Button>
          </div>

          {/* User Data Inspection */}
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">User Data Inspection</h3>
              <Button
                onClick={() => setShowUserData(!showUserData)}
                variant="outline"
                size="sm"
              >
                {showUserData ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showUserData ? "Hide" : "Show"} User Data
              </Button>
            </div>
            
            {showUserData && (
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-xs overflow-auto max-h-96">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* Security Guidelines */}
          <div className="border-t pt-4">
            <h3 className="font-semibold mb-3">Security Guidelines</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <span>LinkedIn passwords should NEVER be stored in localStorage</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <span>LinkedIn passwords should NEVER be stored in sessionStorage</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <span>LinkedIn passwords should NEVER be sent to Supabase</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <span>Only use LinkedIn passwords for verification, then discard</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <span>Store only linkedin_account_id and linkedin_email</span>
              </div>
            </div>
          </div>

          {/* Test Instructions */}
          <div className="border-t pt-4">
            <h3 className="font-semibold mb-3">How to Test</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <div>1. <strong>Start Monitoring:</strong> Click "Start Monitoring" to detect storage attempts</div>
              <div>2. <strong>Sign Up:</strong> Go through the signup process with LinkedIn credentials</div>
              <div>3. <strong>Check Results:</strong> Return here and click "Run Security Check"</div>
              <div>4. <strong>Inspect Data:</strong> Use "Show User Data" to manually inspect user object</div>
              <div>5. <strong>Verify Console:</strong> Check browser console for security alerts</div>
            </div>
          </div>

          {/* Current Status */}
          <div className="border-t pt-4">
            <h3 className="font-semibold mb-3">Current Status</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">User Logged In:</span> 
                <Badge variant={user ? "default" : "secondary"} className="ml-2">
                  {user ? "Yes" : "No"}
                </Badge>
              </div>
              <div>
                <span className="font-medium">Monitoring Active:</span> 
                <Badge variant={isMonitoring ? "default" : "secondary"} className="ml-2">
                  {isMonitoring ? "Yes" : "No"}
                </Badge>
              </div>
              <div>
                <span className="font-medium">Security Checks:</span> 
                <Badge variant={allChecksPassed ? "default" : "destructive"} className="ml-2">
                  {securityResults.length > 0 ? (allChecksPassed ? "All Pass" : "Some Fail") : "Not Run"}
                </Badge>
              </div>
              <div>
                <span className="font-medium">Last Check:</span> 
                <span className="ml-2 text-gray-500">
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecurityTestPanel;
