
import React from 'react';
import { Check, Facebook, Linkedin, Instagram, Video, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';

interface PostTemplateSelectorProps {
  value: string;
  onValueChange: (value: string) => void;
}

// Define a proper type for the icon component
type IconType = React.ComponentType<{ className?: string }>;

const templates = [
  {
    id: 'linkedin-update',
    name: 'LinkedIn Update',
    description: 'Professional update about company news or achievements',
    icon: Linkedin,
    iconColor: 'text-[#0A66C2]',
    comingSoon: false
  },
  {
    id: 'twitter-post',
    name: 'X.com Post',
    description: 'Concise announcement with hashtags',
    icon: () => (
      <img 
        src="/lovable-uploads/df6d2122-288a-4d42-89fd-66ad79121d05.png" 
        alt="X.com Logo" 
        className="h-5 w-5"
      />
    ),
    iconColor: 'text-black',
    comingSoon: true
  },
  {
    id: 'facebook-update',
    name: 'Facebook Update',
    description: 'Detailed company news or milestone',
    icon: Facebook,
    iconColor: 'text-[#1877F2]',
    comingSoon: true
  },
  {
    id: 'instagram-post',
    name: 'Instagram Post',
    description: 'Visual content with engaging captions',
    icon: Instagram,
    iconColor: 'text-[#E4405F]',
    comingSoon: true
  },
  {
    id: 'tiktok-video',
    name: 'TikTok Video',
    description: 'Short-form video content for trends',
    icon: () => (
      <img 
        src="/lovable-uploads/d8f353ec-e207-4d42-89fd-66ad79121d05.png" 
        alt="TikTok Logo" 
        className="h-5 w-5"
      />
    ),
    iconColor: 'text-black',
    comingSoon: true
  }
];

const PostTemplateSelector = ({ value, onValueChange }: PostTemplateSelectorProps) => {
  return (
    <div className="space-y-3 max-w-3xl">
      {templates.map((template) => {
        const isSelected = template.id === value;
        const IconComponent = template.icon;
        
        return (
          <div
            key={template.id}
            className={cn(
              "relative cursor-pointer rounded-md border p-3 hover:border-enterprise-blue/50 transition-all flex items-center",
              isSelected && !template.comingSoon ? "border-2 border-enterprise-blue bg-enterprise-lightBlue/20" : "bg-white",
              template.comingSoon ? "opacity-70 cursor-not-allowed" : ""
            )}
            onClick={() => !template.comingSoon && onValueChange(template.id)}
          >
            {isSelected && !template.comingSoon && (
              <div className="absolute top-1/2 -translate-y-1/2 right-3 h-5 w-5 rounded-full bg-enterprise-blue flex items-center justify-center">
                <Check className="h-3 w-3 text-white" />
              </div>
            )}
            
            <div className="flex items-center w-full pr-8">
              <div className="flex-shrink-0 mr-3">
                <IconComponent className={cn("h-5 w-5", template.iconColor)} />
              </div>
              <div className="flex-grow min-w-0">
                <div className="flex items-center gap-1">
                  <h4 className="font-medium text-sm whitespace-nowrap">{template.name}</h4>
                  {template.comingSoon && (
                    <span className="text-[10px] text-gray-500 whitespace-nowrap">(Coming Soon)</span>
                  )}
                </div>
                <p className="text-xs text-muted-foreground truncate">{template.description}</p>
              </div>
              {template.comingSoon && (
                <Lock className="text-gray-400 h-4 w-4 ml-1 flex-shrink-0" />
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PostTemplateSelector;
