
import React from 'react';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Check, AlertCircle } from 'lucide-react';

interface ContentGuidelinesSectionProps {
  dosValue: string;
  dontsValue: string;
  onDosChange: (value: string) => void;
  onDontsChange: (value: string) => void;
}

const ContentGuidelinesSection = ({
  dosValue,
  dontsValue,
  onDosChange,
  onDontsChange
}: ContentGuidelinesSectionProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-base font-medium">Content Guidelines</h3>
      <div className="space-y-4">
        <div className="flex flex-col space-y-2">
          <div className="flex items-center space-x-3">
            <Check className="text-green-500 mt-0.5" size={16} />
            <Label htmlFor="guideline-do">Do</Label>
          </div>
          <Textarea 
            id="guideline-do" 
            value={dosValue}
            onChange={(e) => onDosChange(e.target.value)}
            rows={2}
            className="w-full"
          />
        </div>
        
        <div className="flex flex-col space-y-2">
          <div className="flex items-center space-x-3">
            <AlertCircle className="text-red-500 mt-0.5" size={16} />
            <Label htmlFor="guideline-dont">Don't</Label>
          </div>
          <Textarea 
            id="guideline-dont" 
            value={dontsValue}
            onChange={(e) => onDontsChange(e.target.value)}
            rows={2}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
};

export default ContentGuidelinesSection;
