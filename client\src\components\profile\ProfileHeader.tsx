
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Building } from 'lucide-react';

type ProfileHeaderProps = {
  name: string;
  initials: string;
  jobTitle: string;
  company: string;
  avatarUrl?: string;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  name,
  initials,
  jobTitle,
  company,
  avatarUrl
}) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col items-center">
          <Avatar className="h-24 w-24 mb-4">
            <AvatarImage src={avatarUrl || ""} />
            <AvatarFallback className="bg-enterprise-blue text-white text-xl">{initials}</AvatarFallback>
          </Avatar>
          <h2 className="text-xl font-semibold">{name}</h2>
          <p className="text-enterprise-gray-600">{jobTitle}</p>
          <p className="text-sm text-enterprise-gray-600 flex items-center mt-1">
            <Building size={14} className="mr-1" />
            {company}
          </p>
          <Button variant="outline" size="sm" className="mt-4 w-full">
            Change Photo
          </Button>
        </div>
        
        <div className="mt-6 pt-6 border-t">
          <h3 className="text-sm font-medium mb-3">Activity Stats</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs text-enterprise-gray-600">Content Created</span>
              <span className="text-xs font-medium">32</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs text-enterprise-gray-600">Posts Published</span>
              <span className="text-xs font-medium">18</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs text-enterprise-gray-600">Approvals Pending</span>
              <span className="text-xs font-medium">3</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileHeader;
