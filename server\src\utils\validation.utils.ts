/**
 * Validation Utility Functions
 * Pure functions for data validation
 */

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

/**
 * Validate URL format
 */
export const isValidUrl = (url: string): boolean => {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
};

/**
 * Validate LinkedIn URL format
 */
export const isValidLinkedInUrl = (url: string): boolean => {
    if (!isValidUrl(url)) return false;
    return url.includes('linkedin.com/in/');
};

/**
 * Validate UUID format
 */
export const isValidUuid = (uuid: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
};

/**
 * Validate string length
 */
export const isValidLength = (
    str: string,
    minLength: number = 0,
    maxLength: number = Infinity
): boolean => {
    return str.length >= minLength && str.length <= maxLength;
};

/**
 * Validate required string field
 */
export const isValidRequiredString = (
    value: any,
    minLength: number = 1,
    maxLength: number = 255
): boolean => {
    return typeof value === 'string' && 
           value.trim().length >= minLength && 
           value.trim().length <= maxLength;
};

/**
 * Validate optional string field
 */
export const isValidOptionalString = (
    value: any,
    maxLength: number = 255
): boolean => {
    return value === undefined || 
           value === null || 
           (typeof value === 'string' && value.length <= maxLength);
};

/**
 * Validate user signup data
 */
export interface UserSignupValidation {
    isValid: boolean;
    errors: string[];
}

export const validateUserSignup = (data: any): UserSignupValidation => {
    const errors: string[] = [];

    // Email validation
    if (!data.email) {
        errors.push('Email is required');
    } else if (!isValidEmail(data.email)) {
        errors.push('Invalid email format');
    }

    // First name validation
    if (!isValidRequiredString(data.first_name, 1, 50)) {
        errors.push('First name is required and must be 1-50 characters');
    }

    // Last name validation
    if (!isValidRequiredString(data.last_name, 1, 50)) {
        errors.push('Last name is required and must be 1-50 characters');
    }

    // LinkedIn URL validation (optional)
    if (data.linkedin_url && !isValidLinkedInUrl(data.linkedin_url)) {
        errors.push('Invalid LinkedIn URL format');
    }

    // LinkedIn email validation (optional)
    if (data.linkedin_email && !isValidEmail(data.linkedin_email)) {
        errors.push('Invalid LinkedIn email format');
    }

    // Role validation (optional)
    if (data.role && !['user', 'admin', 'manager'].includes(data.role)) {
        errors.push('Invalid role. Must be user, admin, or manager');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Validate analytics request data
 */
export const validateAnalyticsRequest = (data: any): UserSignupValidation => {
    const errors: string[] = [];

    // User ID validation
    if (!data.userId) {
        errors.push('User ID is required');
    } else if (!isValidUuid(data.userId)) {
        errors.push('Invalid User ID format');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Validate LinkedIn account data
 */
export const validateLinkedInAccount = (data: any): UserSignupValidation => {
    const errors: string[] = [];

    // Account ID validation
    if (!data.accountId) {
        errors.push('LinkedIn account ID is required');
    } else if (!isValidRequiredString(data.accountId, 1, 100)) {
        errors.push('Invalid LinkedIn account ID format');
    }

    // Email validation (optional)
    if (data.email && !isValidEmail(data.email)) {
        errors.push('Invalid email format');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Validate pagination parameters
 */
export interface PaginationParams {
    limit: number;
    offset: number;
    cursor?: string;
}

export const validatePagination = (query: any): PaginationParams => {
    const limit = Math.min(Math.max(parseInt(query.limit) || 50, 1), 100);
    const offset = Math.max(parseInt(query.offset) || 0, 0);
    const cursor = query.cursor || undefined;

    return { limit, offset, cursor };
};

/**
 * Sanitize string input
 */
export const sanitizeString = (input: string): string => {
    return input.trim().replace(/[<>]/g, '');
};

/**
 * Sanitize object with string values
 */
export const sanitizeObject = (obj: Record<string, any>): Record<string, any> => {
    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string') {
            sanitized[key] = sanitizeString(value);
        } else {
            sanitized[key] = value;
        }
    }
    
    return sanitized;
};

/**
 * Check if value is empty
 */
export const isEmpty = (value: any): boolean => {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim().length === 0;
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
};

/**
 * Validate date string
 */
export const isValidDate = (dateString: string): boolean => {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
};

/**
 * Validate numeric range
 */
export const isInRange = (
    value: number,
    min: number = -Infinity,
    max: number = Infinity
): boolean => {
    return value >= min && value <= max;
};
