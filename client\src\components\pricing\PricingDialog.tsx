
import * as React from "react";
import {
  Di<PERSON>,
  Dialog<PERSON>ontent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Check } from "lucide-react";

export function PricingDialog({ trigger }: { trigger: React.ReactNode }) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <div className="text-center space-y-4 mb-8">
          <h2 className="text-3xl font-semibold">Simple, Transparent Pricing</h2>
          <p className="text-muted-foreground">Empower your team or yourself to grow on LinkedIn. Try free for 7 days!</p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Individual Plan */}
          <Card className="relative overflow-hidden">
            <CardHeader className="space-y-1">
              <h3 className="text-2xl font-semibold">Individual Plan</h3>
              <div className="text-3xl font-bold">$20<span className="text-lg font-normal text-muted-foreground">/month</span></div>
              <div className="absolute top-4 right-4 bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium">
                7-day free trial
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <ul className="space-y-2">
                {[
                  "AI-powered LinkedIn post generation",
                  "Personal analytics dashboard",
                  "Access to LinkedIn best practices library"
                ].map((feature) => (
                  <li key={feature} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-primary" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button className="w-full">Start Free Trial</Button>
            </CardFooter>
          </Card>

          {/* Team Plan */}
          <Card className="relative overflow-hidden border-primary">
            <CardHeader className="space-y-1">
              <h3 className="text-2xl font-semibold">Team Plan</h3>
              <div className="text-3xl font-bold">$20<span className="text-lg font-normal text-muted-foreground">/user/month</span></div>
              <p className="text-sm text-muted-foreground">Setup Fee: $500 one-time</p>
              <div className="absolute top-4 right-4 bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium">
                7-day free trial
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <ul className="space-y-2">
                {[
                  "Everything in Individual Plan",
                  "Admin dashboard to monitor team performance",
                  "Centralized billing",
                  "Dedicated onboarding support",
                  "Priority support"
                ].map((feature) => (
                  <li key={feature} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-primary" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              <p className="text-sm text-muted-foreground italic">Custom onboarding available for larger teams</p>
            </CardContent>
            <CardFooter>
              <Button className="w-full" variant="default">Start Free Trial</Button>
            </CardFooter>
          </Card>
        </div>

        <div className="mt-8">
          <h3 className="text-xl font-semibold mb-4">Frequently Asked Questions</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">What happens after my free trial?</h4>
              <p className="text-muted-foreground">After your 7-day trial, your selected plan will begin and you'll be billed accordingly. You can cancel anytime before the trial ends.</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Can I add more users later?</h4>
              <p className="text-muted-foreground">Yes! You can add more users to your team plan at any time. Each new user gets their own 7-day trial.</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Is the setup fee refundable?</h4>
              <p className="text-muted-foreground">The setup fee covers initial onboarding and team configuration. It is non-refundable once the setup process has begun.</p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
