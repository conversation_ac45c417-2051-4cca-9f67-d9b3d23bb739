
import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useContent } from '@/context/ContentContext';

const ContentForm = () => {
  const {
    contentIdea,
    contentTone,
    activeBox,
    setContentIdea,
    setContentTone,
  } = useContent();

  const getPlaceholderText = () => {
    if (activeBox === 'company') {
      return "Share details about a company achievement, product launch, award, or milestone...";
    } else if (activeBox === 'personal') {
      return "Share your personal achievement, insight, or professional growth story...";
    }
    return "Click on one of the options above to start creating your LinkedIn post...";
  };
  
  const borderColor = activeBox === 'company' ? 'focus:border-enterprise-blue' : 'focus:border-enterprise-teal';

  return (
    <div className="space-y-4 animate-fade-in">
      <Textarea
        id="content-idea"
        placeholder={getPlaceholderText()}
        value={contentIdea}
        onChange={(e) => setContentIdea(e.target.value)}
        className={`min-h-[120px] bg-white/70 backdrop-blur-sm border border-gray-200 ${borderColor} transition-all`}
        autoFocus={!!activeBox}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="content-tone" className="text-sm font-medium">
            Tone of voice
          </label>
          <Select value={contentTone} onValueChange={setContentTone}>
            <SelectTrigger className="w-full bg-white/70 backdrop-blur-sm border border-gray-200">
              <SelectValue placeholder="Select tone" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Tone</SelectLabel>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="casual">Casual</SelectItem>
                <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
                <SelectItem value="informative">Informative</SelectItem>
                <SelectItem value="authoritative">Authoritative</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <label htmlFor="content-keywords" className="text-sm font-medium">
            Keywords to include (optional)
          </label>
          <Input 
            id="content-keywords" 
            placeholder="innovation, teamwork, excellence..."
            className="bg-white/70 backdrop-blur-sm border border-gray-200"
          />
        </div>
      </div>
    </div>
  );
};

export default ContentForm;
