
import React, { useState } from 'react';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Eye, ExternalLink, ThumbsUp, MessageCircle, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// No mock data - will fetch real user posts from API

const UserPostList = () => {
  const [userPosts, setUserPosts] = useState<any[]>([]);

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-center">Engagement</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {userPosts.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                No posts found. Start creating LinkedIn content to see your posts here.
              </TableCell>
            </TableRow>
          ) : (
            userPosts.map((post) => (
            <TableRow key={post.id}>
              <TableCell className="font-medium">{post.title}</TableCell>
              <TableCell>{post.date}</TableCell>
              <TableCell>
                <Badge 
                  variant="outline" 
                  className={
                    post.status === "Published" 
                      ? "border-green-500 text-green-600 bg-green-50" 
                      : "border-amber-500 text-amber-600 bg-amber-50"
                  }
                >
                  {post.status}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex justify-center items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <ThumbsUp className="h-3.5 w-3.5 text-enterprise-blue" />
                    <span className="text-sm">{post.likes}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageCircle className="h-3.5 w-3.5 text-enterprise-teal" />
                    <span className="text-sm">{post.comments}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="h-3.5 w-3.5 text-enterprise-gray-500" />
                    <span className="text-sm">{post.views}</span>
                  </div>
                </div>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Options</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open in LinkedIn
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <ThumbsUp className="h-4 w-4 mr-2" />
                      View Engagement
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default UserPostList;
