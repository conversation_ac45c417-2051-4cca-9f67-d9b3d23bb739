
import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Palette, Check } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface ColorConfig {
  name: string;
  value: string;
  label: string;
}

interface BrandColorEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  colors: ColorConfig[];
  onSave: (colors: ColorConfig[]) => void;
}

const BrandColorEditor = ({ open, onOpenChange, colors: initialColors, onSave }: BrandColorEditorProps) => {
  const [colors, setColors] = useState<ColorConfig[]>(initialColors);
  const { toast } = useToast();
  
  const handleColorChange = (index: number, value: string) => {
    const newColors = [...colors];
    newColors[index] = { ...newColors[index], value };
    setColors(newColors);
  };

  const handleSave = () => {
    onSave(colors);
    toast({
      title: "Colors updated",
      description: "Your brand colors have been updated successfully.",
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Edit Brand Colors
          </DialogTitle>
          <DialogDescription>
            Customize your brand colors to match your company's visual identity.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          {colors.map((color, index) => (
            <div key={color.name} className="grid grid-cols-[1fr_120px] gap-4 items-center">
              <div>
                <Label htmlFor={`color-${index}`} className="text-sm font-medium">
                  {color.label}
                </Label>
                <div className="text-xs text-muted-foreground">{color.name}</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <div 
                    className="w-8 h-8 rounded-md border cursor-pointer"
                    style={{ backgroundColor: color.value }}
                    onClick={() => {
                      // This creates a click event on the hidden color input
                      document.getElementById(`color-picker-${index}`)?.click();
                    }}
                  />
                  <input 
                    type="color"
                    id={`color-picker-${index}`}
                    value={color.value}
                    onChange={(e) => handleColorChange(index, e.target.value)}
                    className="opacity-0 absolute inset-0 w-8 h-8 cursor-pointer"
                  />
                </div>
                <Input
                  id={`color-${index}`}
                  type="text"
                  value={color.value}
                  onChange={(e) => handleColorChange(index, e.target.value)}
                  className="h-9"
                />
              </div>
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSave}
            className="bg-enterprise-blue hover:bg-enterprise-blue/90"
          >
            <Check className="mr-2 h-4 w-4" />
            Save Colors
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BrandColorEditor;
