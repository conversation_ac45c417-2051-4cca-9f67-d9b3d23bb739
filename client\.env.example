# Frontend Environment Configuration Example
# Copy this file to .env and update the values

# API Configuration
VITE_API_BASE_URL=http://localhost:3000
VITE_CLIENT_URL=https://localhost:8080

# Phyllo Integration
VITE_PHYLLO_CLIENT_ID=your_phyllo_client_id_here
VITE_PHYLLO_CLIENT_SECRET=your_phyllo_client_secret_here
VITE_PHYLLO_API_BASE_URL=https://api.sandbox.getphyllo.com/v1

# Unipile Integration
VITE_UNIPILE_API_URL=https://api.unipile.com
VITE_UNIPILE_ACCOUNT_URL=https://account.unipile.com
VITE_UNIPILE_ACCESS_TOKEN=your_unipile_access_token_here

# Environment
VITE_NODE_ENV=development

# Production URLs (update for production deployment)
# VITE_API_BASE_URL=https://your-api-domain.com
# VITE_CLIENT_URL=https://your-frontend-domain.com
# VITE_NODE_ENV=production
