
import React from 'react';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Link, Youtube } from 'lucide-react';
import { Input } from "@/components/ui/input";
import { useContent } from '@/context/ContentContext';

const PostTypeSelector = () => {
  const { contentType, contentUrl, setContentType, setContentUrl } = useContent();

  // If the content type is null or unset, default to 'text'
  React.useEffect(() => {
    if (!contentType) {
      setContentType('text');
    }
  }, [contentType, setContentType]);

  return (
    <div className="space-y-4 animate-fade-in">
      <h3 className="text-sm font-medium mb-2">Add a link to your post (optional)</h3>
      <RadioGroup
        value={contentType}
        onValueChange={(value) => setContentType(value as 'text' | 'article' | 'youtube')}
        className="flex items-center space-x-6"
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="article" id="article" />
          <Label htmlFor="article" className="flex items-center gap-1 cursor-pointer">
            <Link className="h-4 w-4" />
            Article link
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="youtube" id="youtube" />
          <Label htmlFor="youtube" className="flex items-center gap-1 cursor-pointer">
            <Youtube className="h-4 w-4" />
            YouTube video
          </Label>
        </div>
      </RadioGroup>

      {(contentType === 'article' || contentType === 'youtube') && (
        <div className="space-y-2 animate-fade-in">
          <Label htmlFor="content-url">
            {contentType === 'youtube' ? 'YouTube Video URL' : 'Article URL'}
          </Label>
          <Input
            id="content-url"
            type="url"
            placeholder={contentType === 'youtube' ? 'Paste YouTube video URL here...' : 'Paste article URL here...'}
            value={contentUrl}
            onChange={(e) => setContentUrl(e.target.value)}
            className="bg-white/70 backdrop-blur-sm border border-gray-200"
          />
        </div>
      )}
    </div>
  );
};

export default PostTypeSelector;
