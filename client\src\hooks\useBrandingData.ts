
import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Json } from '@/integrations/supabase/types';

// Type definitions
export interface LinkedInPostExample {
  id: string;
  subject: string;
  content: string;
}

export interface ColorConfig {
  name: string;
  value: string;
  label: string;
}

export interface BrandingData {
  id?: string;
  company_id?: string;
  core_values: string[];
  tone_of_voice: string;
  company_description: string;
  target_audience: string;
  audience_action: string;
  dos_content: string;
  donts_content: string;
  hashtags: string;
  brand_colors: ColorConfig[];
  linkedin_examples: LinkedInPostExample[];
}

// Interface for database operations that matches Supabase's expected types
interface BrandingDataDB {
  id?: string;
  company_id: string;
  core_values?: string[];
  tone_of_voice?: string;
  company_description?: string;
  target_audience?: string;
  audience_action?: string;
  dos_content?: string;
  donts_content?: string;
  hashtags?: string;
  brand_colors?: Json;
  linkedin_examples?: Json;
}

interface UseBrandingDataProps {
  profileId: string | undefined;
}

export const useBrandingData = ({ profileId }: UseBrandingDataProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const initialLoadComplete = useRef(false);

  // Database record ID
  const [brandingId, setBrandingId] = useState<string | null>(null);
  
  // Core Values state
  const [coreValues, setCoreValues] = useState<string[]>([
    "Innovative", "Trustworthy", "Professional", "Customer-focused", "Expert"
  ]);
  
  // Text content states
  const [toneOfVoice, setToneOfVoice] = useState(
    "Our communication style is professional yet approachable. We use clear, concise language and avoid jargon when possible. We're authoritative but not condescending, and we focus on how our products and services benefit our customers."
  );
  const [companyDescription, setCompanyDescription] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [audienceAction, setAudienceAction] = useState('');
  
  // Content guidelines states
  const [dosContent, setDosContent] = useState("Use data and statistics to support claims. Emphasize customer benefits over features. Keep content concise and focused.");
  const [dontsContent, setDontsContent] = useState("Criticize competitors by name. Make claims that cannot be verified. Use slang or unprofessional language.");
  const [hashTags, setHashTags] = useState("#AcmeCorp, #Innovation, #EnterpriseSuccess, #TeamAcme, #TechLeader, #ProductName");
  
  // Visual branding states
  const [brandColors, setBrandColors] = useState<ColorConfig[]>([
    { name: "Primary Blue", value: "#2563eb", label: "Primary Blue" },
    { name: "Teal", value: "#0d9488", label: "Teal" },
    { name: "Dark Gray", value: "#1e293b", label: "Dark Gray" }
  ]);
  
  // LinkedIn examples state
  const [postExamples, setPostExamples] = useState<LinkedInPostExample[]>([
    {
      id: "1",
      subject: "Product Launch",
      content: "Excited to announce the launch of our new enterprise software solution! 🚀 After months of hard work, we're proud to unveil a product that will transform how businesses manage their operations. \n\nOur team has focused on creating an intuitive user experience while delivering powerful capabilities that address the most challenging problems our customers face.\n\n#ProductLaunch #Innovation"
    },
    {
      id: "2",
      subject: "Industry Insight",
      content: "The landscape of enterprise software is evolving faster than ever. As businesses adapt to remote and hybrid work environments, we're seeing a fundamental shift in what tools are needed to succeed.\n\nAt Acme, we believe the future belongs to companies that embrace flexibility, automation, and data-driven decision making.\n\nWhat changes are you seeing in your industry? Let's discuss below.\n\n#FutureOfWork #EnterpriseTools"
    }
  ]);

  // Auto-save timer ref to clear on unmount
  const autoSaveTimerRef = useRef<number | null>(null);

  // Function to track changes
  const setValueWithTracking = <T,>(setter: React.Dispatch<React.SetStateAction<T>>) => {
    return (value: T) => {
      if (initialLoadComplete.current) {
        console.log("Change detected, marking as unsaved");
        setHasUnsavedChanges(true);
      } else {
        console.log("Initial load still in progress, not marking as unsaved");
      }
      setter(value);
    };
  };

  // Create wrapped setters for all form fields
  const setTrackedCoreValues = setValueWithTracking(setCoreValues);
  const setTrackedToneOfVoice = setValueWithTracking(setToneOfVoice);
  const setTrackedCompanyDescription = setValueWithTracking(setCompanyDescription);
  const setTrackedTargetAudience = setValueWithTracking(setTargetAudience);
  const setTrackedAudienceAction = setValueWithTracking(setAudienceAction);
  const setTrackedDosContent = setValueWithTracking(setDosContent);
  const setTrackedDontsContent = setValueWithTracking(setDontsContent);
  const setTrackedHashTags = setValueWithTracking(setHashTags);
  const setTrackedBrandColors = setValueWithTracking(setBrandColors);
  const setTrackedPostExamples = setValueWithTracking(setPostExamples);

  // Function to fetch branding data from the database
  const fetchBrandingData = useCallback(async () => {
    if (!profileId) {
      console.log("Cannot fetch branding data: No profile ID");
      return;
    }
    
    try {
      setIsLoading(true);
      console.log("Fetching branding data for company ID:", profileId);
      
      const { data, error } = await supabase
        .from('company_branding')
        .select('*')
        .eq('company_id', profileId)
        .maybeSingle();
      
      if (error) {
        console.error('Error fetching branding data:', error);
        setDebugInfo(prev => prev + '\nFetch error: ' + JSON.stringify(error));
        toast({
          title: "Error loading branding data",
          description: error.message,
          variant: "destructive"
        });
        return;
      }
      
      console.log("Branding data retrieved:", data);
      setDebugInfo(prev => prev + '\nData retrieved: ' + JSON.stringify(data));
      
      if (data) {
        setBrandingId(data.id);
        setCoreValues(data.core_values || coreValues);
        setToneOfVoice(data.tone_of_voice || toneOfVoice);
        setCompanyDescription(data.company_description || companyDescription);
        setTargetAudience(data.target_audience || targetAudience);
        setAudienceAction(data.audience_action || audienceAction);
        setDosContent(data.dos_content || dosContent);
        setDontsContent(data.donts_content || dontsContent);
        setHashTags(data.hashtags || hashTags);
        
        // Parse JSON data with type checking
        if (data.brand_colors) {
          const parsedColors = data.brand_colors as unknown as ColorConfig[];
          if (Array.isArray(parsedColors)) {
            setBrandColors(parsedColors);
            console.log("Brand colors loaded:", parsedColors);
          }
        }
        
        // Parse JSON data with type checking
        if (data.linkedin_examples) {
          const parsedExamples = data.linkedin_examples as unknown as LinkedInPostExample[];
          if (Array.isArray(parsedExamples)) {
            setPostExamples(parsedExamples);
            console.log("LinkedIn examples loaded:", parsedExamples);
          }
        }
      }
      
      // Mark initial load as complete AFTER data is loaded
      console.log("Initial data load complete, enabling change tracking");
      initialLoadComplete.current = true;
      
    } catch (err) {
      console.error('Exception fetching branding data:', err);
      setDebugInfo(prev => prev + '\nFetch exception: ' + JSON.stringify(err));
    } finally {
      setIsLoading(false);
    }
  }, [profileId, toast]);

  // Save all branding data
  const handleSaveAll = useCallback(async (silent = false) => {
    if (!profileId) {
      console.error("Cannot save: No profile ID available");
      setDebugInfo(prev => prev + '\nSave error: No profile ID');
      if (!silent) {
        toast({
          title: "Authentication error",
          description: "You must be logged in to save branding settings.",
          variant: "destructive"
        });
      }
      return;
    }
    
    console.log("Auto-save triggered, current changes status:", hasUnsavedChanges);
    if (!hasUnsavedChanges) {
      console.log("No unsaved changes to save");
      return;
    }
    
    setIsSaving(true);
    console.log("Attempting to save branding data for company ID:", profileId);
    
    try {
      // Create a database-compatible object with proper JSON types
      const brandingDataDB: BrandingDataDB = {
        company_id: profileId,
        core_values: coreValues,
        tone_of_voice: toneOfVoice,
        company_description: companyDescription,
        target_audience: targetAudience,
        audience_action: audienceAction,
        dos_content: dosContent,
        donts_content: dontsContent,
        hashtags: hashTags,
        // Convert complex objects to JSON-compatible format
        brand_colors: brandColors as unknown as Json,
        linkedin_examples: postExamples as unknown as Json
      };
      
      console.log("Prepared data for saving:", brandingDataDB);
      setDebugInfo(prev => prev + '\nPrepared data: ' + JSON.stringify(brandingDataDB));
      
      let result;
      
      if (brandingId) {
        // Update existing record
        console.log(`Updating existing record with ID: ${brandingId}`);
        result = await supabase
          .from('company_branding')
          .update(brandingDataDB)
          .eq('id', brandingId);
      } else {
        // Insert new record
        console.log("Inserting new branding record");
        result = await supabase
          .from('company_branding')
          .insert([brandingDataDB]);
      }
      
      console.log("Supabase operation result:", result);
      setDebugInfo(prev => prev + '\nSave result: ' + JSON.stringify(result));
      
      const { error } = result;
      
      if (error) {
        throw error;
      }
      
      // If it was a new record, fetch the data again to get the ID
      if (!brandingId) {
        fetchBrandingData();
      }
      
      // Mark that changes are now saved
      setHasUnsavedChanges(false);
      
      if (!silent) {
        toast({
          title: "Branding settings saved",
          description: "Your company branding settings have been updated."
        });
      } else {
        console.log("Auto-save completed successfully");
      }
    } catch (error: any) {
      console.error('Error saving branding data:', error);
      setDebugInfo(prev => prev + '\nSave error: ' + JSON.stringify(error));
      if (!silent) {
        toast({
          title: "Save failed",
          description: error.message || "There was an error saving your branding settings.",
          variant: "destructive"
        });
      }
    } finally {
      setIsSaving(false);
    }
  }, [
    profileId, hasUnsavedChanges, toast, fetchBrandingData,
    coreValues, toneOfVoice, companyDescription, targetAudience,
    audienceAction, dosContent, dontsContent, hashTags,
    brandColors, postExamples, brandingId
  ]);

  // Effect to initialize the component and fetch data
  useEffect(() => {
    if (profileId) {
      console.log("Profile detected with ID:", profileId);
      fetchBrandingData();
    } else {
      console.log("No profile ID detected");
      setIsLoading(false);
    }
  }, [profileId, fetchBrandingData]);

  // Initialize periodic auto-save every 2 minutes
  useEffect(() => {
    // Set up a timer to auto-save every 2 minutes if there are changes
    autoSaveTimerRef.current = window.setInterval(() => {
      if (hasUnsavedChanges && initialLoadComplete.current) {
        console.log("Periodic auto-save triggered");
        handleSaveAll(true);
      }
    }, 120000); // 2 minutes

    // Clean up timer on unmount
    return () => {
      if (autoSaveTimerRef.current) {
        window.clearInterval(autoSaveTimerRef.current);
      }
    };
  }, [hasUnsavedChanges, handleSaveAll]);
  
  // Setup tab change listener
  useEffect(() => {
    // Only set up listeners after initial load is complete
    if (!initialLoadComplete.current) {
      return;
    }

    console.log("Setting up tab change listeners");
    
    // Listen for the custom tab change event from Settings.tsx
    const handleTabChangeEvent = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Tab change event detected:", customEvent.detail?.tab);
      
      if (hasUnsavedChanges && initialLoadComplete.current && profileId) {
        console.log("Auto-saving changes on tab change");
        handleSaveAll(true);
      }
    };
    
    // Listen for direct click events on tab triggers
    const handleTabClick = (event: Event) => {
      const target = event.target as HTMLElement;
      const tabTrigger = target.closest('[role="tab"]');
      
      if (tabTrigger) {
        console.log("Tab click detected on:", tabTrigger.getAttribute('data-value') || tabTrigger.textContent);
        
        if (hasUnsavedChanges && initialLoadComplete.current && profileId) {
          console.log("Auto-saving changes on tab click");
          handleSaveAll(true);
        }
      }
    };
    
    // Register event listeners
    document.addEventListener('settingsTabChange', handleTabChangeEvent);
    document.addEventListener('click', handleTabClick);
    
    // Also set up the MutationObserver for attribute changes
    const tabObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-state') {
          const target = mutation.target as HTMLElement;
          if (target.getAttribute('role') === 'tab' && target.getAttribute('data-state') === 'active') {
            console.log("Tab state change detected via MutationObserver");
            
            if (hasUnsavedChanges && initialLoadComplete.current && profileId) {
              console.log("Auto-saving changes on tab state change");
              handleSaveAll(true);
            }
          }
        }
      }
    });
    
    // Find all tab elements and observe them
    const tabs = document.querySelectorAll('[role="tab"]');
    tabs.forEach(tab => {
      tabObserver.observe(tab, { attributes: true });
    });
    
    // Clean up all observers and event listeners when component unmounts
    return () => {
      console.log("Cleaning up tab change listeners");
      document.removeEventListener('settingsTabChange', handleTabChangeEvent);
      document.removeEventListener('click', handleTabClick);
      tabObserver.disconnect();
    };
  }, [hasUnsavedChanges, profileId, handleSaveAll, initialLoadComplete]);

  return {
    isLoading,
    isSaving,
    debugInfo,
    hasUnsavedChanges,
    // State values
    coreValues,
    toneOfVoice,
    companyDescription,
    targetAudience,
    audienceAction,
    dosContent,
    dontsContent,
    hashTags,
    brandColors,
    postExamples,
    brandingId,
    // State setters with change tracking
    setCoreValues: setTrackedCoreValues,
    setToneOfVoice: setTrackedToneOfVoice,
    setCompanyDescription: setTrackedCompanyDescription,
    setTargetAudience: setTrackedTargetAudience,
    setAudienceAction: setTrackedAudienceAction,
    setDosContent: setTrackedDosContent,
    setDontsContent: setTrackedDontsContent,
    setHashTags: setTrackedHashTags,
    setBrandColors: setTrackedBrandColors,
    setPostExamples: setTrackedPostExamples,
    // Actions
    handleSaveAll,
    fetchBrandingData,
  };
};
